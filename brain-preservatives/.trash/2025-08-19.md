---
creation_date: 2025-08-19 12:52
modification_date: Tuesday 19th August 2025 12:52:32
type: daily
status: active
priority: medium
area_category: Personal
owner: Jordan
tags:
  - daily
  - 2025-08
date: 2025-08-19
day_of_week: Tuesday
week: 2025-W34
month: 2025-08
mood: ""
energy_level: 
weather: ""
location: ""
related_projects: 
related_areas: 
related_resources: 
related_people:
  - "[[Jordan]]"
task_priority: medium
task_context: admin
---

# 2025-08-19 - Tuesday

<< [[2025-08-18]] | [[2025-08-20]] >>

> **🔄 Template not processed?** Click here: [Process Template](obsidian://advanced-uri?vault=brain-preservatives&commandid=templater-obsidian%3Areplace-in-file-templater)

## Quick Capture
<!-- Use this section for quick thoughts that you'll organize later -->
-

## Tasks for Today
#todo #tasks #outstanding
### 📋 Carried Forward Tasks (7704 tasks from 22 days)
#### 🔥 Recent Tasks (Last 7 Days)
##### From 2025-08-18 (yesterday) - 3852 tasks
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact.

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact.

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact.

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact.

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian

##### From 2025-08-17 (2 days ago) - 1926 tasks
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact.

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact.

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian

##### From 2025-08-16 (3 days ago) - 963 tasks
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact.

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian

##### From 2025-08-13 (6 days ago) - 471 tasks
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact.

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian

##### From 2025-08-12 (7 days ago) - 246 tasks
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian

#### 📚 Older Tasks (246 tasks from 17 days)
<details><summary>Click to expand older tasks</summary>

##### From 2025-08-09 (10 days ago) - 123 tasks
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian

##### From 2025-08-08 (11 days ago) - 41 tasks
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian

##### From 2025-08-03 (16 days ago) - 41 tasks
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
- [ ] add the webssh key to dropbear
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian

##### From 2025-07-22 (28 days ago) - 2 tasks
- [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
- [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17

##### From 2025-07-02 (48 days ago) - 1 tasks

- [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder

##### From 2025-06-25 (55 days ago) - 1 tasks

- [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.

##### From 2025-06-24 (56 days ago) - 1 tasks
- [ ] add the webssh key to dropbear

##### From 2025-06-17 (63 days ago) - 5 tasks
- [ ] should i use setapp or buyout software licenses for mac?
- [ ] buy #software and add receipts to #tax:
	- [ ] swish
	- [ ] contexts
	- [ ] augment

##### From 2025-06-13 (67 days ago) - 2 tasks
- [ ] add all clippings to relevant sections in vault
- [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining

##### From 2025-06-12 (68 days ago) - 2 tasks
- [ ] Consider if #raycast pro is worth purchasing
- [ ] Add the #raycast pro features to a reference in 3-resources

##### From 2025-06-11 (69 days ago) - 1 tasks
- [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]

##### From 2025-06-10 (70 days ago) - 1 tasks
	  - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact. 

##### From 2025-06-08 (72 days ago) - 10 tasks

- [ ] (90/212) electricity ;
	- [ ] $212.00 due on the 06•06.25 - missed payment

- [ ] optus;
	- [ ] • $220.40 due on the 12•06-25

- [ ] fuel;
	- [ ] have already put in over $50.00 so far

- [ ] smokes;

- [ ] license renewal7[[]()]()

- [ ] groceries + %:

- [ ] $ currently after :

##### From 2025-06-03 (77 days ago) - 3 tasks
- [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
- [ ] sort all daily notes data into vault properly
- [ ] use augment ai trial 13 <NAME_EMAIL>

##### From 2025-04-16 (125 days ago) - 5 tasks
- [ ] Migrate Obsidian vault to PARA structure
- [ ] Add proper frontmatter to existing notes
- [ ] Tag notes appropriately for better organization
- [ ] Start linking related notes together
- [ ] Create tables of contents for each section

##### From 2025-04-15 (126 days ago) - 4 tasks
- [ ] Research PARA methodology for Obsidian vault organization
- [ ] Look into dataview plugin capabilities
- [ ] Explore templates for consistent note structure
- [ ] Plan vault reorganization

##### From 2025-04-14 (127 days ago) - 3 tasks
- [ ] Review current Obsidian vault organization
- [ ] Research best practices for note organization
- [ ] Look into productivity plugins for Obsidian

</details>

### ✨ New Tasks for Today

- [ ]
- [ ]
- [ ]

## CI/CD
<!-- Administrative tasks, correspondence, documentation -->
-

## Finance & Banking
<!-- Banking, donations, payments, reconciliation -->
-

## Maintainence
<!-- Compliance, training, documentation -->
-

## Development/Updates
<!-- Items requiring follow-up action -->
- [ ]
- [ ]

## Journal
<!-- How was your day? What happened? What did you learn? -->
-

## Notes
<!-- Any other notes or information -->
-

## 🎯 Today's Focus
<!-- Show only the most important tasks -->

### 🔥 Must Do Today
```dataview

TASK
FROM "1-Projects" OR "2-Areas" OR "0-Daily Notes"
WHERE !completed
  AND task_priority = "urgent"
  AND (due <= date("2025-08-19") OR contains(tags, "#today"))
SORT file.path ASC
LIMIT 3

```



### ⚡ High Impact Work
```dataview

TASK
FROM "1-Projects" OR "2-Areas"
WHERE !completed
  AND task_priority = "high"
  AND (due <= date("2025-08-19") + dur(2 days) OR !due)
SORT due ASC
LIMIT 5

```



### 📋 If Time Permits
```dataview

TASK
FROM "1-Projects" OR "2-Areas"
WHERE !completed
  AND task_priority = "medium"
  AND estimated_time <= "30min"
SORT estimated_time ASC
LIMIT 4

```



## 🚀 Active Project Status
```dataview

TABLE WITHOUT ID
  file.link as "🚀 Project",
  choice(priority = "critical", "🔥", choice(priority = "high", "⚡", "📋")) as "P",
  completion_percentage + "%" as "Done",
  choice(deadline < date("2025-08-19"), "⚠️ OVERDUE", choice(deadline <= date("2025-08-19") + dur(7 days), "🔜 Soon", "📅 " + string(deadline))) as "Deadline"
FROM "1-Projects"
WHERE status = "active"
  AND (file.mtime >= date("2025-08-19") - dur(3 days)
   OR deadline <= date("2025-08-19") + dur(14 days)
   OR priority = "critical"
   OR priority = "high")
SORT choice(deadline < date("2025-08-19"), 1, choice(priority = "critical", 2, 3)) ASC
LIMIT 5

```



## 📅 Today's Meetings
```dataview

TABLE WITHOUT ID
  file.link as "📅 Meeting",
  time as "Time",
  participants as "Participants",
  location as "Location"
FROM #meeting
WHERE date = date("2025-08-19")
SORT time ASC

```



## ⚠️ Overdue & Urgent
```dataview

TABLE WITHOUT ID
  "⚠️" as "",
  file.link as "OVERDUE TASKS",
  choice(task_priority = "urgent", "🔥", choice(task_priority = "high", "⚡", "📋")) as "P",
  due as "Was Due"
FROM "1-Projects" OR "2-Areas"
WHERE !completed
  AND due < date("2025-08-19")
SORT task_priority ASC, due ASC
LIMIT 8

```



## 🔄 Context-Based Quick Tasks
```dataview

TABLE WITHOUT ID
  choice(task_priority = "high", "⚡", choice(task_priority = "medium", "📋", "🔄")) as "P",
  file.link as "QUICK WINS (15min)",
  choice(energy_required, energy_required, "medium") as "Energy"
FROM "1-Projects" OR "2-Areas"
WHERE !completed
  AND estimated_time = "15min"
SORT task_priority ASC, energy_required DESC
LIMIT 6

```



## Recent Notes
```dataview
TABLE WITHOUT ID
  file.link as "Note",
  type as "Type",
  file.mtime as "Last Modified"
FROM -"Daily" AND -"Templates"
SORT file.mtime DESC
LIMIT 5
```

## Create New Note
- [[2025-08-19 Meeting|Create New Meeting]]
- [[2025-08-19 Task|Create New Task]]
- [[2025-08-19 Idea|Create New Idea]]

## Related
- [[Daily Notes TOC]]
- [[2025-08|Monthly Overview]]
- [[2025-W34|Weekly Overview]]
- [[Tasks]]
- [[Home]]
