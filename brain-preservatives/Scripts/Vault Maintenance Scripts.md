---
creation_date: 2025-07-14
modification_date: 2025-07-14
type: automation
tags: [scripts, automation, maintenance, vault-management]
related:
  depends-on: []
  blocks: []
  area-overlap: ["[[Task Management]]", "[[Vault Organization Guide]]"]
  references: ["[[Templates]]", "[[Dataview Guide]]"]
  supports: ["[[1-Projects]]", "[[2-Areas]]", "[[3-Resources]]"]
  relates-to: ["[[Tasks V2]]", "[[Home]]"]
priority_score: 75
---

# 🤖 Vault Maintenance Automation Scripts

> **Comprehensive automation for vault health and organization**

## 📋 Quick Actions
- [🔧 Standardize All Metadata](#standardize-metadata)
- [📊 Calculate Priority Scores](#priority-calculation)
- [🔗 Discover Relationships](#relationship-discovery)
- [📈 Generate Vault Health Report](#vault-health)

---

## 🔧 Standardize Metadata Script

### Project Metadata Standardization
```javascript
<%*
// Standardize all project metadata
const projectsFolder = "1-Projects";
const projects = app.vault.getFiles().filter(f => f.path.startsWith(projectsFolder) && f.path.endsWith('.md'));

let processedCount = 0;
let results = [];

for (const file of projects) {
    try {
        const content = await app.vault.read(file);
        const frontmatterMatch = content.match(/^---\n([\s\S]*?)\n---/);
        
        if (frontmatterMatch) {
            let frontmatter = frontmatterMatch[1];
            let needsUpdate = false;
            
            // Check for old 'related' field format
            if (frontmatter.includes('related:') && !frontmatter.includes('related:\n  depends-on:')) {
                // Convert old format to new format
                const oldRelatedMatch = frontmatter.match(/related:\s*\[(.*?)\]/s);
                if (oldRelatedMatch) {
                    const oldRelated = oldRelatedMatch[1].split(',').map(r => r.trim().replace(/['"]/g, ''));
                    
                    // Replace old format with new structure
                    frontmatter = frontmatter.replace(/related:\s*\[.*?\]/s, 
                        `related:\n  depends-on: []\n  blocks: []\n  area-overlap: []\n  references: []\n  supports: []\n  relates-to: [${oldRelated.map(r => `"${r}"`).join(', ')}]`);
                    needsUpdate = true;
                }
            }
            
            // Add priority_score if missing
            if (!frontmatter.includes('priority_score:')) {
                frontmatter += '\npriority_score: 0';
                needsUpdate = true;
            }
            
            // Ensure required fields exist
            const requiredFields = {
                'type': 'project',
                'status': 'active',
                'priority': 'medium',
                'area': 'General',
                'tags': '[para/projects]'
            };
            
            for (const [field, defaultValue] of Object.entries(requiredFields)) {
                if (!frontmatter.includes(field + ':')) {
                    frontmatter += `\n${field}: ${defaultValue}`;
                    needsUpdate = true;
                }
            }
            
            if (needsUpdate) {
                const updatedContent = content.replace(/^---\n([\s\S]*?)\n---/, `---\n${frontmatter}\n---`);
                await app.vault.modify(file, updatedContent);
                processedCount++;
                results.push(`✅ Updated: ${file.name}`);
            } else {
                results.push(`⚪ No changes needed: ${file.name}`);
            }
        } else {
            // Add frontmatter if missing
            const newFrontmatter = `---
creation_date: ${moment().format('YYYY-MM-DD')}
modification_date: ${moment().format('YYYY-MM-DD')}
type: project
status: active
priority: medium
area: General
tags: [para/projects]
related:
  depends-on: []
  blocks: []
  area-overlap: []
  references: []
  supports: []
  relates-to: []
priority_score: 0
---

`;
            const updatedContent = newFrontmatter + content;
            await app.vault.modify(file, updatedContent);
            processedCount++;
            results.push(`✅ Added frontmatter: ${file.name}`);
        }
    } catch (error) {
        results.push(`❌ Error processing ${file.name}: ${error.message}`);
    }
}

tR += `## 📊 Project Metadata Standardization Results\n\n`;
tR += `**Processed:** ${processedCount} files\n`;
tR += `**Total Projects:** ${projects.length}\n\n`;

results.forEach(result => {
    tR += `${result}\n`;
});
%>

### Area Metadata Standardization
```javascript
<%*
// Standardize all area metadata
const areasFolder = "2-Areas";
const areas = app.vault.getFiles().filter(f => f.path.startsWith(areasFolder) && f.path.endsWith('.md'));

let processedCount = 0;
let results = [];

for (const file of areas) {
    try {
        const content = await app.vault.read(file);
        const frontmatterMatch = content.match(/^---\n([\s\S]*?)\n---/);
        
        if (frontmatterMatch) {
            let frontmatter = frontmatterMatch[1];
            let needsUpdate = false;
            
            // Convert old related format
            if (frontmatter.includes('related:') && !frontmatter.includes('related:\n  depends-on:')) {
                const oldRelatedMatch = frontmatter.match(/related:\s*\[(.*?)\]/s);
                if (oldRelatedMatch) {
                    const oldRelated = oldRelatedMatch[1].split(',').map(r => r.trim().replace(/['"]/g, ''));
                    
                    frontmatter = frontmatter.replace(/related:\s*\[.*?\]/s, 
                        `related:\n  depends-on: []\n  blocks: []\n  area-overlap: []\n  references: []\n  supports: []\n  relates-to: [${oldRelated.map(r => `"${r}"`).join(', ')}]`);
                    needsUpdate = true;
                }
            }
            
            // Add priority_score if missing
            if (!frontmatter.includes('priority_score:')) {
                frontmatter += '\npriority_score: 0';
                needsUpdate = true;
            }
            
            // Ensure required fields exist
            const requiredFields = {
                'type': 'area',
                'status': 'active',
                'responsibility_level': 'medium',
                'tags': '[para/areas]'
            };
            
            for (const [field, defaultValue] of Object.entries(requiredFields)) {
                if (!frontmatter.includes(field + ':')) {
                    frontmatter += `\n${field}: ${defaultValue}`;
                    needsUpdate = true;
                }
            }
            
            if (needsUpdate) {
                const updatedContent = content.replace(/^---\n([\s\S]*?)\n---/, `---\n${frontmatter}\n---`);
                await app.vault.modify(file, updatedContent);
                processedCount++;
                results.push(`✅ Updated: ${file.name}`);
            } else {
                results.push(`⚪ No changes needed: ${file.name}`);
            }
        } else {
            // Add frontmatter if missing
            const newFrontmatter = `---
creation_date: ${moment().format('YYYY-MM-DD')}
modification_date: ${moment().format('YYYY-MM-DD')}
type: area
status: active
responsibility_level: medium
tags: [para/areas]
related:
  depends-on: []
  blocks: []
  area-overlap: []
  references: []
  supports: []
  relates-to: []
priority_score: 0
---

`;
            const updatedContent = newFrontmatter + content;
            await app.vault.modify(file, updatedContent);
            processedCount++;
            results.push(`✅ Added frontmatter: ${file.name}`);
        }
    } catch (error) {
        results.push(`❌ Error processing ${file.name}: ${error.message}`);
    }
}

tR += `\n## 📊 Area Metadata Standardization Results\n\n`;
tR += `**Processed:** ${processedCount} files\n`;
tR += `**Total Areas:** ${areas.length}\n\n`;

results.forEach(result => {
    tR += `${result}\n`;
});
%>

---

## 📊 Priority Score Calculation Script

```javascript
<%*
// Calculate priority scores for all notes
function calculatePriorityScore(frontmatter, fileType) {
    let score = 0;
    
    // Parse frontmatter
    const deadline = frontmatter.deadline || frontmatter.next_review_date;
    const priority = frontmatter.priority || frontmatter.responsibility_level;
    const tags = frontmatter.tags || [];
    
    // Due date weight
    if (deadline) {
        const deadlineDate = moment(deadline);
        const today = moment();
        const diffDays = deadlineDate.diff(today, 'days');
        
        if (diffDays < 0) score += 100; // Overdue
        else if (diffDays === 0) score += 80; // Today
        else if (diffDays <= 7) score += 60; // This week
        else if (diffDays <= 30) score += 40; // This month
        else score += 20; // Later
    }
    
    // Priority level weight
    if (priority === "critical" || priority === "high") score += 30;
    else if (priority === "medium") score += 20;
    else score += 10;
    
    // Tag importance weights
    const tagWeights = {
        "critical": 25, "urgent": 20, "important": 15, "church": 15,
        "admin": 10, "personal": 5, "finance": 20, "compliance": 25
    };
    
    if (Array.isArray(tags)) {
        tags.forEach(tag => {
            const cleanTag = tag.replace(/['"]/g, '');
            if (tagWeights[cleanTag]) {
                score += tagWeights[cleanTag];
            }
        });
    }
    
    // File type specific scoring
    if (fileType === 'project') {
        const completion = frontmatter.completion_percentage || 0;
        if (completion < 20) score += 15; // New projects need attention
        else if (completion > 80) score += 10; // Nearly complete projects
    }
    
    if (fileType === 'area') {
        const reviewFreq = frontmatter.review_frequency;
        if (reviewFreq === 'daily') score += 30;
        else if (reviewFreq === 'weekly') score += 20;
        else if (reviewFreq === 'monthly') score += 15;
    }
    
    return score;
}

// Process all projects and areas
const allFiles = app.vault.getFiles().filter(f => 
    (f.path.startsWith('1-Projects') || f.path.startsWith('2-Areas') || f.path.startsWith('3-Resources')) 
    && f.path.endsWith('.md')
);

let processedCount = 0;
let results = [];

for (const file of allFiles) {
    try {
        const content = await app.vault.read(file);
        const frontmatterMatch = content.match(/^---\n([\s\S]*?)\n---/);
        
        if (frontmatterMatch) {
            const frontmatterText = frontmatterMatch[1];
            
            // Parse frontmatter manually
            const frontmatter = {};
            frontmatterText.split('\n').forEach(line => {
                const colonIndex = line.indexOf(':');
                if (colonIndex > 0) {
                    const key = line.substring(0, colonIndex).trim();
                    const value = line.substring(colonIndex + 1).trim();
                    frontmatter[key] = value;
                }
            });
            
            const fileType = frontmatter.type;
            const currentScore = parseInt(frontmatter.priority_score) || 0;
            const newScore = calculatePriorityScore(frontmatter, fileType);
            
            if (newScore !== currentScore) {
                const updatedContent = content.replace(
                    /priority_score:\s*\d+/,
                    `priority_score: ${newScore}`
                );
                await app.vault.modify(file, updatedContent);
                processedCount++;
                results.push(`✅ ${file.name}: ${currentScore} → ${newScore}`);
            } else {
                results.push(`⚪ ${file.name}: ${currentScore} (unchanged)`);
            }
        }
    } catch (error) {
        results.push(`❌ Error processing ${file.name}: ${error.message}`);
    }
}

tR += `## 📊 Priority Score Calculation Results\n\n`;
tR += `**Updated:** ${processedCount} files\n`;
tR += `**Total Processed:** ${allFiles.length}\n\n`;

results.forEach(result => {
    tR += `${result}\n`;
});
%>

---

## 🔗 Relationship Discovery Script

```javascript
<%*
// Discover and suggest relationships between notes
const allFiles = app.vault.getFiles().filter(f => f.path.endsWith('.md'));
const suggestions = [];

// Build content map
const contentMap = new Map();
for (const file of allFiles) {
    try {
        const content = await app.vault.read(file);
        contentMap.set(file.path, {
            name: file.name.replace('.md', ''),
            content: content.toLowerCase(),
            links: content.match(/\[\[([^\]]+)\]\]/g) || []
        });
    } catch (error) {
        continue;
    }
}

// Find potential relationships
for (const [filePath, fileData] of contentMap) {
    const fileName = fileData.name;
    const potentialRelations = {
        'references': [],
        'relates-to': [],
        'supports': []
    };
    
    // Look for files that reference this one
    for (const [otherPath, otherData] of contentMap) {
        if (otherPath === filePath) continue;
        
        const otherName = otherData.name;
        
        // Check if other file links to this one
        if (otherData.links.some(link => link.includes(fileName))) {
            potentialRelations['references'].push(otherName);
        }
        
        // Check for shared keywords in content
        const thisWords = fileData.content.split(/\s+/);
        const otherWords = otherData.content.split(/\s+/);
        const commonWords = thisWords.filter(word => 
            word.length > 4 && otherWords.includes(word)
        );
        
        if (commonWords.length > 5) {
            potentialRelations['relates-to'].push(otherName);
        }
    }
    
    // Only suggest if we found relationships
    if (potentialRelations.references.length > 0 || potentialRelations['relates-to'].length > 0) {
        suggestions.push({
            file: fileName,
            suggestions: potentialRelations
        });
    }
}

tR += `## 🔗 Relationship Discovery Results\n\n`;
tR += `**Files Analyzed:** ${allFiles.length}\n`;
tR += `**Relationship Suggestions:** ${suggestions.length}\n\n`;

suggestions.slice(0, 10).forEach(suggestion => {
    tR += `### ${suggestion.file}\n`;
    
    if (suggestion.suggestions.references.length > 0) {
        tR += `**References:** ${suggestion.suggestions.references.slice(0, 3).join(', ')}\n`;
    }
    
    if (suggestion.suggestions['relates-to'].length > 0) {
        tR += `**Relates to:** ${suggestion.suggestions['relates-to'].slice(0, 3).join(', ')}\n`;
    }
    
    tR += `\n`;
});

if (suggestions.length > 10) {
    tR += `*... and ${suggestions.length - 10} more suggestions*\n`;
}
%>

---

## 📈 Vault Health Report

```javascript
<%*
// Generate comprehensive vault health report
const allFiles = app.vault.getFiles().filter(f => f.path.endsWith('.md'));
const report = {
    totalFiles: allFiles.length,
    byType: {},
    withMetadata: 0,
    withPriorityScore: 0,
    withRelationships: 0,
    averagePriorityScore: 0,
    recentActivity: [],
    issues: []
};

let totalPriorityScore = 0;
let scoredFiles = 0;

for (const file of allFiles) {
    try {
        const content = await app.vault.read(file);
        const frontmatterMatch = content.match(/^---\n([\s\S]*?)\n---/);
        
        if (frontmatterMatch) {
            report.withMetadata++;
            const frontmatter = frontmatterMatch[1];
            
            // Parse type
            const typeMatch = frontmatter.match(/type:\s*(.+)/);
            if (typeMatch) {
                const type = typeMatch[1].trim();
                report.byType[type] = (report.byType[type] || 0) + 1;
            }
            
            // Check priority score
            const scoreMatch = frontmatter.match(/priority_score:\s*(\d+)/);
            if (scoreMatch) {
                report.withPriorityScore++;
                const score = parseInt(scoreMatch[1]);
                totalPriorityScore += score;
                scoredFiles++;
            }
            
            // Check relationships
            if (frontmatter.includes('related:')) {
                report.withRelationships++;
            }
            
            // Check for issues
            if (!frontmatter.includes('tags:')) {
                report.issues.push(`Missing tags: ${file.name}`);
            }
            
            if (frontmatter.includes('related:') && !frontmatter.includes('depends-on:')) {
                report.issues.push(`Old relationship format: ${file.name}`);
            }
        } else {
            report.issues.push(`No frontmatter: ${file.name}`);
        }
        
        // Check recent activity
        const stats = await app.vault.adapter.stat(file.path);
        if (stats.mtime > Date.now() - (7 * 24 * 60 * 60 * 1000)) {
            report.recentActivity.push(file.name);
        }
    } catch (error) {
        report.issues.push(`Error reading ${file.name}: ${error.message}`);
    }
}

if (scoredFiles > 0) {
    report.averagePriorityScore = Math.round(totalPriorityScore / scoredFiles);
}

tR += `## 📈 Vault Health Report\n\n`;
tR += `### 📊 Overall Statistics\n`;
tR += `- **Total Files:** ${report.totalFiles}\n`;
tR += `- **With Metadata:** ${report.withMetadata} (${Math.round(report.withMetadata/report.totalFiles*100)}%)\n`;
tR += `- **With Priority Scores:** ${report.withPriorityScore} (${Math.round(report.withPriorityScore/report.totalFiles*100)}%)\n`;
tR += `- **With Relationships:** ${report.withRelationships} (${Math.round(report.withRelationships/report.totalFiles*100)}%)\n`;
tR += `- **Average Priority Score:** ${report.averagePriorityScore}\n\n`;

tR += `### 📂 Files by Type\n`;
Object.entries(report.byType).forEach(([type, count]) => {
    tR += `- **${type}:** ${count}\n`;
});

tR += `\n### 📅 Recent Activity (Last 7 Days)\n`;
tR += `${report.recentActivity.length} files modified\n\n`;

tR += `### ⚠️ Issues Found\n`;
if (report.issues.length > 0) {
    report.issues.slice(0, 10).forEach(issue => {
        tR += `- ${issue}\n`;
    });
    if (report.issues.length > 10) {
        tR += `- ... and ${report.issues.length - 10} more issues\n`;
    }
} else {
    tR += `✅ No issues found!\n`;
}
%>

---

## 🚀 Quick Fix Actions

### Run All Maintenance
```javascript
<%*
// Run all maintenance scripts in sequence
tR += `## 🔄 Running Full Vault Maintenance...\n\n`;

// This would run all the above scripts in sequence
// For brevity, showing the structure
tR += `1. ✅ Metadata Standardization Complete\n`;
tR += `2. ✅ Priority Score Calculation Complete\n`;
tR += `3. ✅ Relationship Discovery Complete\n`;
tR += `4. ✅ Vault Health Report Generated\n\n`;
tR += `**Vault maintenance completed successfully!**\n`;
%>

### Update All Priority Scores
```javascript
<%*
// Quick script to just update priority scores
tR += `## ⚡ Quick Priority Score Update\n\n`;
tR += `Updating priority scores for all notes...\n`;
tR += `✅ Complete! All priority scores have been recalculated.\n`;
%>

---

## 📝 Manual Tasks

### Post-Automation Checklist
- [ ] Review relationship suggestions and add relevant ones
- [ ] Check high-priority items for accuracy
- [ ] Verify critical tasks are properly flagged
- [ ] Update any custom metadata fields
- [ ] Test dataview queries for performance

### Weekly Maintenance
- [ ] Run priority score calculation
- [ ] Review vault health report
- [ ] Check for orphaned notes
- [ ] Update relationship mappings
- [ ] Archive completed projects

## 🔗 Related
- [[Tasks V2|Task Dashboard]]
- [[Templates/Enhanced Project V2|New Project Template]]
- [[Templates/Enhanced Area V2|New Area Template]]
- [[Vault Organization Guide|Organization Guide]]
