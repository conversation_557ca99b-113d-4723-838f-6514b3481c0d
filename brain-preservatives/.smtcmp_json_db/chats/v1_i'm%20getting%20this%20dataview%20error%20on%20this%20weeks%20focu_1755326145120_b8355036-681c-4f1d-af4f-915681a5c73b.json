{"id": "b8355036-681c-4f1d-af4f-915681a5c73b", "title": "i'm getting this dataview error on this weeks focu", "messages": [{"role": "user", "content": {"root": {"children": [{"children": [{"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "i'm getting this dataview error on this weeks focus dataview in the current file:", "type": "text", "version": 1}, {"type": "linebreak", "version": 1}, {"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "Dataview: Error: ", "type": "text", "version": 1}, {"type": "linebreak", "version": 1}, {"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "-- PARSING FAILED --------------------------------------------------", "type": "text", "version": 1}, {"type": "linebreak", "version": 1}, {"type": "linebreak", "version": 1}, {"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "  5 |   choice(estimated_time, estimated_time, \"?\") as \"Time\",", "type": "text", "version": 1}, {"type": "linebreak", "version": 1}, {"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "  6 |   choice(deadline, deadline, \"Flexible\") as \"Due\"", "type": "text", "version": 1}, {"type": "linebreak", "version": 1}, {"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "> 7 | FROM this.file.folder OR \"1-Projects\"", "type": "text", "version": 1}, {"type": "linebreak", "version": 1}, {"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "    |      ^", "type": "text", "version": 1}, {"type": "linebreak", "version": 1}, {"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "  8 | WHERE !completed", "type": "text", "version": 1}, {"type": "linebreak", "version": 1}, {"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "  9 |   AND contains(related_areas, \"Stellar XLM Holdings - Transactions\")", "type": "text", "version": 1}, {"type": "linebreak", "version": 1}, {"type": "linebreak", "version": 1}, {"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "Expected one of the following: ", "type": "text", "version": 1}, {"type": "linebreak", "version": 1}, {"type": "linebreak", "version": 1}, {"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "'!', '(', '-', 'csv(', 'outgoing(', file link, string, tag ('#hello/stuff')", "type": "text", "version": 1}, {"type": "linebreak", "version": 1}, {"type": "linebreak", "version": 1}, {"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "im also getting this error on routine maintanence dataview in the same file:", "type": "text", "version": 1}, {"type": "linebreak", "version": 1}, {"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "Dataview: Error: ", "type": "text", "version": 1}, {"type": "linebreak", "version": 1}, {"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "-- PARSING FAILED --------------------------------------------------", "type": "text", "version": 1}, {"type": "linebreak", "version": 1}, {"type": "linebreak", "version": 1}, {"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "  4 |   file.link as \"Routine Tasks\",", "type": "text", "version": 1}, {"type": "linebreak", "version": 1}, {"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "  5 |   estimated_time as \"Time\"", "type": "text", "version": 1}, {"type": "linebreak", "version": 1}, {"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "> 6 | FROM this.file.folder", "type": "text", "version": 1}, {"type": "linebreak", "version": 1}, {"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "    |      ^", "type": "text", "version": 1}, {"type": "linebreak", "version": 1}, {"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "  7 | WHERE !completed", "type": "text", "version": 1}, {"type": "linebreak", "version": 1}, {"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "  8 |   AND task_priority = \"routine\"", "type": "text", "version": 1}, {"type": "linebreak", "version": 1}, {"type": "linebreak", "version": 1}, {"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "Expected one of the following: ", "type": "text", "version": 1}, {"type": "linebreak", "version": 1}, {"type": "linebreak", "version": 1}, {"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "'!', '(', '-', 'csv(', 'outgoing(', file link, string, tag ('#hello/stuff')", "type": "text", "version": 1}], "direction": "ltr", "format": "", "indent": 0, "type": "paragraph", "version": 1, "textFormat": 0, "textStyle": ""}], "direction": "ltr", "format": "", "indent": 0, "type": "root", "version": 1}}, "promptContent": [{"type": "text", "text": "\n\ni'm getting this dataview error on this weeks focus dataview in the current file:\nDataview: Error: \n-- PARSING FAILED --------------------------------------------------\n\n  5 |   choice(estimated_time, estimated_time, \"?\") as \"Time\",\n  6 |   choice(deadline, deadline, \"Flexible\") as \"Due\"\n> 7 | FROM this.file.folder OR \"1-Projects\"\n    |      ^\n  8 | WHERE !completed\n  9 |   AND contains(related_areas, \"Stellar XLM Holdings - Transactions\")\n\nExpected one of the following: \n\n'!', '(', '-', 'csv(', 'outgoing(', file link, string, tag ('#hello/stuff')\n\nim also getting this error on routine maintanence dataview in the same file:\nDataview: Error: \n-- PARSING FAILED --------------------------------------------------\n\n  4 |   file.link as \"Routine Tasks\",\n  5 |   estimated_time as \"Time\"\n> 6 | FROM this.file.folder\n    |      ^\n  7 | WHERE !completed\n  8 |   AND task_priority = \"routine\"\n\nExpected one of the following: \n\n'!', '(', '-', 'csv(', 'outgoing(', file link, string, tag ('#hello/stuff')\n\n"}], "id": "e5bef55c-6947-4fa8-86a6-b7bfa59ad488", "mentionables": [{"type": "current-file", "file": "2-Areas/Finances/Cryptocurrency-Trading/Stellar XLM Holdings - Transactions.md"}]}], "createdAt": 1755326145120, "updatedAt": 1755326145120, "schemaVersion": 1}