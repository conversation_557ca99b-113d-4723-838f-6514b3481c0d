---
# CORE METADATA
creation_date: 2025-07-14
modification_date: 2025-07-14
type: index
status: active
priority: high
area_category: Administration
owner: Jordan
tags: [tasks, active, index, navigation]

# RELATIONSHIPS (Enhanced System)
related_areas: ["[[Task Management]]", "[[Administration]]"]
related_resources: ["[[Task Management System]]"]
related_people: ["[[Jordan]]"]

# TASK MANAGEMENT
task_priority: high
task_context: admin
---

# 📂 Active Tasks Index

> **Central index of all active tasks in the system** - Your comprehensive view of current work.

---

## 📊 Active Tasks Overview

### Summary Statistics
```dataview
TABLE WITHOUT ID
  "📊 Active Tasks Summary" as "Category",
  length(filter(rows, (r) => r.task_priority = "urgent")) as "🔥 Urgent",
  length(filter(rows, (r) => r.task_priority = "high")) as "⚡ High",
  length(filter(rows, (r) => r.task_priority = "medium")) as "📋 Medium",
  length(filter(rows, (r) => r.task_priority = "routine")) as "🔄 Routine",
  length(rows) as "Total"
FROM "0.2-Tasks/Active"
WHERE type = "task" AND !completed
GROUP BY "summary"
```

---

## 🔥 URGENT TASKS - Immediate Action Required

```dataview
TABLE WITHOUT ID
  file.link as "🔥 URGENT TASK",
  choice(estimated_time, estimated_time, "?") as "Time",
  choice(due_date, due_date, "ASAP") as "Due",
  choice(source_note, source_note, "Direct") as "Source",
  task_context as "Context"
FROM "0.2-Tasks/Active"
WHERE !completed 
  AND task_priority = "urgent"
SORT created_date ASC
```

---

## ⚡ HIGH PRIORITY TASKS - Important Work

```dataview
TABLE WITHOUT ID
  file.link as "⚡ HIGH PRIORITY TASK",
  choice(estimated_time, estimated_time, "?") as "Time",
  choice(due_date, due_date, "Soon") as "Due",
  choice(source_note, source_note, "Direct") as "Source",
  task_context as "Context"
FROM "0.2-Tasks/Active"
WHERE !completed 
  AND task_priority = "high"
SORT due_date ASC, created_date ASC
```

---

## 📋 MEDIUM PRIORITY TASKS - Regular Work

```dataview
TABLE WITHOUT ID
  file.link as "📋 MEDIUM PRIORITY TASK",
  choice(estimated_time, estimated_time, "?") as "Time",
  choice(due_date, due_date, "Flexible") as "Due",
  choice(source_note, source_note, "Direct") as "Source",
  task_context as "Context"
FROM "0.2-Tasks/Active"
WHERE !completed 
  AND task_priority = "medium"
SORT due_date ASC, created_date ASC
LIMIT 20
```

---

## 🔄 ROUTINE TASKS - Maintenance & Admin

```dataview
TABLE WITHOUT ID
  file.link as "🔄 ROUTINE TASK",
  choice(estimated_time, estimated_time, "?") as "Time",
  choice(source_note, source_note, "Direct") as "Source",
  task_context as "Context"
FROM "0.2-Tasks/Active"
WHERE !completed 
  AND task_priority = "routine"
SORT estimated_time ASC, created_date ASC
LIMIT 15
```

---

## 💡 SOMEDAY/MAYBE TASKS - Future Ideas

```dataview
TABLE WITHOUT ID
  file.link as "💡 SOMEDAY/MAYBE",
  task_context as "Context",
  choice(source_note, source_note, "Direct") as "Source",
  created_date as "Added"
FROM "0.2-Tasks/Active"
WHERE !completed 
  AND task_priority = "someday"
SORT created_date DESC
LIMIT 10
```

---

## 📅 TASKS BY DUE DATE

### Overdue Tasks
```dataview
TABLE WITHOUT ID
  "⚠️" as "",
  file.link as "OVERDUE TASK",
  choice(task_priority = "urgent", "🔥", choice(task_priority = "high", "⚡", "📋")) as "Priority",
  due_date as "Was Due",
  round((date(today) - due_date) / dur(1 day)) + " days" as "Overdue By"
FROM "0.2-Tasks/Active"
WHERE !completed 
  AND due_date < date(today)
SORT due_date ASC
```

### Due This Week
```dataview
TABLE WITHOUT ID
  choice(task_priority = "urgent", "🔥", choice(task_priority = "high", "⚡", "📋")) as "P",
  file.link as "DUE THIS WEEK",
  due_date as "Due Date",
  choice(estimated_time, estimated_time, "?") as "Time"
FROM "0.2-Tasks/Active"
WHERE !completed 
  AND due_date >= date(today)
  AND due_date <= date(today) + dur(7 days)
SORT due_date ASC, task_priority ASC
```

### Due Next Week
```dataview
TABLE WITHOUT ID
  choice(task_priority = "urgent", "🔥", choice(task_priority = "high", "⚡", "📋")) as "P",
  file.link as "DUE NEXT WEEK",
  due_date as "Due Date",
  choice(estimated_time, estimated_time, "?") as "Time"
FROM "0.2-Tasks/Active"
WHERE !completed 
  AND due_date > date(today) + dur(7 days)
  AND due_date <= date(today) + dur(14 days)
SORT due_date ASC, task_priority ASC
```

---

## 🏷️ TASKS BY CONTEXT

### Deep Work Tasks (High Energy)
```dataview
TABLE WITHOUT ID
  file.link as "🧠 DEEP WORK TASK",
  choice(task_priority = "urgent", "🔥", choice(task_priority = "high", "⚡", "📋")) as "P",
  choice(estimated_time, estimated_time, "?") as "Time",
  choice(due_date, due_date, "Flexible") as "Due"
FROM "0.2-Tasks/Active"
WHERE !completed 
  AND task_context = "deep-work"
SORT task_priority ASC, estimated_time ASC
```

### Admin Tasks (Low Energy)
```dataview
TABLE WITHOUT ID
  file.link as "🪫 ADMIN TASK",
  choice(task_priority = "urgent", "🔥", choice(task_priority = "high", "⚡", "📋")) as "P",
  choice(estimated_time, estimated_time, "?") as "Time",
  choice(source_note, source_note, "Direct") as "Source"
FROM "0.2-Tasks/Active"
WHERE !completed 
  AND task_context = "admin"
SORT estimated_time ASC, task_priority ASC
```

### Communication Tasks
```dataview
TABLE WITHOUT ID
  file.link as "📞 COMMUNICATION TASK",
  choice(task_priority = "urgent", "🔥", choice(task_priority = "high", "⚡", "📋")) as "P",
  choice(estimated_time, estimated_time, "?") as "Time",
  choice(due_date, due_date, "Flexible") as "Due"
FROM "0.2-Tasks/Active"
WHERE !completed 
  AND task_context = "communication"
SORT task_priority ASC, due_date ASC
```

---

## 📊 TASKS BY SOURCE

### Tasks from Daily Notes
```dataview
TABLE WITHOUT ID
  file.link as "📅 TASK FROM DAILY NOTE",
  source_note as "Original Daily Note",
  source_date as "Note Date",
  choice(task_priority = "urgent", "🔥", choice(task_priority = "high", "⚡", "📋")) as "P"
FROM "0.2-Tasks/Active"
WHERE !completed 
  AND source_note
SORT source_date DESC, task_priority ASC
LIMIT 15
```

### Direct Tasks (Created in Task System)
```dataview
TABLE WITHOUT ID
  file.link as "📝 DIRECT TASK",
  choice(task_priority = "urgent", "🔥", choice(task_priority = "high", "⚡", "📋")) as "P",
  choice(estimated_time, estimated_time, "?") as "Time",
  created_date as "Created"
FROM "0.2-Tasks/Active"
WHERE !completed 
  AND !source_note
SORT created_date DESC, task_priority ASC
LIMIT 10
```

---

## 🔗 TASKS BY PROJECT/AREA

### Project-Related Tasks
```dataview
TABLE WITHOUT ID
  file.link as "🚀 PROJECT TASK",
  choice(related_projects[0], related_projects[0], "No Project") as "Project",
  choice(task_priority = "urgent", "🔥", choice(task_priority = "high", "⚡", "📋")) as "P",
  choice(due_date, due_date, "Flexible") as "Due"
FROM "0.2-Tasks/Active"
WHERE !completed 
  AND related_projects
SORT related_projects[0] ASC, task_priority ASC
```

### Area-Related Tasks
```dataview
TABLE WITHOUT ID
  file.link as "📋 AREA TASK",
  choice(related_areas[0], related_areas[0], "No Area") as "Area",
  choice(task_priority = "urgent", "🔥", choice(task_priority = "high", "⚡", "📋")) as "P",
  choice(due_date, due_date, "Flexible") as "Due"
FROM "0.2-Tasks/Active"
WHERE !completed 
  AND related_areas
SORT related_areas[0] ASC, task_priority ASC
```

---

## 🚀 Quick Actions

### Create New Tasks
- [[0.2-Tasks/Active/New Task|➕ Create New Task]]
- [[Templates/Enhanced Task|📋 Create from Template]]

### Maintenance Actions
- [[0.2-Tasks/Daily Task Sync|🔄 Run Daily Sync]]
- [[0.2-Tasks/Automation/Archive Completed|📚 Archive Completed]]
- [[0.2-Tasks/Automation/Update Priorities|⚡ Update Priorities]]

### Navigation
- [[0.2-Tasks/Tasks Dashboard|🎯 Tasks Dashboard]]
- [[0.2-Tasks/Tasks TOC|📋 Tasks TOC]]
- [[0.2-Tasks/Completed/Completed Tasks Index|✅ Completed Tasks]]

---

**Last Updated**: 2025-07-14 | **Total Active Tasks**: `$= dv.pages('"0.2-Tasks/Active"').where(p => p.type === "task" && !p.completed).length`

**Navigation**: [[0.2-Tasks/Tasks TOC|📋 Tasks TOC]] | [[0.2-Tasks/Tasks Dashboard|🎯 Dashboard]] | [[Home|🏠 Home]]
