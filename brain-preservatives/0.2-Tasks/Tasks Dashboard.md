---
# CORE METADATA
creation_date: 2025-08-07
modification_date: 2025-08-07
type: task-dashboard
status: active
priority: critical
area_category: Administration
owner: Jordan
tags: [tasks, dashboard, productivity, central-hub]

# RELATIONSHIPS (Enhanced System)
related_areas: ["[[2-Areas/Task Management]]", "[[2-Areas/Website-Maintenance]]", "[[2-Areas/Church Administration]]"]
related_resources: ["[[3-Resources/Vault Organization Guide]]", "[[3-Resources/Software Development Resources]]"]
related_people: ["[[Jordan]]"]

# TASK MANAGEMENT
task_priority: critical
task_context: admin
---

# 🎯 Tasks Dashboard - Central Command

> **The central hub for all task management across the vault** - Your single source of truth for what needs to be done.

## 🔥 URGENT - Do Immediately
```dataview
TABLE WITHOUT ID
  "🔥" as "",
  file.link as "URGENT TASK",
  choice(estimated_time, estimated_time, "?") as "Time",
  choice(due_date, due_date, "ASAP") as "Due",
  source_note as "From"
FROM "0.2-Tasks/Active"
WHERE !completed 
  AND task_priority = "urgent"
SORT created_date ASC
LIMIT 8
```

## ⚡ HIGH PRIORITY - Important Work
```dataview
TABLE WITHOUT ID
  "⚡" as "",
  file.link as "HIGH PRIORITY TASK",
  choice(estimated_time, estimated_time, "?") as "Time",
  choice(due_date, due_date, "Soon") as "Due",
  source_note as "From"
FROM "0.2-Tasks/Active"
WHERE !completed 
  AND task_priority = "high"
SORT due_date ASC, created_date ASC
LIMIT 12
```

## ⚠️ OVERDUE - Needs Immediate Attention
```dataview
TABLE WITHOUT ID
  "⚠️" as "",
  file.link as "OVERDUE TASK",
  choice(task_priority = "urgent", "🔥", choice(task_priority = "high", "⚡", "📋")) as "P",
  due_date as "Was Due",
  source_note as "From"
FROM "0.2-Tasks/Active"
WHERE !completed 
  AND due_date < date(today)
SORT task_priority ASC, due_date ASC
LIMIT 10
```

## 📅 DUE TODAY
```dataview
TABLE WITHOUT ID
  choice(task_priority = "urgent", "🔥", choice(task_priority = "high", "⚡", "📋")) as "P",
  file.link as "DUE TODAY",
  choice(estimated_time, estimated_time, "?") as "Time",
  source_note as "From"
FROM "0.2-Tasks/Active"
WHERE !completed 
  AND due_date = date(today)
SORT task_priority ASC
```

## 📋 MEDIUM PRIORITY - Regular Work
```dataview
TABLE WITHOUT ID
  "📋" as "",
  file.link as "MEDIUM PRIORITY",
  choice(estimated_time, estimated_time, "?") as "Time",
  choice(due_date, due_date, "Flexible") as "Due",
  source_note as "From"
FROM "0.2-Tasks/Active"
WHERE !completed 
  AND task_priority = "medium"
  AND (due_date <= date(today) + dur(7 days) OR !due_date)
SORT due_date ASC, created_date ASC
LIMIT 15
```

## 🔋 CONTEXT-BASED TASK BATCHING

### 🧠 Deep Work (High Energy Required)
```dataview
TABLE WITHOUT ID
  "🔋" as "",
  file.link as "DEEP WORK TASK",
  estimated_time as "Time",
  choice(due_date, due_date, "Flexible") as "Due"
FROM "0.2-Tasks/Active"
WHERE !completed 
  AND energy_required = "high"
  AND task_context = "deep-work"
  AND (task_priority = "high" OR task_priority = "medium")
SORT task_priority ASC, estimated_time ASC
LIMIT 6
```

### 🪫 Admin Tasks (Low Energy)
```dataview
TABLE WITHOUT ID
  "🪫" as "",
  file.link as "ADMIN TASK",
  estimated_time as "Time",
  source_note as "From"
FROM "0.2-Tasks/Active"
WHERE !completed 
  AND energy_required = "low"
  AND task_context = "admin"
SORT estimated_time ASC
LIMIT 10
```

### ⏱️ Quick Wins (15 minutes or less)
```dataview
TABLE WITHOUT ID
  choice(task_priority = "high", "⚡", choice(task_priority = "medium", "📋", "🔄")) as "P",
  file.link as "QUICK WIN",
  choice(energy_required, energy_required, "medium") as "Energy",
  source_note as "From"
FROM "0.2-Tasks/Active"
WHERE !completed 
  AND estimated_time = "15min"
SORT task_priority ASC, energy_required DESC
LIMIT 8
```

## 📊 TASK ANALYTICS

### This Week's Progress
```dataview
TABLE WITHOUT ID
  "📊 This Week" as "Period",
  length(filter(rows, (r) => r.completed_date >= date(today) - dur(7 days))) as "Completed",
  length(filter(rows, (r) => r.task_priority = "high" AND r.completed_date >= date(today) - dur(7 days))) as "High Priority",
  length(filter(rows, (r) => !r.completed)) as "Remaining"
FROM "0.2-Tasks"
WHERE type = "task"
GROUP BY "week"
```

### Task Sources Analysis
```dataview
TABLE WITHOUT ID
  source_note as "Source",
  length(rows) as "Total Tasks",
  length(filter(rows, (r) => r.completed)) as "Completed",
  round(length(filter(rows, (r) => r.completed)) / length(rows) * 100, 1) + "%" as "Success Rate"
FROM "0.2-Tasks/Active"
WHERE type = "task"
GROUP BY source_note
SORT "Success Rate" DESC
```

## 🔄 MAINTENANCE & ROUTINE

### Routine Tasks
```dataview
TABLE WITHOUT ID
  "🔄" as "",
  file.link as "ROUTINE TASK",
  estimated_time as "Time",
  source_note as "From"
FROM "0.2-Tasks/Active"
WHERE !completed 
  AND task_priority = "routine"
SORT estimated_time ASC
LIMIT 8
```

### Someday/Maybe Tasks
```dataview
TABLE WITHOUT ID
  "💡" as "",
  file.link as "SOMEDAY/MAYBE",
  task_context as "Context",
  source_note as "From"
FROM "0.2-Tasks/Active"
WHERE !completed 
  AND (task_priority = "someday" OR !due_date)
SORT task_context ASC
LIMIT 10
```

## 📈 RECENTLY COMPLETED
```dataview
TABLE WITHOUT ID
  "✅" as "",
  file.link as "COMPLETED TASK",
  task_priority as "Priority",
  completed_date as "Done",
  source_note as "From"
FROM "0.2-Tasks/Completed"
WHERE completed_date >= date(today) - dur(7 days)
SORT completed_date DESC
LIMIT 10
```

## 🚀 Quick Actions
- [[Templates/Enhanced Task|➕ Create New Task]]
- [[Templates/Enhanced Project V2|📋 Create New Project]]
- [[0.2-Tasks/Task Automation|⚙️ Task Automation]]
- [[0.2-Tasks/Completed/Completed Tasks Index|📚 View Completed Tasks]]

## 🔗 Navigation
- [[0.2-Tasks/Tasks TOC|📋 Tasks Table of Contents]]
- [[0.2-Tasks/Active/Active Tasks Index|📂 Active Tasks]]
- [[0.2-Tasks/Completed/Completed Tasks Index|✅ Completed Tasks]]
- [[Tasks|📊 Original Tasks View]]
- [[Home|🏠 Home]]

---

**Last Updated**: 2025-08-07 | **Total Active Tasks**: `$= dv.pages('"0.2-Tasks/Active"').where(p => p.type === "task" && !p.completed).length`
