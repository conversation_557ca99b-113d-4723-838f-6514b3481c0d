---
# CORE METADATA
creation_date: 2025-07-14
modification_date: 2025-07-14
type: automation
status: active
priority: critical
area_category: Administration
owner: Jordan
tags: [tasks, automation, daily-sync, carryover]

# RELATIONSHIPS (Enhanced System)
related_areas: ["[[Task Management]]", "[[Administration]]"]
related_resources: ["[[Vault Organization System Guide]]", "[[Daily Notes Sync System]]"]
related_people: ["[[Jordan]]"]

# TASK MANAGEMENT
task_priority: critical
task_context: admin
---

# 🔄 Daily Task Sync - Automated Carryover System

> **Automated system that carries over incomplete tasks from daily notes while preserving the original archive**

---

## 🎯 System Overview

This system automatically:
1. **Scans daily notes** for incomplete tasks
2. **Creates task copies** in the 0.2-Tasks system
3. **Preserves original tasks** in daily notes (archive integrity)
4. **Links tasks** to their source daily notes
5. **Prevents duplicates** through intelligent tracking

---

## 📋 Current Unfinished Tasks from Daily Notes

### 🔥 Recent Unfinished Tasks (Last 7 Days)
```dataview
TABLE WITHOUT ID
  "📅" as "",
  file.link as "Daily Note",
  date as "Date",
  length(filter(file.tasks, (t) => !t.completed)) as "Open Tasks"
FROM "0-Daily Notes"
WHERE file.tasks
  AND length(filter(file.tasks, (t) => !t.completed)) > 0
  AND date >= date(today) - dur(7 days)
SORT date DESC
LIMIT 10
```

### 📚 Older Unfinished Tasks (8+ Days Ago)
```dataview
TABLE WITHOUT ID
  "⏰" as "",
  file.link as "Daily Note",
  date as "Date",
  length(filter(file.tasks, (t) => !t.completed)) as "Open Tasks",
  round((date(today) - date) / dur(1 day)) + " days ago" as "Age"
FROM "0-Daily Notes"
WHERE file.tasks
  AND length(filter(file.tasks, (t) => !t.completed)) > 0
  AND date < date(today) - dur(7 days)
SORT date DESC
LIMIT 15
```

---

## 🔄 Task Carryover Process

### Step 1: Daily Note Task Detection
```dataview
TASK
FROM "0-Daily Notes"
WHERE !completed
  AND file.name != "Daily Notes TOC"
  AND file.name != "Daily Note Folder. Must be cleaned Daily"
SORT file.name DESC
LIMIT 20
```

### Step 2: Task Analysis & Categorization
**Automatic categorization based on content:**
- **🔥 Urgent**: Contains keywords like "urgent", "ASAP", "emergency", "critical"
- **⚡ High**: Contains project names, deadlines, "important"
- **📋 Medium**: Regular tasks without special indicators
- **🔄 Routine**: Contains "routine", "maintenance", "cleanup"

### Step 3: Task Migration Rules
1. **Preserve Original**: Never modify tasks in daily notes
2. **Create Copy**: Generate new task file in `0.2-Tasks/Active/`
3. **Add Metadata**: Include source note, creation date, priority
4. **Link Back**: Maintain connection to original daily note
5. **Prevent Duplicates**: Check if task already exists in system

---

## 📝 Task Creation Format

### For New Tasks in Daily Notes
When adding tasks to daily notes, use this format for better automation:

```markdown
## Tasks for Today
- [ ] [Priority] Task description #context #project-tag
- [ ] [HIGH] Review YendorCats deployment #deep-work #yendorcats
- [ ] [URGENT] Fix server backup issue #admin #maintenance
- [ ] [MEDIUM] Update documentation #writing #projects
- [ ] [ROUTINE] Clean email inbox #admin #communication
```

### Priority Indicators
- **[URGENT]** or **[🔥]** → `task_priority: urgent`
- **[HIGH]** or **[⚡]** → `task_priority: high`
- **[MEDIUM]** or **[📋]** → `task_priority: medium`
- **[ROUTINE]** or **[🔄]** → `task_priority: routine`
- **[SOMEDAY]** or **[💡]** → `task_priority: someday`

### Context Tags
- **#deep-work** → Requires focused concentration
- **#admin** → Administrative/bureaucratic tasks
- **#communication** → Emails, calls, meetings
- **#maintenance** → System upkeep, routine checks
- **#creative** → Design, writing, brainstorming

### Project/Area Tags
- **#yendorcats** → Links to YendorCats project
- **#church** → Church administration tasks
- **#personal** → Personal tasks
- **#university** → University-related tasks

---

## 🤖 Automation Workflow

### Daily Sync Process (Manual Trigger)
```markdown
1. **Scan Daily Notes**: Check last 30 days for incomplete tasks
2. **Extract Task Data**: Parse priority, context, and content
3. **Check Duplicates**: Verify task doesn't already exist
4. **Create Task File**: Generate new task in 0.2-Tasks/Active/
5. **Update Index**: Add to active tasks index
6. **Generate Report**: Show what was processed
```

### Task File Naming Convention
```
YYYY-MM-DD-TaskNumber-SourceNote.md
Example: 2025-07-14-001-2025-06-25.md
```

### Automated Metadata Generation
```yaml
---
# CORE METADATA
creation_date: 2025-07-14
modification_date: 2025-07-14
type: task
status: not-started
priority: high
area_category: [Auto-detected from tags]
owner: Jordan
tags: [task, daily-carryover, [context-tags]]

# TASK MANAGEMENT
task_priority: [Extracted from daily note]
task_context: [Extracted from hashtags]
estimated_time: [Auto-estimated based on content]
energy_required: [Auto-assigned based on context]
due_date: [Extracted if present]

# RELATIONSHIPS (Enhanced System)
related_projects: [["[[Auto-detected from tags]]"]]
related_areas: [["[[Auto-detected from context]]"]]
source_note: "[[Original Daily Note]]"
source_date: 2025-06-25
task_number: 001

# TASK LIFECYCLE
created_date: 2025-07-14
migrated_from: "0-Daily Notes/2025-06-25.md"
original_task_line: 30
---
```

---

## 🔗 Linking Strategy

### Vault Connectivity Rules
1. **Project Links**: Tasks automatically link to related projects
2. **Area Links**: Tasks connect to relevant responsibility areas
3. **Resource Links**: Tasks link to helpful resources/guides
4. **People Links**: Tasks connect to responsible/involved people

### Example Linking Pattern
```markdown
# Task: Update YendorCats Documentation

## Related Vault Content
- **Project**: [[YendorCats Project Documentation]]
- **Area**: [[Software-Development]]
- **Resources**: 
  - [[YendorCats Deployment Guide]]
  - [[Documentation Best Practices]]
- **People**: [[Jordan]]
- **Source**: [[2025-06-25]] (Daily Note)

## Context Links
- **Similar Tasks**: Other documentation tasks
- **Dependencies**: [[YendorCats Testing]] must be complete
- **Blockers**: None currently
```

---

## 📊 Sync Statistics

### Today's Sync Status
```dataview
TABLE WITHOUT ID
  "📊 Sync Status" as "Metric",
  length(filter(pages('"0.2-Tasks/Active"'), (p) => p.migrated_from AND p.created_date = date(today))) as "Tasks Migrated Today",
  length(filter(pages('"0-Daily Notes"'), (p) => p.file.tasks AND length(filter(p.file.tasks, (t) => !t.completed)) > 0)) as "Daily Notes with Open Tasks",
  length(filter(pages('"0.2-Tasks/Active"'), (p) => p.source_note))) as "Total Migrated Tasks"
FROM ""
WHERE file.name = "Daily Task Sync"
LIMIT 1
```

### Migration History
```dataview
TABLE WITHOUT ID
  created_date as "Migration Date",
  length(rows) as "Tasks Migrated",
  list(distinct(source_date)) as "Source Dates"
FROM "0.2-Tasks/Active"
WHERE migrated_from
GROUP BY created_date
SORT created_date DESC
LIMIT 7
```

---

## 🚀 Manual Sync Actions

### Run Daily Sync
1. **[[0.2-Tasks/Automation/Run Daily Sync|🔄 Execute Daily Sync]]**
2. **[[0.2-Tasks/Automation/Sync Report|📊 View Last Sync Report]]**
3. **[[0.2-Tasks/Automation/Cleanup Duplicates|🧹 Remove Duplicates]]**

### Maintenance
- **[[0.2-Tasks/Automation/Archive Old Tasks|📚 Archive Completed Tasks]]**
- **[[0.2-Tasks/Automation/Update Links|🔗 Update Broken Links]]**
- **[[0.2-Tasks/Automation/Validate Metadata|✅ Validate Task Metadata]]**

---

## 🔧 Configuration

### Sync Settings
- **Scan Period**: Last 30 days of daily notes
- **Duplicate Check**: Compare task content similarity
- **Auto-Priority**: Assign based on keywords and context
- **Link Generation**: Automatic based on tags and content

### Exclusion Rules
- Skip tasks marked with `#no-sync`
- Ignore completed tasks
- Skip tasks already migrated (duplicate prevention)
- Exclude template files and TOC files

---

**Navigation**: [[0.2-Tasks/Tasks TOC|📋 Tasks TOC]] | [[0.2-Tasks/Tasks Dashboard|🎯 Dashboard]] | [[Home|🏠 Home]]
