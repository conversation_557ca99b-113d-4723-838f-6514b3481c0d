---
creation_date: 2025-09-01 23:26
modification_date: Monday 1st September 2025 23:26:53
type: daily
status: active
priority: medium
area_category: Personal
owner: Jordan
tags:
  - daily
  - 2025-09
date: 2025-09-01
day_of_week: Monday
week: 2025-W36
month: 2025-09
mood: ""
energy_level: 
weather: ""
location: ""
related_projects: 
related_areas: 
related_resources: 
related_people:
  - "[[Jordan]]"
task_priority: medium
task_context: admin
---

# 2025-09-01 - Monday

<< [[2025-08-31]] | [[2025-09-02]] >>

> **🔄 Template not processed?** Click here: [Process Template](obsidian://advanced-uri?vault=brain-preservatives&commandid=templater-obsidian%3Areplace-in-file-templater)

## Quick Capture
<!-- Use this section for quick thoughts that you'll organize later -->
-

## Tasks for Today
#todo #tasks #outstanding
### 📋 Carried Forward Tasks
> **51 tasks from 20 days** - Click to expand/collapse

> [!info]- 📋 Carried Forward Tasks (51 total)
> 
> #### 📚 Older Tasks (44 tasks from 16 days)
> 
> **From 2025-08-22** (10 days ago) - 1 tasks:
> 
> - [ ] New Princess <PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON>@paceyspace.com
> 
> **From 2025-08-19** (13 days ago) - 2 tasks:
> 
> - [ ] On thursday, pay for daisydisk to manage disk space [[DaisyDisk, the most popular disk space analyzer]]
> - [ ] Watch "Foundation" a new scifi tv series (2021-?)
> 
> **From 2025-07-22** (41 days ago) - 2 tasks:
> 
> - [ ] #task test 📅 2025-07-22 ➕ 2025-07-17
> - [ ] #task testing 📅 2025-07-22 ➕ 2025-07-17
> 
> **From 2025-07-02** (61 days ago) - 1 tasks:
> 
> - [ ] Implement the PaceSpace Wiki obsidian Templates which are located inthe brain-preservatives template folder
> 
> **From 2025-06-25** (68 days ago) - 1 tasks:
> 
> - [ ] Remove from Cloudflare: The error message indicates that a domain registered through Cloudflare's registrar cannot be deleted directly from the Cloudflare dashboard. To delete such a domain, you need to either ==cancel the domain registration or transfer it to another registrar==. Contacting Cloudflare support is recommended to guide you through the appropriate steps for your situation.
> 
> **From 2025-06-24** (69 days ago) - 1 tasks:
> 
> - [ ] add the webssh key to dropbear
> 
> **From 2025-06-17** (76 days ago) - 5 tasks:
> 
> - [ ] should i use setapp or buyout software licenses for mac?
> - [ ] buy #software and add receipts to #tax:
> - [ ] swish
> - [ ] contexts
> - [ ] augment
> 
> **From 2025-06-13** (80 days ago) - 2 tasks:
> 
> - [ ] add all clippings to relevant sections in vault
> - [ ] review the comittee nomination form as it may be relevant to the safe church policy intertwining
> 
> **From 2025-06-12** (81 days ago) - 2 tasks:
> 
> - [ ] Consider if #raycast pro is worth purchasing
> - [ ] Add the #raycast pro features to a reference in 3-resources
> 
> **From 2025-06-11** (82 days ago) - 1 tasks:
> 
> - [ ] book apple genius bar appoint  [repeat:: every day]  [start:: 2025-06-16]  [scheduled:: 2025-06-15]  [due:: 2025-06-19]
> 
> **From 2025-06-10** (83 days ago) - 1 tasks:
> 
> - [ ] Today, I have come to the realisation that all my #tasks are not being completed because all the tasks that are being actioned roughly 1-2 hours after the fact. with many of them being permanently deferred or put off until the tasks problem has inflicted it's full impact.
> 
> **From 2025-06-08** (85 days ago) - 10 tasks:
> 
> - [ ] (90/212) electricity ;
> - [ ] $212.00 due on the 06•06.25 - missed payment
> - [ ] optus;
> - [ ] • $220.40 due on the 12•06-25
> - [ ] fuel;
> - [ ] have already put in over $50.00 so far
> - [ ] smokes;
> - [ ] license renewal7[[]()]()
> - [ ] groceries + %:
> - [ ] $ currently after :
> 
> **From 2025-06-03** (90 days ago) - 3 tasks:
> 
> - [ ] create a shortcut and alias to auto add and commit obsidian notes. one for daily notes only and another with arguement as commit message.
> - [ ] sort all daily notes data into vault properly
> - [ ] use augment ai trial 13 <NAME_EMAIL>
> 
> **From 2025-04-16** (138 days ago) - 5 tasks:
> 
> - [ ] Migrate Obsidian vault to PARA structure
> - [ ] Add proper frontmatter to existing notes
> - [ ] Tag notes appropriately for better organization
> - [ ] Start linking related notes together
> - [ ] Create tables of contents for each section
> 
> **From 2025-04-15** (139 days ago) - 4 tasks:
> 
> - [ ] Research PARA methodology for Obsidian vault organization
> - [ ] Look into dataview plugin capabilities
> - [ ] Explore templates for consistent note structure
> - [ ] Plan vault reorganization
> 
> **From 2025-04-14** (140 days ago) - 3 tasks:
> 
> - [ ] Review current Obsidian vault organization
> - [ ] Research best practices for note organization
> - [ ] Look into productivity plugins for Obsidian
> 

### ✨ New Tasks for Today

- [ ]
- [ ]
- [ ]

## CI/CD
<!-- Administrative tasks, correspondence, documentation -->
-

## Finance & Banking
<!-- Banking, donations, payments, reconciliation -->
-

## Maintainence
<!-- Compliance, training, documentation -->
-

## Development/Updates
<!-- Items requiring follow-up action -->
- [ ]
- [ ]

## Journal
<!-- How was your day? What happened? What did you learn? -->
-

## Notes
<!-- Any other notes or information -->
-

## 🎯 Today's Focus
<!-- Show only the most important tasks -->

### 🔥 Must Do Today
```dataview

TASK
FROM "1-Projects" OR "2-Areas" OR "0-Daily Notes"
WHERE !completed
  AND task_priority = "urgent"
  AND (due <= date("2025-09-01") OR contains(tags, "#today"))
SORT file.path ASC
LIMIT 3

```



### ⚡ High Impact Work
```dataview

TASK
FROM "1-Projects" OR "2-Areas"
WHERE !completed
  AND task_priority = "high"
  AND (due <= date("2025-09-01") + dur(2 days) OR !due)
SORT due ASC
LIMIT 5

```



### 📋 If Time Permits
```dataview

TASK
FROM "1-Projects" OR "2-Areas"
WHERE !completed
  AND task_priority = "medium"
  AND estimated_time <= "30min"
SORT estimated_time ASC
LIMIT 4

```



## 🚀 Active Project Status
```dataview

TABLE WITHOUT ID
  file.link as "🚀 Project",
  choice(priority = "critical", "🔥", choice(priority = "high", "⚡", "📋")) as "P",
  completion_percentage + "%" as "Done",
  choice(deadline < date("2025-09-01"), "⚠️ OVERDUE", choice(deadline <= date("2025-09-01") + dur(7 days), "🔜 Soon", "📅 " + string(deadline))) as "Deadline"
FROM "1-Projects"
WHERE status = "active"
  AND (file.mtime >= date("2025-09-01") - dur(3 days)
   OR deadline <= date("2025-09-01") + dur(14 days)
   OR priority = "critical"
   OR priority = "high")
SORT choice(deadline < date("2025-09-01"), 1, choice(priority = "critical", 2, 3)) ASC
LIMIT 5

```



## 📅 Today's Meetings
```dataview

TABLE WITHOUT ID
  file.link as "📅 Meeting",
  time as "Time",
  participants as "Participants",
  location as "Location"
FROM #meeting
WHERE date = date("2025-09-01")
SORT time ASC

```



## ⚠️ Overdue & Urgent
```dataview

TABLE WITHOUT ID
  "⚠️" as "",
  file.link as "OVERDUE TASKS",
  choice(task_priority = "urgent", "🔥", choice(task_priority = "high", "⚡", "📋")) as "P",
  due as "Was Due"
FROM "1-Projects" OR "2-Areas"
WHERE !completed
  AND due < date("2025-09-01")
SORT task_priority ASC, due ASC
LIMIT 8

```



## 🔄 Context-Based Quick Tasks
```dataview

TABLE WITHOUT ID
  choice(task_priority = "high", "⚡", choice(task_priority = "medium", "📋", "🔄")) as "P",
  file.link as "QUICK WINS (15min)",
  choice(energy_required, energy_required, "medium") as "Energy"
FROM "1-Projects" OR "2-Areas"
WHERE !completed
  AND estimated_time = "15min"
SORT task_priority ASC, energy_required DESC
LIMIT 6

```



## Recent Notes
```dataview
TABLE WITHOUT ID
  file.link as "Note",
  type as "Type",
  file.mtime as "Last Modified"
FROM -"Daily" AND -"Templates"
SORT file.mtime DESC
LIMIT 5
```

## Create New Note
- [[2025-09-01 Meeting|Create New Meeting]]
- [[2025-09-01 Task|Create New Task]]
- [[2025-09-01 Idea|Create New Idea]]

## Related
- [[Daily Notes TOC]]
- [[2025-09|Monthly Overview]]
- [[2025-W36|Weekly Overview]]
- [[Tasks]]
- [[Home]]
