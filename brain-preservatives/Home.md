---
creation_date: <% tp.date.now("YYYY-MM-DD") %>
modification_date: <% tp.date.now("YYYY-MM-DD") %>
type: dashboard
aliases: [Home, Dashboard, MOC]
tags: [dashboard, index]
---

# Home Dashboard

## PARA System
- 📋 [[1-Projects]] - Time-limited goals with defined outcomes
- 🔄 [[2-Areas]] - Ongoing responsibilities to maintain
- 📚 [[3-Resources]] - Information and tools for reference
- 🗄️ [[4-Archive]] - Completed or inactive items

## Tables of Contents
- **[[0.2-Tasks/Tasks TOC|📋 Tasks TOC]]** - Complete task management system
- 📑 [[Projects TOC]] - Comprehensive index of all projects
- 📑 [[Areas TOC]] - Comprehensive index of all areas
- 📑 [[Resources TOC]] - Comprehensive index of all resources
- 📑 [[Daily Notes TOC]] - Index of all daily notes
- 📑 [[People TOC]] - Index of all people and contacts

## Category Tables of Contents
- 📑 [[Network TOC]] - Index of all network-related resources
- 📑 [[CRUCA-Church-Vault/3-Resources/Church 1/Church TOC]] - Index of all church-related resources
- 📑 [[University TOC]] - Index of all university-related resources
- 📑 [[Family TOC]] - Index of all family-related resources
- 📑 [[Prompt Engineering TOC]] - Index of all prompt engineering resources

## 🎯 Task Management
- **[[0.2-Tasks/Tasks Dashboard|🎯 Enhanced Tasks Dashboard]]** - Priority-based task command center
- **[[0.2-Tasks/Daily Task Sync|🔄 Daily Task Sync]]** - Automated carryover from daily notes
- **[[0.2-Tasks/Active/Active Tasks Index|📂 Active Tasks]]** - All current tasks with smart filtering
- ✅ [[Tasks]] - Original task management system

## Quick Actions
- 🔍 [[Search]] - Advanced search options
- 📝 [[Templates/Daily Note]] - Today's note
- 🆕 [[New Note]]
- **[[0.2-Tasks/Active/New Task|➕ Create New Task]]** - Quick task creation

## Recent Notes
```dataview
TABLE
  type as "Type",
  file.mtime as "Last Modified"
FROM "1-Projects" OR "2-Areas" OR "3-Resources" OR "Daily"
SORT file.mtime DESC
LIMIT 5
```

## Current Focus Projects
- 🔄 [[MCP Server Project]] - Setting up a centralized MCP server for unified context across development environments

## Active Projects
```dataview
TABLE
  status as "Status",
  priority as "Priority"
FROM "1-Projects"
WHERE status = "active" OR !contains(status, "completed")
SORT priority ASC
LIMIT 5
```

## Software Development
```dataview
LIST
FROM "1-Projects" OR "3-Resources"
WHERE contains(tags, "software-dev") OR contains(tags, "programming")
LIMIT 5
```

## Administration
```dataview
LIST
FROM "1-Projects" OR "2-Areas"
WHERE contains(tags, "admin") OR contains(tags, "administration")
LIMIT 5
```

## Family
```dataview
LIST
FROM "1-Projects/Family" OR "2-Areas/Family" OR "3-Resources/Family"
SORT file.name ASC
LIMIT 5
```

## Prompt Engineering
```dataview
LIST
FROM "3-Resources/Prompt Engineering"
SORT file.name ASC
LIMIT 5
```

## Quick Links
- [[Tags MOC]] - Map of content by tags
- [[People MOC]] - People and contacts
- [[Vault Organization Guide]] - Guide to using the vault
- [[README]] - Vault information and instructions