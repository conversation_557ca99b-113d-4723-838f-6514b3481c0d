---
# CORE METADATA
creation_date: 2025-06-08
modification_date: 2025-07-14
type: index
status: active
priority: high
area_category: Administration
owner: Jordan
aliases: [Tasks, Todo]
tags: [tasks, index, productivity]

# RELATIONSHIPS (Enhanced System)
related_areas: ["[[Task Management]]", "[[Administration]]"]
related_resources: ["[[Task Management System]]", "[[Productivity Tools]]"]
related_people: ["[[Jordan]]"]
---

# 🎯 Task Management Dashboard

> **Central command for all tasks across the vault** - Prioritized, contextualized, and actionable.

## 🚀 NEW: Enhanced Task System
**[[0.2-Tasks/Tasks Dashboard|🎯 Enhanced Tasks Dashboard]]** - Advanced task management with daily note integration

### Quick Access
- **[[0.2-Tasks/Tasks Dashboard|🎯 Tasks Dashboard]]** - Priority-based task command center
- **[[0.2-Tasks/Daily Task Sync|🔄 Daily Task Sync]]** - Automated carryover from daily notes
- **[[0.2-Tasks/Active/Active Tasks Index|📂 Active Tasks]]** - All current tasks with smart filtering
- **[[0.2-Tasks/Task Linking Guide|🔗 Task Linking Guide]]** - How to connect tasks to vault content

---

## 🔥 URGENT - Do First
```dataview
TABLE WITHOUT ID
  "🔥" as "",
  file.link as "URGENT TASKS",
  choice(estimated_time, estimated_time, "?") as "Time",
  choice(due, due, "ASAP") as "Due"
FROM ""
WHERE !completed
  AND task_priority = "urgent"
  AND (due <= date(today) OR !due)
SORT file.path ASC
LIMIT 8
```

## ⚡ HIGH PRIORITY - Important Work
```dataview
TABLE WITHOUT ID
  "⚡" as "",
  file.link as "HIGH PRIORITY",
  choice(estimated_time, estimated_time, "?") as "Time",
  choice(due, due, "Soon") as "Due"
FROM ""
WHERE !completed
  AND task_priority = "high"
  AND (due <= date(today) + dur(3 days) OR !due)
SORT due ASC, file.path ASC
LIMIT 10
```

## ⚠️ OVERDUE - Needs Attention
```dataview
TABLE WITHOUT ID
  "⚠️" as "",
  file.link as "OVERDUE TASKS",
  choice(task_priority = "urgent", "🔥", choice(task_priority = "high", "⚡", "📋")) as "P",
  due as "Was Due"
FROM ""
WHERE !completed
  AND due < date(today)
SORT task_priority ASC, due ASC
LIMIT 10
```

## 📅 DUE TODAY
```dataview
TABLE WITHOUT ID
  choice(task_priority = "urgent", "🔥", choice(task_priority = "high", "⚡", "📋")) as "P",
  file.link as "DUE TODAY",
  choice(estimated_time, estimated_time, "?") as "Time"
FROM ""
WHERE !completed
  AND due = date(today)
SORT task_priority ASC
```

## 🔋 CONTEXT-BASED TASK BATCHING

### 🧠 Deep Work (High Energy)
```dataview
TABLE WITHOUT ID
  "🔋" as "",
  file.link as "DEEP WORK",
  estimated_time as "Time",
  choice(due, due, "Flexible") as "Due"
FROM ""
WHERE !completed
  AND energy_required = "high"
  AND contains(tags, "#deep-work")
  AND (task_priority = "high" OR task_priority = "medium")
SORT task_priority ASC, estimated_time ASC
LIMIT 5
```

### 🪫 Admin Tasks (Low Energy)
```dataview
TABLE WITHOUT ID
  "🪫" as "",
  file.link as "ADMIN TASKS",
  estimated_time as "Time"
FROM ""
WHERE !completed
  AND energy_required = "low"
  AND contains(tags, "#admin")
SORT estimated_time ASC
LIMIT 8
```

### ⏱️ Quick Wins (15 minutes)
```dataview
TABLE WITHOUT ID
  choice(task_priority = "high", "⚡", choice(task_priority = "medium", "📋", "🔄")) as "P",
  file.link as "QUICK WINS",
  choice(energy_required, energy_required, "medium") as "Energy"
FROM ""
WHERE !completed
  AND estimated_time = "15min"
SORT task_priority ASC, energy_required DESC
LIMIT 6
```

## 📊 TASK ANALYTICS

### This Week's Completion
```dataview
TABLE WITHOUT ID
  "📊 This Week" as "Period",
  length(filter(rows, (r) => r.completed_date >= date(today) - dur(7 days))) as "Completed",
  length(filter(rows, (r) => r.task_priority = "high" AND r.completed_date >= date(today) - dur(7 days))) as "High Priority",
  round(average(filter(rows, (r) => r.actual_time AND r.completed_date >= date(today) - dur(7 days)).actual_time), 1) + " hrs" as "Avg Time"
FROM ""
WHERE type = "task" AND completed_date
GROUP BY "week"
```

### Task Context Success Rates
```dataview
TABLE WITHOUT ID
  task_context as "Context",
  length(rows) as "Total",
  length(filter(rows, (r) => r.completed_date)) as "Done",
  round(length(filter(rows, (r) => r.completed_date)) / length(rows) * 100, 1) + "%" as "Success Rate"
FROM ""
WHERE type = "task"
GROUP BY task_context
SORT "Success Rate" DESC
```

## 🔄 MAINTENANCE & ROUTINE

### Routine Tasks
```dataview
TABLE WITHOUT ID
  "🔄" as "",
  file.link as "ROUTINE TASKS",
  estimated_time as "Time"
FROM ""
WHERE !completed
  AND task_priority = "routine"
SORT estimated_time ASC
LIMIT 8
```

### No Due Date (Someday/Maybe)
```dataview
TABLE WITHOUT ID
  "💡" as "",
  file.link as "SOMEDAY/MAYBE",
  task_context as "Context"
FROM ""
WHERE !completed
  AND (task_priority = "someday" OR !due)
SORT task_context ASC
LIMIT 10
```

## 📈 RECENTLY COMPLETED
```dataview
TABLE WITHOUT ID
  "✅" as "",
  file.link as "COMPLETED",
  task_priority as "Priority",
  completed_date as "Done"
FROM ""
WHERE completed_date >= date(today) - dur(7 days)
SORT completed_date DESC
LIMIT 10
```

## 🚀 Quick Actions
- [[Templates/Enhanced Task|➕ Create New Task]]
- [[Templates/Enhanced Project|📋 Create New Project]]
- [[Templates/Enhanced Area|🏢 Create New Area]]

## 🔗 Integration with Enhanced System

### Daily Note Task Carryover
```dataview
TABLE WITHOUT ID
  "📅" as "",
  file.link as "Daily Note with Open Tasks",
  date as "Date",
  length(filter(file.tasks, (t) => !t.completed)) as "Open Tasks"
FROM "0-Daily Notes"
WHERE file.tasks
  AND length(filter(file.tasks, (t) => !t.completed)) > 0
  AND date >= date(today) - dur(7 days)
SORT date DESC
LIMIT 5
```

### Enhanced System Status
```dataview
TABLE WITHOUT ID
  "📊 Enhanced Task System" as "Metric",
  length(filter(pages('"0.2-Tasks/Active"'), (p) => p.type = "task" AND !p.completed)) as "Active Tasks",
  length(filter(pages('"0.2-Tasks/Active"'), (p) => p.task_priority = "urgent")) as "🔥 Urgent",
  length(filter(pages('"0.2-Tasks/Active"'), (p) => p.task_priority = "high")) as "⚡ High Priority"
FROM ""
WHERE file.name = "Tasks"
LIMIT 1
```

## Related
- **[[0.2-Tasks/Tasks TOC|📋 Enhanced Tasks System]]** - Complete task management hub
- [[Home]]
- [[1-Projects]]
- [[2-Areas]]
- [[Task Management]]
