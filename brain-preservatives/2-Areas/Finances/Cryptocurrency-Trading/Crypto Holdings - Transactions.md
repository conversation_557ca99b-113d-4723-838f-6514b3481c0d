---
# CORE METADATA
creation_date: 2025-08-16
modification_date: 2025-08-16
type: area
status: active
priority: low
area_category: Finance
owner: Jordan
tags: [para/areas, finance]

# AREA MANAGEMENT
responsibility_level: medium
review_frequency: weekly
last_review_date: 2025-08-16
next_review_date: 2025-09-15
key_metrics: []

# RELATIONSHIPS (Enhanced System)
related_projects: []
related_resources: []
related_people: ["[[Jordan]]"]
depends_on: []

# TASK MANAGEMENT
task_priority: medium
task_context: maintenance
---

# Stellar XLM Holdings - Transactions

## Overview
<!-- Brief description of this area of responsibility -->

## Transactions
#cryptocurrency #transactions
### Coinspot Data
#coinspot 
#### Solana
#sol #solana

| Date           | Type       | Amount   | Rate   | Rate ex. fee | Total     |
| -------------- | ---------- | -------- | ------ | ------------ | --------- |
| 26/01 12:18 AM | Buy        | 0.051109 | 410.89 | 406.821855   | 21.00 AUD |
| 18/01 08:54 PM | Sell       | 0.146449 | 383.62 | 383.999997   | 56.18 AUD |
| 17/01 03:49 PM | Market Buy | 0.086851 | 344.53 | 344.182111   | 29.92 AUD |
| 17/01 03:47 PM | Market Buy | 0.059598 | 344.63 | 344.283931   | 20.54 AUD |
#### Stellar (XLM)
#xlm #stellar

| Date           | Type       | Amount    | Rate     | Rate ex. fee | Total     |
| -------------- | ---------- | --------- | -------- | ------------ | --------- |
| 17/01 03:39 PM | Market Buy | 61.06357  | 0.818819 | 0.818        | 50.00 AUD |
| 16/01 11:36 AM | Sell       | 26.287392 | 0.743838 | 0.751352     | 19.55 AUD |
| 15/01 10:31 PM | Buy        | 26.287392 | 0.760821 | 0.753288     | 20.00 AUD |
| 12/01 07:41 AM | Sell       | 53.509558 | 0.712264 | 0.719459     | 38.11 AUD |
| 06/01 10:29 PM | Buy        | 53.509558 | 0.74753  | 0.740129     | 40.00 AUD |

## Key Responsibilities
<!-- List the key responsibilities in this area -->
-

## 📋 This Week's Focus
```dataview

TABLE WITHOUT ID
  choice(task_priority = "urgent", "🔥", choice(task_priority = "high", "⚡", "📋")) as "P",
  file.link as "This Week's Tasks",
  choice(estimated_time, estimated_time, "?") as "Time",
  choice(deadline, deadline, "Flexible") as "Due"
FROM this.file.folder OR "1-Projects"
WHERE !completed
  AND contains(related_areas, "Stellar XLM Holdings - Transactions")
  AND due >= date(today)
  AND due <= date(today) + dur(7 days)
SORT task_priority ASC, due ASC
LIMIT 10

```



## 🔄 Routine Maintenance
```dataview

TABLE WITHOUT ID
  "🔄" as "",
  file.link as "Routine Tasks",
  estimated_time as "Time"
FROM this.file.folder
WHERE !completed
  AND task_priority = "routine"
  AND task_context = "maintenance"
SORT estimated_time ASC
LIMIT 5

```



## Regular Tasks Checklist
<!-- Recurring tasks in this area -->
- [ ] **Daily**:
- [ ] **Weekly**:
- [ ] **Monthly**:
- [ ] **Quarterly**:

## 🚀 Active Projects in Area
```dataview

TABLE WITHOUT ID
  file.link as "🚀 Project",
  choice(priority = "critical", "🔥", choice(priority = "high", "⚡", choice(priority = "medium", "📋", "💡"))) as "P",
  completion_percentage + "%" as "Progress",
  deadline as "Due"
FROM "1-Projects"
WHERE contains(related_areas, "Stellar XLM Holdings - Transactions")
  AND (status = "active" OR status = "on-hold")
SORT choice(priority = "critical", 1, choice(priority = "high", 2, 3)) ASC, deadline ASC
LIMIT 6

```



## 📚 Area Resources
```dataview

LIST
FROM "3-Resources"
WHERE contains(related_areas, "Stellar XLM Holdings - Transactions")
   OR area_category = "Personal"
   OR contains(tags, "personal")
SORT choice(contains(related_areas, "Stellar XLM Holdings - Transactions"), 1, 2) ASC, file.mtime DESC
LIMIT 5

```



## 📅 Upcoming Area Reviews
```dataview

TABLE WITHOUT ID
  file.link as "📅 Review Needed",
  next_review_date as "Due",
  responsibility_level as "Priority"
FROM "2-Areas"
WHERE next_review_date <= date(today) + dur(14 days)
  AND file.name != "Stellar XLM Holdings - Transactions"
  AND status = "active"
SORT next_review_date ASC
LIMIT 3

```



## 📝 Recent Area Activity
```dataview

TABLE WITHOUT ID
  file.link as "📝 Recent Activity",
  type as "Type",
  file.mtime as "Last Modified"
FROM -"Templates"
WHERE (contains(related_areas, "Stellar XLM Holdings - Transactions")
   OR contains(file.content, "[[Stellar XLM Holdings - Transactions]]"))
  AND file.mtime >= date(today) - dur(7 days)
SORT file.mtime DESC
LIMIT 5

```



## Key Metrics
<!-- Metrics to track in this area -->
-

## Quick Links
- [[Stellar XLM Holdings - Transactions Project|New Project]]
- [[Stellar XLM Holdings - Transactions Resource|New Resource]]
- [[Stellar XLM Holdings - Transactions Meeting|New Meeting]]
- [[2-Areas|All Areas]]


---
