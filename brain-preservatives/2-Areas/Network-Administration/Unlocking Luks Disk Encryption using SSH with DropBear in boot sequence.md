---
creation_date: 2025-06-30
modification_date: 2025-06-30
type: resource
source: Personal research
tags: [para/resources, reference]
area: Administration
difficulty: medium
url: 
---

# Unlocking Luks Disk Encryption using SSH with DropBear in boot sequence

## Overview
<!-- Brief description of this resource -->

## Key Points
<!-- Main takeaways or important information -->
ssh to the #sirius #dropbear instance:
```bash
ssh -p 2222 -i ~/.ssh/sirius-ps root@*********** -v
```

## Details
<!-- Detailed information -->

## Examples
<!-- Examples or code snippets if applicable -->
```
// Code example
```

## Related Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  status as "Status",
  priority as "Priority"
FROM "1-Projects"
WHERE contains(file.content, "[[Unlocking Luks Disk Encryption using SSH with DropBear in boot sequence]]")
SORT priority ASC
```

## Related Areas
```dataview
TABLE WITHOUT ID
  file.link as "Area",
  area as "Category"
FROM "2-Areas"
WHERE contains(file.content, "[[Unlocking Luks Disk Encryption using SSH with DropBear in boot sequence]]") OR area = "Administration"
```

## Related Resources
```dataview
LIST
FROM "3-Resources"
WHERE contains(tags, "reference") AND file.name != "Unlocking Luks Disk Encryption using SSH with DropBear in boot sequence"
LIMIT 5
```

## Notes
<!-- Any additional notes -->

## Quick Links
- [[Unlocking Luks Disk Encryption using SSH with DropBear in boot sequence Project|New Project]]
- [[Unlocking Luks Disk Encryption using SSH with DropBear in boot sequence Reference|Quick Reference]]
- [[3-Resources|All Resources]]
