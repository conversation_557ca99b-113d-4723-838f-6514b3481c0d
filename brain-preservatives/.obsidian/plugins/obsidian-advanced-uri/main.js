/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source visit the plugins github repository (https://github.com/Vinzent03/obsidian-advanced-uri)
*/

var ze=Object.create;var Y=Object.defineProperty;var Le=Object.getOwnPropertyDescriptor;var De=Object.getOwnPropertyNames;var Ue=Object.getPrototypeOf,He=Object.prototype.hasOwnProperty;var Ne=(a,n)=>()=>(n||a((n={exports:{}}).exports,n),n.exports),We=(a,n)=>{for(var e in n)Y(a,e,{get:n[e],enumerable:!0})},fe=(a,n,e,t)=>{if(n&&typeof n=="object"||typeof n=="function")for(let i of De(n))!He.call(a,i)&&i!==e&&Y(a,i,{get:()=>n[i],enumerable:!(t=Le(n,i))||t.enumerable});return a};var Be=(a,n,e)=>(e=a!=null?ze(Ue(a)):{},fe(n||!a||!a.__esModule?Y(e,"default",{value:a,enumerable:!0}):e,a)),$e=a=>fe(Y({},"__esModule",{value:!0}),a);var ge=Ne((H,ae)=>{(function(n,e){typeof H=="object"&&typeof ae=="object"?ae.exports=e():typeof define=="function"&&define.amd?define([],e):typeof H=="object"?H.feather=e():n.feather=e()})(typeof self!="undefined"?self:H,function(){return function(a){var n={};function e(t){if(n[t])return n[t].exports;var i=n[t]={i:t,l:!1,exports:{}};return a[t].call(i.exports,i,i.exports,e),i.l=!0,i.exports}return e.m=a,e.c=n,e.d=function(t,i,o){e.o(t,i)||Object.defineProperty(t,i,{configurable:!1,enumerable:!0,get:o})},e.r=function(t){Object.defineProperty(t,"__esModule",{value:!0})},e.n=function(t){var i=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(i,"a",i),i},e.o=function(t,i){return Object.prototype.hasOwnProperty.call(t,i)},e.p="",e(e.s=0)}({"./dist/icons.json":function(a){a.exports={activity:'<polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline>',airplay:'<path d="M5 17H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-1"></path><polygon points="12 15 17 21 7 21 12 15"></polygon>',"alert-circle":'<circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line>',"alert-octagon":'<polygon points="7.86 2 16.14 2 22 7.86 22 16.14 16.14 22 7.86 22 2 16.14 2 7.86 7.86 2"></polygon><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line>',"alert-triangle":'<path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path><line x1="12" y1="9" x2="12" y2="13"></line><line x1="12" y1="17" x2="12.01" y2="17"></line>',"align-center":'<line x1="18" y1="10" x2="6" y2="10"></line><line x1="21" y1="6" x2="3" y2="6"></line><line x1="21" y1="14" x2="3" y2="14"></line><line x1="18" y1="18" x2="6" y2="18"></line>',"align-justify":'<line x1="21" y1="10" x2="3" y2="10"></line><line x1="21" y1="6" x2="3" y2="6"></line><line x1="21" y1="14" x2="3" y2="14"></line><line x1="21" y1="18" x2="3" y2="18"></line>',"align-left":'<line x1="17" y1="10" x2="3" y2="10"></line><line x1="21" y1="6" x2="3" y2="6"></line><line x1="21" y1="14" x2="3" y2="14"></line><line x1="17" y1="18" x2="3" y2="18"></line>',"align-right":'<line x1="21" y1="10" x2="7" y2="10"></line><line x1="21" y1="6" x2="3" y2="6"></line><line x1="21" y1="14" x2="3" y2="14"></line><line x1="21" y1="18" x2="7" y2="18"></line>',anchor:'<circle cx="12" cy="5" r="3"></circle><line x1="12" y1="22" x2="12" y2="8"></line><path d="M5 12H2a10 10 0 0 0 20 0h-3"></path>',aperture:'<circle cx="12" cy="12" r="10"></circle><line x1="14.31" y1="8" x2="20.05" y2="17.94"></line><line x1="9.69" y1="8" x2="21.17" y2="8"></line><line x1="7.38" y1="12" x2="13.12" y2="2.06"></line><line x1="9.69" y1="16" x2="3.95" y2="6.06"></line><line x1="14.31" y1="16" x2="2.83" y2="16"></line><line x1="16.62" y1="12" x2="10.88" y2="21.94"></line>',archive:'<polyline points="21 8 21 21 3 21 3 8"></polyline><rect x="1" y="3" width="22" height="5"></rect><line x1="10" y1="12" x2="14" y2="12"></line>',"arrow-down-circle":'<circle cx="12" cy="12" r="10"></circle><polyline points="8 12 12 16 16 12"></polyline><line x1="12" y1="8" x2="12" y2="16"></line>',"arrow-down-left":'<line x1="17" y1="7" x2="7" y2="17"></line><polyline points="17 17 7 17 7 7"></polyline>',"arrow-down-right":'<line x1="7" y1="7" x2="17" y2="17"></line><polyline points="17 7 17 17 7 17"></polyline>',"arrow-down":'<line x1="12" y1="5" x2="12" y2="19"></line><polyline points="19 12 12 19 5 12"></polyline>',"arrow-left-circle":'<circle cx="12" cy="12" r="10"></circle><polyline points="12 8 8 12 12 16"></polyline><line x1="16" y1="12" x2="8" y2="12"></line>',"arrow-left":'<line x1="19" y1="12" x2="5" y2="12"></line><polyline points="12 19 5 12 12 5"></polyline>',"arrow-right-circle":'<circle cx="12" cy="12" r="10"></circle><polyline points="12 16 16 12 12 8"></polyline><line x1="8" y1="12" x2="16" y2="12"></line>',"arrow-right":'<line x1="5" y1="12" x2="19" y2="12"></line><polyline points="12 5 19 12 12 19"></polyline>',"arrow-up-circle":'<circle cx="12" cy="12" r="10"></circle><polyline points="16 12 12 8 8 12"></polyline><line x1="12" y1="16" x2="12" y2="8"></line>',"arrow-up-left":'<line x1="17" y1="17" x2="7" y2="7"></line><polyline points="7 17 7 7 17 7"></polyline>',"arrow-up-right":'<line x1="7" y1="17" x2="17" y2="7"></line><polyline points="7 7 17 7 17 17"></polyline>',"arrow-up":'<line x1="12" y1="19" x2="12" y2="5"></line><polyline points="5 12 12 5 19 12"></polyline>',"at-sign":'<circle cx="12" cy="12" r="4"></circle><path d="M16 8v5a3 3 0 0 0 6 0v-1a10 10 0 1 0-3.92 7.94"></path>',award:'<circle cx="12" cy="8" r="7"></circle><polyline points="8.21 13.89 7 23 12 20 17 23 15.79 13.88"></polyline>',"bar-chart-2":'<line x1="18" y1="20" x2="18" y2="10"></line><line x1="12" y1="20" x2="12" y2="4"></line><line x1="6" y1="20" x2="6" y2="14"></line>',"bar-chart":'<line x1="12" y1="20" x2="12" y2="10"></line><line x1="18" y1="20" x2="18" y2="4"></line><line x1="6" y1="20" x2="6" y2="16"></line>',"battery-charging":'<path d="M5 18H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h3.19M15 6h2a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-3.19"></path><line x1="23" y1="13" x2="23" y2="11"></line><polyline points="11 6 7 12 13 12 9 18"></polyline>',battery:'<rect x="1" y="6" width="18" height="12" rx="2" ry="2"></rect><line x1="23" y1="13" x2="23" y2="11"></line>',"bell-off":'<path d="M13.73 21a2 2 0 0 1-3.46 0"></path><path d="M18.63 13A17.89 17.89 0 0 1 18 8"></path><path d="M6.26 6.26A5.86 5.86 0 0 0 6 8c0 7-3 9-3 9h14"></path><path d="M18 8a6 6 0 0 0-9.33-5"></path><line x1="1" y1="1" x2="23" y2="23"></line>',bell:'<path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path><path d="M13.73 21a2 2 0 0 1-3.46 0"></path>',bluetooth:'<polyline points="6.5 6.5 17.5 17.5 12 23 12 1 17.5 6.5 6.5 17.5"></polyline>',bold:'<path d="M6 4h8a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z"></path><path d="M6 12h9a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z"></path>',"book-open":'<path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path><path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>',book:'<path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"></path><path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z"></path>',bookmark:'<path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"></path>',box:'<path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path><polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline><line x1="12" y1="22.08" x2="12" y2="12"></line>',briefcase:'<rect x="2" y="7" width="20" height="14" rx="2" ry="2"></rect><path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path>',calendar:'<rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line>',"camera-off":'<line x1="1" y1="1" x2="23" y2="23"></line><path d="M21 21H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h3m3-3h6l2 3h4a2 2 0 0 1 2 2v9.34m-7.72-2.06a4 4 0 1 1-5.56-5.56"></path>',camera:'<path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path><circle cx="12" cy="13" r="4"></circle>',cast:'<path d="M2 16.1A5 5 0 0 1 5.9 20M2 12.05A9 9 0 0 1 9.95 20M2 8V6a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2h-6"></path><line x1="2" y1="20" x2="2.01" y2="20"></line>',"check-circle":'<path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline>',"check-square":'<polyline points="9 11 12 14 22 4"></polyline><path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11"></path>',check:'<polyline points="20 6 9 17 4 12"></polyline>',"chevron-down":'<polyline points="6 9 12 15 18 9"></polyline>',"chevron-left":'<polyline points="15 18 9 12 15 6"></polyline>',"chevron-right":'<polyline points="9 18 15 12 9 6"></polyline>',"chevron-up":'<polyline points="18 15 12 9 6 15"></polyline>',"chevrons-down":'<polyline points="7 13 12 18 17 13"></polyline><polyline points="7 6 12 11 17 6"></polyline>',"chevrons-left":'<polyline points="11 17 6 12 11 7"></polyline><polyline points="18 17 13 12 18 7"></polyline>',"chevrons-right":'<polyline points="13 17 18 12 13 7"></polyline><polyline points="6 17 11 12 6 7"></polyline>',"chevrons-up":'<polyline points="17 11 12 6 7 11"></polyline><polyline points="17 18 12 13 7 18"></polyline>',chrome:'<circle cx="12" cy="12" r="10"></circle><circle cx="12" cy="12" r="4"></circle><line x1="21.17" y1="8" x2="12" y2="8"></line><line x1="3.95" y1="6.06" x2="8.54" y2="14"></line><line x1="10.88" y1="21.94" x2="15.46" y2="14"></line>',circle:'<circle cx="12" cy="12" r="10"></circle>',clipboard:'<path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path><rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>',clock:'<circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline>',"cloud-drizzle":'<line x1="8" y1="19" x2="8" y2="21"></line><line x1="8" y1="13" x2="8" y2="15"></line><line x1="16" y1="19" x2="16" y2="21"></line><line x1="16" y1="13" x2="16" y2="15"></line><line x1="12" y1="21" x2="12" y2="23"></line><line x1="12" y1="15" x2="12" y2="17"></line><path d="M20 16.58A5 5 0 0 0 18 7h-1.26A8 8 0 1 0 4 15.25"></path>',"cloud-lightning":'<path d="M19 16.9A5 5 0 0 0 18 7h-1.26a8 8 0 1 0-11.62 9"></path><polyline points="13 11 9 17 15 17 11 23"></polyline>',"cloud-off":'<path d="M22.61 16.95A5 5 0 0 0 18 10h-1.26a8 8 0 0 0-7.05-6M5 5a8 8 0 0 0 4 15h9a5 5 0 0 0 1.7-.3"></path><line x1="1" y1="1" x2="23" y2="23"></line>',"cloud-rain":'<line x1="16" y1="13" x2="16" y2="21"></line><line x1="8" y1="13" x2="8" y2="21"></line><line x1="12" y1="15" x2="12" y2="23"></line><path d="M20 16.58A5 5 0 0 0 18 7h-1.26A8 8 0 1 0 4 15.25"></path>',"cloud-snow":'<path d="M20 17.58A5 5 0 0 0 18 8h-1.26A8 8 0 1 0 4 16.25"></path><line x1="8" y1="16" x2="8.01" y2="16"></line><line x1="8" y1="20" x2="8.01" y2="20"></line><line x1="12" y1="18" x2="12.01" y2="18"></line><line x1="12" y1="22" x2="12.01" y2="22"></line><line x1="16" y1="16" x2="16.01" y2="16"></line><line x1="16" y1="20" x2="16.01" y2="20"></line>',cloud:'<path d="M18 10h-1.26A8 8 0 1 0 9 20h9a5 5 0 0 0 0-10z"></path>',code:'<polyline points="16 18 22 12 16 6"></polyline><polyline points="8 6 2 12 8 18"></polyline>',codepen:'<polygon points="12 2 22 8.5 22 15.5 12 22 2 15.5 2 8.5 12 2"></polygon><line x1="12" y1="22" x2="12" y2="15.5"></line><polyline points="22 8.5 12 15.5 2 8.5"></polyline><polyline points="2 15.5 12 8.5 22 15.5"></polyline><line x1="12" y1="2" x2="12" y2="8.5"></line>',codesandbox:'<path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path><polyline points="7.5 4.21 12 6.81 16.5 4.21"></polyline><polyline points="7.5 19.79 7.5 14.6 3 12"></polyline><polyline points="21 12 16.5 14.6 16.5 19.79"></polyline><polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline><line x1="12" y1="22.08" x2="12" y2="12"></line>',coffee:'<path d="M18 8h1a4 4 0 0 1 0 8h-1"></path><path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z"></path><line x1="6" y1="1" x2="6" y2="4"></line><line x1="10" y1="1" x2="10" y2="4"></line><line x1="14" y1="1" x2="14" y2="4"></line>',columns:'<path d="M12 3h7a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-7m0-18H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h7m0-18v18"></path>',command:'<path d="M18 3a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3 3 3 0 0 0 3-3 3 3 0 0 0-3-3H6a3 3 0 0 0-3 3 3 3 0 0 0 3 3 3 3 0 0 0 3-3V6a3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3h12a3 3 0 0 0 3-3 3 3 0 0 0-3-3z"></path>',compass:'<circle cx="12" cy="12" r="10"></circle><polygon points="16.24 7.76 14.12 14.12 7.76 16.24 9.88 9.88 16.24 7.76"></polygon>',copy:'<rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect><path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>',"corner-down-left":'<polyline points="9 10 4 15 9 20"></polyline><path d="M20 4v7a4 4 0 0 1-4 4H4"></path>',"corner-down-right":'<polyline points="15 10 20 15 15 20"></polyline><path d="M4 4v7a4 4 0 0 0 4 4h12"></path>',"corner-left-down":'<polyline points="14 15 9 20 4 15"></polyline><path d="M20 4h-7a4 4 0 0 0-4 4v12"></path>',"corner-left-up":'<polyline points="14 9 9 4 4 9"></polyline><path d="M20 20h-7a4 4 0 0 1-4-4V4"></path>',"corner-right-down":'<polyline points="10 15 15 20 20 15"></polyline><path d="M4 4h7a4 4 0 0 1 4 4v12"></path>',"corner-right-up":'<polyline points="10 9 15 4 20 9"></polyline><path d="M4 20h7a4 4 0 0 0 4-4V4"></path>',"corner-up-left":'<polyline points="9 14 4 9 9 4"></polyline><path d="M20 20v-7a4 4 0 0 0-4-4H4"></path>',"corner-up-right":'<polyline points="15 14 20 9 15 4"></polyline><path d="M4 20v-7a4 4 0 0 1 4-4h12"></path>',cpu:'<rect x="4" y="4" width="16" height="16" rx="2" ry="2"></rect><rect x="9" y="9" width="6" height="6"></rect><line x1="9" y1="1" x2="9" y2="4"></line><line x1="15" y1="1" x2="15" y2="4"></line><line x1="9" y1="20" x2="9" y2="23"></line><line x1="15" y1="20" x2="15" y2="23"></line><line x1="20" y1="9" x2="23" y2="9"></line><line x1="20" y1="14" x2="23" y2="14"></line><line x1="1" y1="9" x2="4" y2="9"></line><line x1="1" y1="14" x2="4" y2="14"></line>',"credit-card":'<rect x="1" y="4" width="22" height="16" rx="2" ry="2"></rect><line x1="1" y1="10" x2="23" y2="10"></line>',crop:'<path d="M6.13 1L6 16a2 2 0 0 0 2 2h15"></path><path d="M1 6.13L16 6a2 2 0 0 1 2 2v15"></path>',crosshair:'<circle cx="12" cy="12" r="10"></circle><line x1="22" y1="12" x2="18" y2="12"></line><line x1="6" y1="12" x2="2" y2="12"></line><line x1="12" y1="6" x2="12" y2="2"></line><line x1="12" y1="22" x2="12" y2="18"></line>',database:'<ellipse cx="12" cy="5" rx="9" ry="3"></ellipse><path d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3"></path><path d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5"></path>',delete:'<path d="M21 4H8l-7 8 7 8h13a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2z"></path><line x1="18" y1="9" x2="12" y2="15"></line><line x1="12" y1="9" x2="18" y2="15"></line>',disc:'<circle cx="12" cy="12" r="10"></circle><circle cx="12" cy="12" r="3"></circle>',"divide-circle":'<line x1="8" y1="12" x2="16" y2="12"></line><line x1="12" y1="16" x2="12" y2="16"></line><line x1="12" y1="8" x2="12" y2="8"></line><circle cx="12" cy="12" r="10"></circle>',"divide-square":'<rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="8" y1="12" x2="16" y2="12"></line><line x1="12" y1="16" x2="12" y2="16"></line><line x1="12" y1="8" x2="12" y2="8"></line>',divide:'<circle cx="12" cy="6" r="2"></circle><line x1="5" y1="12" x2="19" y2="12"></line><circle cx="12" cy="18" r="2"></circle>',"dollar-sign":'<line x1="12" y1="1" x2="12" y2="23"></line><path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>',"download-cloud":'<polyline points="8 17 12 21 16 17"></polyline><line x1="12" y1="12" x2="12" y2="21"></line><path d="M20.88 18.09A5 5 0 0 0 18 9h-1.26A8 8 0 1 0 3 16.29"></path>',download:'<path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="7 10 12 15 17 10"></polyline><line x1="12" y1="15" x2="12" y2="3"></line>',dribbble:'<circle cx="12" cy="12" r="10"></circle><path d="M8.56 2.75c4.37 6.03 6.02 9.42 8.03 17.72m2.54-15.38c-3.72 4.35-8.94 5.66-16.88 5.85m19.5 1.9c-3.5-.93-6.63-.82-8.94 0-2.58.92-5.01 2.86-7.44 6.32"></path>',droplet:'<path d="M12 2.69l5.66 5.66a8 8 0 1 1-11.31 0z"></path>',"edit-2":'<path d="M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"></path>',"edit-3":'<path d="M12 20h9"></path><path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>',edit:'<path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>',"external-link":'<path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" y1="14" x2="21" y2="3"></line>',"eye-off":'<path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path><line x1="1" y1="1" x2="23" y2="23"></line>',eye:'<path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path><circle cx="12" cy="12" r="3"></circle>',facebook:'<path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>',"fast-forward":'<polygon points="13 19 22 12 13 5 13 19"></polygon><polygon points="2 19 11 12 2 5 2 19"></polygon>',feather:'<path d="M20.24 12.24a6 6 0 0 0-8.49-8.49L5 10.5V19h8.5z"></path><line x1="16" y1="8" x2="2" y2="22"></line><line x1="17.5" y1="15" x2="9" y2="15"></line>',figma:'<path d="M5 5.5A3.5 3.5 0 0 1 8.5 2H12v7H8.5A3.5 3.5 0 0 1 5 5.5z"></path><path d="M12 2h3.5a3.5 3.5 0 1 1 0 7H12V2z"></path><path d="M12 12.5a3.5 3.5 0 1 1 7 0 3.5 3.5 0 1 1-7 0z"></path><path d="M5 19.5A3.5 3.5 0 0 1 8.5 16H12v3.5a3.5 3.5 0 1 1-7 0z"></path><path d="M5 12.5A3.5 3.5 0 0 1 8.5 9H12v7H8.5A3.5 3.5 0 0 1 5 12.5z"></path>',"file-minus":'<path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="9" y1="15" x2="15" y2="15"></line>',"file-plus":'<path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="12" y1="18" x2="12" y2="12"></line><line x1="9" y1="15" x2="15" y2="15"></line>',"file-text":'<path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10 9 9 9 8 9"></polyline>',file:'<path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path><polyline points="13 2 13 9 20 9"></polyline>',film:'<rect x="2" y="2" width="20" height="20" rx="2.18" ry="2.18"></rect><line x1="7" y1="2" x2="7" y2="22"></line><line x1="17" y1="2" x2="17" y2="22"></line><line x1="2" y1="12" x2="22" y2="12"></line><line x1="2" y1="7" x2="7" y2="7"></line><line x1="2" y1="17" x2="7" y2="17"></line><line x1="17" y1="17" x2="22" y2="17"></line><line x1="17" y1="7" x2="22" y2="7"></line>',filter:'<polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>',flag:'<path d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"></path><line x1="4" y1="22" x2="4" y2="15"></line>',"folder-minus":'<path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path><line x1="9" y1="14" x2="15" y2="14"></line>',"folder-plus":'<path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path><line x1="12" y1="11" x2="12" y2="17"></line><line x1="9" y1="14" x2="15" y2="14"></line>',folder:'<path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path>',framer:'<path d="M5 16V9h14V2H5l14 14h-7m-7 0l7 7v-7m-7 0h7"></path>',frown:'<circle cx="12" cy="12" r="10"></circle><path d="M16 16s-1.5-2-4-2-4 2-4 2"></path><line x1="9" y1="9" x2="9.01" y2="9"></line><line x1="15" y1="9" x2="15.01" y2="9"></line>',gift:'<polyline points="20 12 20 22 4 22 4 12"></polyline><rect x="2" y="7" width="20" height="5"></rect><line x1="12" y1="22" x2="12" y2="7"></line><path d="M12 7H7.5a2.5 2.5 0 0 1 0-5C11 2 12 7 12 7z"></path><path d="M12 7h4.5a2.5 2.5 0 0 0 0-5C13 2 12 7 12 7z"></path>',"git-branch":'<line x1="6" y1="3" x2="6" y2="15"></line><circle cx="18" cy="6" r="3"></circle><circle cx="6" cy="18" r="3"></circle><path d="M18 9a9 9 0 0 1-9 9"></path>',"git-commit":'<circle cx="12" cy="12" r="4"></circle><line x1="1.05" y1="12" x2="7" y2="12"></line><line x1="17.01" y1="12" x2="22.96" y2="12"></line>',"git-merge":'<circle cx="18" cy="18" r="3"></circle><circle cx="6" cy="6" r="3"></circle><path d="M6 21V9a9 9 0 0 0 9 9"></path>',"git-pull-request":'<circle cx="18" cy="18" r="3"></circle><circle cx="6" cy="6" r="3"></circle><path d="M13 6h3a2 2 0 0 1 2 2v7"></path><line x1="6" y1="9" x2="6" y2="21"></line>',github:'<path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"></path>',gitlab:'<path d="M22.65 14.39L12 22.13 1.35 14.39a.84.84 0 0 1-.3-.94l1.22-3.78 2.44-7.51A.42.42 0 0 1 4.82 2a.43.43 0 0 1 .58 0 .42.42 0 0 1 .11.18l2.44 7.49h8.1l2.44-7.51A.42.42 0 0 1 18.6 2a.43.43 0 0 1 .58 0 .42.42 0 0 1 .11.18l2.44 7.51L23 13.45a.84.84 0 0 1-.35.94z"></path>',globe:'<circle cx="12" cy="12" r="10"></circle><line x1="2" y1="12" x2="22" y2="12"></line><path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>',grid:'<rect x="3" y="3" width="7" height="7"></rect><rect x="14" y="3" width="7" height="7"></rect><rect x="14" y="14" width="7" height="7"></rect><rect x="3" y="14" width="7" height="7"></rect>',"hard-drive":'<line x1="22" y1="12" x2="2" y2="12"></line><path d="M5.45 5.11L2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z"></path><line x1="6" y1="16" x2="6.01" y2="16"></line><line x1="10" y1="16" x2="10.01" y2="16"></line>',hash:'<line x1="4" y1="9" x2="20" y2="9"></line><line x1="4" y1="15" x2="20" y2="15"></line><line x1="10" y1="3" x2="8" y2="21"></line><line x1="16" y1="3" x2="14" y2="21"></line>',headphones:'<path d="M3 18v-6a9 9 0 0 1 18 0v6"></path><path d="M21 19a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3zM3 19a2 2 0 0 0 2 2h1a2 2 0 0 0 2-2v-3a2 2 0 0 0-2-2H3z"></path>',heart:'<path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>',"help-circle":'<circle cx="12" cy="12" r="10"></circle><path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path><line x1="12" y1="17" x2="12.01" y2="17"></line>',hexagon:'<path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>',home:'<path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path><polyline points="9 22 9 12 15 12 15 22"></polyline>',image:'<rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><circle cx="8.5" cy="8.5" r="1.5"></circle><polyline points="21 15 16 10 5 21"></polyline>',inbox:'<polyline points="22 12 16 12 14 15 10 15 8 12 2 12"></polyline><path d="M5.45 5.11L2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z"></path>',info:'<circle cx="12" cy="12" r="10"></circle><line x1="12" y1="16" x2="12" y2="12"></line><line x1="12" y1="8" x2="12.01" y2="8"></line>',instagram:'<rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect><path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path><line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>',italic:'<line x1="19" y1="4" x2="10" y2="4"></line><line x1="14" y1="20" x2="5" y2="20"></line><line x1="15" y1="4" x2="9" y2="20"></line>',key:'<path d="M21 2l-2 2m-7.61 7.61a5.5 5.5 0 1 1-7.778 7.778 5.5 5.5 0 0 1 7.777-7.777zm0 0L15.5 7.5m0 0l3 3L22 7l-3-3m-3.5 3.5L19 4"></path>',layers:'<polygon points="12 2 2 7 12 12 22 7 12 2"></polygon><polyline points="2 17 12 22 22 17"></polyline><polyline points="2 12 12 17 22 12"></polyline>',layout:'<rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="3" y1="9" x2="21" y2="9"></line><line x1="9" y1="21" x2="9" y2="9"></line>',"life-buoy":'<circle cx="12" cy="12" r="10"></circle><circle cx="12" cy="12" r="4"></circle><line x1="4.93" y1="4.93" x2="9.17" y2="9.17"></line><line x1="14.83" y1="14.83" x2="19.07" y2="19.07"></line><line x1="14.83" y1="9.17" x2="19.07" y2="4.93"></line><line x1="14.83" y1="9.17" x2="18.36" y2="5.64"></line><line x1="4.93" y1="19.07" x2="9.17" y2="14.83"></line>',"link-2":'<path d="M15 7h3a5 5 0 0 1 5 5 5 5 0 0 1-5 5h-3m-6 0H6a5 5 0 0 1-5-5 5 5 0 0 1 5-5h3"></path><line x1="8" y1="12" x2="16" y2="12"></line>',link:'<path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path><path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path>',linkedin:'<path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path><rect x="2" y="9" width="4" height="12"></rect><circle cx="4" cy="4" r="2"></circle>',list:'<line x1="8" y1="6" x2="21" y2="6"></line><line x1="8" y1="12" x2="21" y2="12"></line><line x1="8" y1="18" x2="21" y2="18"></line><line x1="3" y1="6" x2="3.01" y2="6"></line><line x1="3" y1="12" x2="3.01" y2="12"></line><line x1="3" y1="18" x2="3.01" y2="18"></line>',loader:'<line x1="12" y1="2" x2="12" y2="6"></line><line x1="12" y1="18" x2="12" y2="22"></line><line x1="4.93" y1="4.93" x2="7.76" y2="7.76"></line><line x1="16.24" y1="16.24" x2="19.07" y2="19.07"></line><line x1="2" y1="12" x2="6" y2="12"></line><line x1="18" y1="12" x2="22" y2="12"></line><line x1="4.93" y1="19.07" x2="7.76" y2="16.24"></line><line x1="16.24" y1="7.76" x2="19.07" y2="4.93"></line>',lock:'<rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect><path d="M7 11V7a5 5 0 0 1 10 0v4"></path>',"log-in":'<path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path><polyline points="10 17 15 12 10 7"></polyline><line x1="15" y1="12" x2="3" y2="12"></line>',"log-out":'<path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path><polyline points="16 17 21 12 16 7"></polyline><line x1="21" y1="12" x2="9" y2="12"></line>',mail:'<path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path><polyline points="22,6 12,13 2,6"></polyline>',"map-pin":'<path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path><circle cx="12" cy="10" r="3"></circle>',map:'<polygon points="1 6 1 22 8 18 16 22 23 18 23 2 16 6 8 2 1 6"></polygon><line x1="8" y1="2" x2="8" y2="18"></line><line x1="16" y1="6" x2="16" y2="22"></line>',"maximize-2":'<polyline points="15 3 21 3 21 9"></polyline><polyline points="9 21 3 21 3 15"></polyline><line x1="21" y1="3" x2="14" y2="10"></line><line x1="3" y1="21" x2="10" y2="14"></line>',maximize:'<path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"></path>',meh:'<circle cx="12" cy="12" r="10"></circle><line x1="8" y1="15" x2="16" y2="15"></line><line x1="9" y1="9" x2="9.01" y2="9"></line><line x1="15" y1="9" x2="15.01" y2="9"></line>',menu:'<line x1="3" y1="12" x2="21" y2="12"></line><line x1="3" y1="6" x2="21" y2="6"></line><line x1="3" y1="18" x2="21" y2="18"></line>',"message-circle":'<path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>',"message-square":'<path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>',"mic-off":'<line x1="1" y1="1" x2="23" y2="23"></line><path d="M9 9v3a3 3 0 0 0 5.12 2.12M15 9.34V4a3 3 0 0 0-5.94-.6"></path><path d="M17 16.95A7 7 0 0 1 5 12v-2m14 0v2a7 7 0 0 1-.11 1.23"></path><line x1="12" y1="19" x2="12" y2="23"></line><line x1="8" y1="23" x2="16" y2="23"></line>',mic:'<path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path><path d="M19 10v2a7 7 0 0 1-14 0v-2"></path><line x1="12" y1="19" x2="12" y2="23"></line><line x1="8" y1="23" x2="16" y2="23"></line>',"minimize-2":'<polyline points="4 14 10 14 10 20"></polyline><polyline points="20 10 14 10 14 4"></polyline><line x1="14" y1="10" x2="21" y2="3"></line><line x1="3" y1="21" x2="10" y2="14"></line>',minimize:'<path d="M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3"></path>',"minus-circle":'<circle cx="12" cy="12" r="10"></circle><line x1="8" y1="12" x2="16" y2="12"></line>',"minus-square":'<rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="8" y1="12" x2="16" y2="12"></line>',minus:'<line x1="5" y1="12" x2="19" y2="12"></line>',monitor:'<rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect><line x1="8" y1="21" x2="16" y2="21"></line><line x1="12" y1="17" x2="12" y2="21"></line>',moon:'<path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>',"more-horizontal":'<circle cx="12" cy="12" r="1"></circle><circle cx="19" cy="12" r="1"></circle><circle cx="5" cy="12" r="1"></circle>',"more-vertical":'<circle cx="12" cy="12" r="1"></circle><circle cx="12" cy="5" r="1"></circle><circle cx="12" cy="19" r="1"></circle>',"mouse-pointer":'<path d="M3 3l7.07 16.97 2.51-7.39 7.39-2.51L3 3z"></path><path d="M13 13l6 6"></path>',move:'<polyline points="5 9 2 12 5 15"></polyline><polyline points="9 5 12 2 15 5"></polyline><polyline points="15 19 12 22 9 19"></polyline><polyline points="19 9 22 12 19 15"></polyline><line x1="2" y1="12" x2="22" y2="12"></line><line x1="12" y1="2" x2="12" y2="22"></line>',music:'<path d="M9 18V5l12-2v13"></path><circle cx="6" cy="18" r="3"></circle><circle cx="18" cy="16" r="3"></circle>',"navigation-2":'<polygon points="12 2 19 21 12 17 5 21 12 2"></polygon>',navigation:'<polygon points="3 11 22 2 13 21 11 13 3 11"></polygon>',octagon:'<polygon points="7.86 2 16.14 2 22 7.86 22 16.14 16.14 22 7.86 22 2 16.14 2 7.86 7.86 2"></polygon>',package:'<line x1="16.5" y1="9.4" x2="7.5" y2="4.21"></line><path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path><polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline><line x1="12" y1="22.08" x2="12" y2="12"></line>',paperclip:'<path d="M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66l-9.2 9.19a2 2 0 0 1-2.83-2.83l8.49-8.48"></path>',"pause-circle":'<circle cx="12" cy="12" r="10"></circle><line x1="10" y1="15" x2="10" y2="9"></line><line x1="14" y1="15" x2="14" y2="9"></line>',pause:'<rect x="6" y="4" width="4" height="16"></rect><rect x="14" y="4" width="4" height="16"></rect>',"pen-tool":'<path d="M12 19l7-7 3 3-7 7-3-3z"></path><path d="M18 13l-1.5-7.5L2 2l3.5 14.5L13 18l5-5z"></path><path d="M2 2l7.586 7.586"></path><circle cx="11" cy="11" r="2"></circle>',percent:'<line x1="19" y1="5" x2="5" y2="19"></line><circle cx="6.5" cy="6.5" r="2.5"></circle><circle cx="17.5" cy="17.5" r="2.5"></circle>',"phone-call":'<path d="M15.05 5A5 5 0 0 1 19 8.95M15.05 1A9 9 0 0 1 23 8.94m-1 7.98v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>',"phone-forwarded":'<polyline points="19 1 23 5 19 9"></polyline><line x1="15" y1="5" x2="23" y2="5"></line><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>',"phone-incoming":'<polyline points="16 2 16 8 22 8"></polyline><line x1="23" y1="1" x2="16" y2="8"></line><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>',"phone-missed":'<line x1="23" y1="1" x2="17" y2="7"></line><line x1="17" y1="1" x2="23" y2="7"></line><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>',"phone-off":'<path d="M10.68 13.31a16 16 0 0 0 3.41 2.6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7 2 2 0 0 1 1.72 2v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.42 19.42 0 0 1-3.33-2.67m-2.67-3.34a19.79 19.79 0 0 1-3.07-8.63A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91"></path><line x1="23" y1="1" x2="1" y2="23"></line>',"phone-outgoing":'<polyline points="23 7 23 1 17 1"></polyline><line x1="16" y1="8" x2="23" y2="1"></line><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>',phone:'<path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>',"pie-chart":'<path d="M21.21 15.89A10 10 0 1 1 8 2.83"></path><path d="M22 12A10 10 0 0 0 12 2v10z"></path>',"play-circle":'<circle cx="12" cy="12" r="10"></circle><polygon points="10 8 16 12 10 16 10 8"></polygon>',play:'<polygon points="5 3 19 12 5 21 5 3"></polygon>',"plus-circle":'<circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="16"></line><line x1="8" y1="12" x2="16" y2="12"></line>',"plus-square":'<rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="12" y1="8" x2="12" y2="16"></line><line x1="8" y1="12" x2="16" y2="12"></line>',plus:'<line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line>',pocket:'<path d="M4 3h16a2 2 0 0 1 2 2v6a10 10 0 0 1-10 10A10 10 0 0 1 2 11V5a2 2 0 0 1 2-2z"></path><polyline points="8 10 12 14 16 10"></polyline>',power:'<path d="M18.36 6.64a9 9 0 1 1-12.73 0"></path><line x1="12" y1="2" x2="12" y2="12"></line>',printer:'<polyline points="6 9 6 2 18 2 18 9"></polyline><path d="M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2"></path><rect x="6" y="14" width="12" height="8"></rect>',radio:'<circle cx="12" cy="12" r="2"></circle><path d="M16.24 7.76a6 6 0 0 1 0 8.49m-8.48-.01a6 6 0 0 1 0-8.49m11.31-2.82a10 10 0 0 1 0 14.14m-14.14 0a10 10 0 0 1 0-14.14"></path>',"refresh-ccw":'<polyline points="1 4 1 10 7 10"></polyline><polyline points="23 20 23 14 17 14"></polyline><path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"></path>',"refresh-cw":'<polyline points="23 4 23 10 17 10"></polyline><polyline points="1 20 1 14 7 14"></polyline><path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>',repeat:'<polyline points="17 1 21 5 17 9"></polyline><path d="M3 11V9a4 4 0 0 1 4-4h14"></path><polyline points="7 23 3 19 7 15"></polyline><path d="M21 13v2a4 4 0 0 1-4 4H3"></path>',rewind:'<polygon points="11 19 2 12 11 5 11 19"></polygon><polygon points="22 19 13 12 22 5 22 19"></polygon>',"rotate-ccw":'<polyline points="1 4 1 10 7 10"></polyline><path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"></path>',"rotate-cw":'<polyline points="23 4 23 10 17 10"></polyline><path d="M20.49 15a9 9 0 1 1-2.12-9.36L23 10"></path>',rss:'<path d="M4 11a9 9 0 0 1 9 9"></path><path d="M4 4a16 16 0 0 1 16 16"></path><circle cx="5" cy="19" r="1"></circle>',save:'<path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path><polyline points="17 21 17 13 7 13 7 21"></polyline><polyline points="7 3 7 8 15 8"></polyline>',scissors:'<circle cx="6" cy="6" r="3"></circle><circle cx="6" cy="18" r="3"></circle><line x1="20" y1="4" x2="8.12" y2="15.88"></line><line x1="14.47" y1="14.48" x2="20" y2="20"></line><line x1="8.12" y1="8.12" x2="12" y2="12"></line>',search:'<circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line>',send:'<line x1="22" y1="2" x2="11" y2="13"></line><polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>',server:'<rect x="2" y="2" width="20" height="8" rx="2" ry="2"></rect><rect x="2" y="14" width="20" height="8" rx="2" ry="2"></rect><line x1="6" y1="6" x2="6.01" y2="6"></line><line x1="6" y1="18" x2="6.01" y2="18"></line>',settings:'<circle cx="12" cy="12" r="3"></circle><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>',"share-2":'<circle cx="18" cy="5" r="3"></circle><circle cx="6" cy="12" r="3"></circle><circle cx="18" cy="19" r="3"></circle><line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line><line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>',share:'<path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"></path><polyline points="16 6 12 2 8 6"></polyline><line x1="12" y1="2" x2="12" y2="15"></line>',"shield-off":'<path d="M19.69 14a6.9 6.9 0 0 0 .31-2V5l-8-3-3.16 1.18"></path><path d="M4.73 4.73L4 5v7c0 6 8 10 8 10a20.29 20.29 0 0 0 5.62-4.38"></path><line x1="1" y1="1" x2="23" y2="23"></line>',shield:'<path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>',"shopping-bag":'<path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"></path><line x1="3" y1="6" x2="21" y2="6"></line><path d="M16 10a4 4 0 0 1-8 0"></path>',"shopping-cart":'<circle cx="9" cy="21" r="1"></circle><circle cx="20" cy="21" r="1"></circle><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>',shuffle:'<polyline points="16 3 21 3 21 8"></polyline><line x1="4" y1="20" x2="21" y2="3"></line><polyline points="21 16 21 21 16 21"></polyline><line x1="15" y1="15" x2="21" y2="21"></line><line x1="4" y1="4" x2="9" y2="9"></line>',sidebar:'<rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="9" y1="3" x2="9" y2="21"></line>',"skip-back":'<polygon points="19 20 9 12 19 4 19 20"></polygon><line x1="5" y1="19" x2="5" y2="5"></line>',"skip-forward":'<polygon points="5 4 15 12 5 20 5 4"></polygon><line x1="19" y1="5" x2="19" y2="19"></line>',slack:'<path d="M14.5 10c-.83 0-1.5-.67-1.5-1.5v-5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5v5c0 .83-.67 1.5-1.5 1.5z"></path><path d="M20.5 10H19V8.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5-.67 1.5-1.5 1.5z"></path><path d="M9.5 14c.83 0 1.5.67 1.5 1.5v5c0 .83-.67 1.5-1.5 1.5S8 21.33 8 20.5v-5c0-.83.67-1.5 1.5-1.5z"></path><path d="M3.5 14H5v1.5c0 .83-.67 1.5-1.5 1.5S2 16.33 2 15.5 2.67 14 3.5 14z"></path><path d="M14 14.5c0-.83.67-1.5 1.5-1.5h5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5h-5c-.83 0-1.5-.67-1.5-1.5z"></path><path d="M15.5 19H14v1.5c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5-.67-1.5-1.5-1.5z"></path><path d="M10 9.5C10 8.67 9.33 8 8.5 8h-5C2.67 8 2 8.67 2 9.5S2.67 11 3.5 11h5c.83 0 1.5-.67 1.5-1.5z"></path><path d="M8.5 5H10V3.5C10 2.67 9.33 2 8.5 2S7 2.67 7 3.5 7.67 5 8.5 5z"></path>',slash:'<circle cx="12" cy="12" r="10"></circle><line x1="4.93" y1="4.93" x2="19.07" y2="19.07"></line>',sliders:'<line x1="4" y1="21" x2="4" y2="14"></line><line x1="4" y1="10" x2="4" y2="3"></line><line x1="12" y1="21" x2="12" y2="12"></line><line x1="12" y1="8" x2="12" y2="3"></line><line x1="20" y1="21" x2="20" y2="16"></line><line x1="20" y1="12" x2="20" y2="3"></line><line x1="1" y1="14" x2="7" y2="14"></line><line x1="9" y1="8" x2="15" y2="8"></line><line x1="17" y1="16" x2="23" y2="16"></line>',smartphone:'<rect x="5" y="2" width="14" height="20" rx="2" ry="2"></rect><line x1="12" y1="18" x2="12.01" y2="18"></line>',smile:'<circle cx="12" cy="12" r="10"></circle><path d="M8 14s1.5 2 4 2 4-2 4-2"></path><line x1="9" y1="9" x2="9.01" y2="9"></line><line x1="15" y1="9" x2="15.01" y2="9"></line>',speaker:'<rect x="4" y="2" width="16" height="20" rx="2" ry="2"></rect><circle cx="12" cy="14" r="4"></circle><line x1="12" y1="6" x2="12.01" y2="6"></line>',square:'<rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>',star:'<polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>',"stop-circle":'<circle cx="12" cy="12" r="10"></circle><rect x="9" y="9" width="6" height="6"></rect>',sun:'<circle cx="12" cy="12" r="5"></circle><line x1="12" y1="1" x2="12" y2="3"></line><line x1="12" y1="21" x2="12" y2="23"></line><line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line><line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line><line x1="1" y1="12" x2="3" y2="12"></line><line x1="21" y1="12" x2="23" y2="12"></line><line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line><line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>',sunrise:'<path d="M17 18a5 5 0 0 0-10 0"></path><line x1="12" y1="2" x2="12" y2="9"></line><line x1="4.22" y1="10.22" x2="5.64" y2="11.64"></line><line x1="1" y1="18" x2="3" y2="18"></line><line x1="21" y1="18" x2="23" y2="18"></line><line x1="18.36" y1="11.64" x2="19.78" y2="10.22"></line><line x1="23" y1="22" x2="1" y2="22"></line><polyline points="8 6 12 2 16 6"></polyline>',sunset:'<path d="M17 18a5 5 0 0 0-10 0"></path><line x1="12" y1="9" x2="12" y2="2"></line><line x1="4.22" y1="10.22" x2="5.64" y2="11.64"></line><line x1="1" y1="18" x2="3" y2="18"></line><line x1="21" y1="18" x2="23" y2="18"></line><line x1="18.36" y1="11.64" x2="19.78" y2="10.22"></line><line x1="23" y1="22" x2="1" y2="22"></line><polyline points="16 5 12 9 8 5"></polyline>',table:'<path d="M9 3H5a2 2 0 0 0-2 2v4m6-6h10a2 2 0 0 1 2 2v4M9 3v18m0 0h10a2 2 0 0 0 2-2V9M9 21H5a2 2 0 0 1-2-2V9m0 0h18"></path>',tablet:'<rect x="4" y="2" width="16" height="20" rx="2" ry="2"></rect><line x1="12" y1="18" x2="12.01" y2="18"></line>',tag:'<path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"></path><line x1="7" y1="7" x2="7.01" y2="7"></line>',target:'<circle cx="12" cy="12" r="10"></circle><circle cx="12" cy="12" r="6"></circle><circle cx="12" cy="12" r="2"></circle>',terminal:'<polyline points="4 17 10 11 4 5"></polyline><line x1="12" y1="19" x2="20" y2="19"></line>',thermometer:'<path d="M14 14.76V3.5a2.5 2.5 0 0 0-5 0v11.26a4.5 4.5 0 1 0 5 0z"></path>',"thumbs-down":'<path d="M10 15v4a3 3 0 0 0 3 3l4-9V2H5.72a2 2 0 0 0-2 1.7l-1.38 9a2 2 0 0 0 2 2.3zm7-13h2.67A2.31 2.31 0 0 1 22 4v7a2.31 2.31 0 0 1-2.33 2H17"></path>',"thumbs-up":'<path d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"></path>',"toggle-left":'<rect x="1" y="5" width="22" height="14" rx="7" ry="7"></rect><circle cx="8" cy="12" r="3"></circle>',"toggle-right":'<rect x="1" y="5" width="22" height="14" rx="7" ry="7"></rect><circle cx="16" cy="12" r="3"></circle>',tool:'<path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"></path>',"trash-2":'<polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line>',trash:'<polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>',trello:'<rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><rect x="7" y="7" width="3" height="9"></rect><rect x="14" y="7" width="3" height="5"></rect>',"trending-down":'<polyline points="23 18 13.5 8.5 8.5 13.5 1 6"></polyline><polyline points="17 18 23 18 23 12"></polyline>',"trending-up":'<polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline><polyline points="17 6 23 6 23 12"></polyline>',triangle:'<path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>',truck:'<rect x="1" y="3" width="15" height="13"></rect><polygon points="16 8 20 8 23 11 23 16 16 16 16 8"></polygon><circle cx="5.5" cy="18.5" r="2.5"></circle><circle cx="18.5" cy="18.5" r="2.5"></circle>',tv:'<rect x="2" y="7" width="20" height="15" rx="2" ry="2"></rect><polyline points="17 2 12 7 7 2"></polyline>',twitch:'<path d="M21 2H3v16h5v4l4-4h5l4-4V2zM11 11V7M16 11V7"></path>',twitter:'<path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"></path>',type:'<polyline points="4 7 4 4 20 4 20 7"></polyline><line x1="9" y1="20" x2="15" y2="20"></line><line x1="12" y1="4" x2="12" y2="20"></line>',umbrella:'<path d="M23 12a11.05 11.05 0 0 0-22 0zm-5 7a3 3 0 0 1-6 0v-7"></path>',underline:'<path d="M6 3v7a6 6 0 0 0 6 6 6 6 0 0 0 6-6V3"></path><line x1="4" y1="21" x2="20" y2="21"></line>',unlock:'<rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect><path d="M7 11V7a5 5 0 0 1 9.9-1"></path>',"upload-cloud":'<polyline points="16 16 12 12 8 16"></polyline><line x1="12" y1="12" x2="12" y2="21"></line><path d="M20.39 18.39A5 5 0 0 0 18 9h-1.26A8 8 0 1 0 3 16.3"></path><polyline points="16 16 12 12 8 16"></polyline>',upload:'<path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="17 8 12 3 7 8"></polyline><line x1="12" y1="3" x2="12" y2="15"></line>',"user-check":'<path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path><circle cx="8.5" cy="7" r="4"></circle><polyline points="17 11 19 13 23 9"></polyline>',"user-minus":'<path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path><circle cx="8.5" cy="7" r="4"></circle><line x1="23" y1="11" x2="17" y2="11"></line>',"user-plus":'<path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path><circle cx="8.5" cy="7" r="4"></circle><line x1="20" y1="8" x2="20" y2="14"></line><line x1="23" y1="11" x2="17" y2="11"></line>',"user-x":'<path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path><circle cx="8.5" cy="7" r="4"></circle><line x1="18" y1="8" x2="23" y2="13"></line><line x1="23" y1="8" x2="18" y2="13"></line>',user:'<path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle>',users:'<path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M23 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path>',"video-off":'<path d="M16 16v1a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V7a2 2 0 0 1 2-2h2m5.66 0H14a2 2 0 0 1 2 2v3.34l1 1L23 7v10"></path><line x1="1" y1="1" x2="23" y2="23"></line>',video:'<polygon points="23 7 16 12 23 17 23 7"></polygon><rect x="1" y="5" width="15" height="14" rx="2" ry="2"></rect>',voicemail:'<circle cx="5.5" cy="11.5" r="4.5"></circle><circle cx="18.5" cy="11.5" r="4.5"></circle><line x1="5.5" y1="16" x2="18.5" y2="16"></line>',"volume-1":'<polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon><path d="M15.54 8.46a5 5 0 0 1 0 7.07"></path>',"volume-2":'<polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon><path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"></path>',"volume-x":'<polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon><line x1="23" y1="9" x2="17" y2="15"></line><line x1="17" y1="9" x2="23" y2="15"></line>',volume:'<polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon>',watch:'<circle cx="12" cy="12" r="7"></circle><polyline points="12 9 12 12 13.5 13.5"></polyline><path d="M16.51 17.35l-.35 3.83a2 2 0 0 1-2 1.82H9.83a2 2 0 0 1-2-1.82l-.35-3.83m.01-10.7l.35-3.83A2 2 0 0 1 9.83 1h4.35a2 2 0 0 1 2 1.82l.35 3.83"></path>',"wifi-off":'<line x1="1" y1="1" x2="23" y2="23"></line><path d="M16.72 11.06A10.94 10.94 0 0 1 19 12.55"></path><path d="M5 12.55a10.94 10.94 0 0 1 5.17-2.39"></path><path d="M10.71 5.05A16 16 0 0 1 22.58 9"></path><path d="M1.42 9a15.91 15.91 0 0 1 4.7-2.88"></path><path d="M8.53 16.11a6 6 0 0 1 6.95 0"></path><line x1="12" y1="20" x2="12.01" y2="20"></line>',wifi:'<path d="M5 12.55a11 11 0 0 1 14.08 0"></path><path d="M1.42 9a16 16 0 0 1 21.16 0"></path><path d="M8.53 16.11a6 6 0 0 1 6.95 0"></path><line x1="12" y1="20" x2="12.01" y2="20"></line>',wind:'<path d="M9.59 4.59A2 2 0 1 1 11 8H2m10.59 11.41A2 2 0 1 0 14 16H2m15.73-8.27A2.5 2.5 0 1 1 19.5 12H2"></path>',"x-circle":'<circle cx="12" cy="12" r="10"></circle><line x1="15" y1="9" x2="9" y2="15"></line><line x1="9" y1="9" x2="15" y2="15"></line>',"x-octagon":'<polygon points="7.86 2 16.14 2 22 7.86 22 16.14 16.14 22 7.86 22 2 16.14 2 7.86 7.86 2"></polygon><line x1="15" y1="9" x2="9" y2="15"></line><line x1="9" y1="9" x2="15" y2="15"></line>',"x-square":'<rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="9" y1="9" x2="15" y2="15"></line><line x1="15" y1="9" x2="9" y2="15"></line>',x:'<line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line>',youtube:'<path d="M22.54 6.42a2.78 2.78 0 0 0-1.94-2C18.88 4 12 4 12 4s-6.88 0-8.6.46a2.78 2.78 0 0 0-1.94 2A29 29 0 0 0 1 11.75a29 29 0 0 0 .46 5.33A2.78 2.78 0 0 0 3.4 19c1.72.46 8.6.46 8.6.46s6.88 0 8.6-.46a2.78 2.78 0 0 0 1.94-2 29 29 0 0 0 .46-5.25 29 29 0 0 0-.46-5.33z"></path><polygon points="9.75 15.02 15.5 11.75 9.75 8.48 9.75 15.02"></polygon>',"zap-off":'<polyline points="12.41 6.75 13 2 10.57 4.92"></polyline><polyline points="18.57 12.91 21 10 15.66 10"></polyline><polyline points="8 8 3 14 12 14 11 22 16 16"></polyline><line x1="1" y1="1" x2="23" y2="23"></line>',zap:'<polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"></polygon>',"zoom-in":'<circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line><line x1="11" y1="8" x2="11" y2="14"></line><line x1="8" y1="11" x2="14" y2="11"></line>',"zoom-out":'<circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line><line x1="8" y1="11" x2="14" y2="11"></line>'}},"./node_modules/classnames/dedupe.js":function(a,n,e){var t,i;(function(){"use strict";var o=function(){function l(){}l.prototype=Object.create(null);function s(f,y){for(var x=y.length,m=0;m<x;++m)u(f,y[m])}var r={}.hasOwnProperty;function c(f,y){f[y]=!0}function d(f,y){for(var x in y)r.call(y,x)&&(f[x]=!!y[x])}var p=/\s+/;function h(f,y){for(var x=y.split(p),m=x.length,M=0;M<m;++M)f[x[M]]=!0}function u(f,y){if(y){var x=typeof y;x==="string"?h(f,y):Array.isArray(y)?s(f,y):x==="object"?d(f,y):x==="number"&&c(f,y)}}function g(){for(var f=arguments.length,y=Array(f),x=0;x<f;x++)y[x]=arguments[x];var m=new l;s(m,y);var M=[];for(var j in m)m[j]&&M.push(j);return M.join(" ")}return g}();typeof a!="undefined"&&a.exports?a.exports=o:(t=[],i=function(){return o}.apply(n,t),i!==void 0&&(a.exports=i))})()},"./node_modules/core-js/es/array/from.js":function(a,n,e){e("./node_modules/core-js/modules/es.string.iterator.js"),e("./node_modules/core-js/modules/es.array.from.js");var t=e("./node_modules/core-js/internals/path.js");a.exports=t.Array.from},"./node_modules/core-js/internals/a-function.js":function(a,n){a.exports=function(e){if(typeof e!="function")throw TypeError(String(e)+" is not a function");return e}},"./node_modules/core-js/internals/an-object.js":function(a,n,e){var t=e("./node_modules/core-js/internals/is-object.js");a.exports=function(i){if(!t(i))throw TypeError(String(i)+" is not an object");return i}},"./node_modules/core-js/internals/array-from.js":function(a,n,e){"use strict";var t=e("./node_modules/core-js/internals/bind-context.js"),i=e("./node_modules/core-js/internals/to-object.js"),o=e("./node_modules/core-js/internals/call-with-safe-iteration-closing.js"),l=e("./node_modules/core-js/internals/is-array-iterator-method.js"),s=e("./node_modules/core-js/internals/to-length.js"),r=e("./node_modules/core-js/internals/create-property.js"),c=e("./node_modules/core-js/internals/get-iterator-method.js");a.exports=function(p){var h=i(p),u=typeof this=="function"?this:Array,g=arguments.length,f=g>1?arguments[1]:void 0,y=f!==void 0,x=0,m=c(h),M,j,b,A;if(y&&(f=t(f,g>2?arguments[2]:void 0,2)),m!=null&&!(u==Array&&l(m)))for(A=m.call(h),j=new u;!(b=A.next()).done;x++)r(j,x,y?o(A,f,[b.value,x],!0):b.value);else for(M=s(h.length),j=new u(M);M>x;x++)r(j,x,y?f(h[x],x):h[x]);return j.length=x,j}},"./node_modules/core-js/internals/array-includes.js":function(a,n,e){var t=e("./node_modules/core-js/internals/to-indexed-object.js"),i=e("./node_modules/core-js/internals/to-length.js"),o=e("./node_modules/core-js/internals/to-absolute-index.js");a.exports=function(l){return function(s,r,c){var d=t(s),p=i(d.length),h=o(c,p),u;if(l&&r!=r){for(;p>h;)if(u=d[h++],u!=u)return!0}else for(;p>h;h++)if((l||h in d)&&d[h]===r)return l||h||0;return!l&&-1}}},"./node_modules/core-js/internals/bind-context.js":function(a,n,e){var t=e("./node_modules/core-js/internals/a-function.js");a.exports=function(i,o,l){if(t(i),o===void 0)return i;switch(l){case 0:return function(){return i.call(o)};case 1:return function(s){return i.call(o,s)};case 2:return function(s,r){return i.call(o,s,r)};case 3:return function(s,r,c){return i.call(o,s,r,c)}}return function(){return i.apply(o,arguments)}}},"./node_modules/core-js/internals/call-with-safe-iteration-closing.js":function(a,n,e){var t=e("./node_modules/core-js/internals/an-object.js");a.exports=function(i,o,l,s){try{return s?o(t(l)[0],l[1]):o(l)}catch(c){var r=i.return;throw r!==void 0&&t(r.call(i)),c}}},"./node_modules/core-js/internals/check-correctness-of-iteration.js":function(a,n,e){var t=e("./node_modules/core-js/internals/well-known-symbol.js"),i=t("iterator"),o=!1;try{var l=0,s={next:function(){return{done:!!l++}},return:function(){o=!0}};s[i]=function(){return this},Array.from(s,function(){throw 2})}catch(r){}a.exports=function(r,c){if(!c&&!o)return!1;var d=!1;try{var p={};p[i]=function(){return{next:function(){return{done:d=!0}}}},r(p)}catch(h){}return d}},"./node_modules/core-js/internals/classof-raw.js":function(a,n){var e={}.toString;a.exports=function(t){return e.call(t).slice(8,-1)}},"./node_modules/core-js/internals/classof.js":function(a,n,e){var t=e("./node_modules/core-js/internals/classof-raw.js"),i=e("./node_modules/core-js/internals/well-known-symbol.js"),o=i("toStringTag"),l=t(function(){return arguments}())=="Arguments",s=function(r,c){try{return r[c]}catch(d){}};a.exports=function(r){var c,d,p;return r===void 0?"Undefined":r===null?"Null":typeof(d=s(c=Object(r),o))=="string"?d:l?t(c):(p=t(c))=="Object"&&typeof c.callee=="function"?"Arguments":p}},"./node_modules/core-js/internals/copy-constructor-properties.js":function(a,n,e){var t=e("./node_modules/core-js/internals/has.js"),i=e("./node_modules/core-js/internals/own-keys.js"),o=e("./node_modules/core-js/internals/object-get-own-property-descriptor.js"),l=e("./node_modules/core-js/internals/object-define-property.js");a.exports=function(s,r){for(var c=i(r),d=l.f,p=o.f,h=0;h<c.length;h++){var u=c[h];t(s,u)||d(s,u,p(r,u))}}},"./node_modules/core-js/internals/correct-prototype-getter.js":function(a,n,e){var t=e("./node_modules/core-js/internals/fails.js");a.exports=!t(function(){function i(){}return i.prototype.constructor=null,Object.getPrototypeOf(new i)!==i.prototype})},"./node_modules/core-js/internals/create-iterator-constructor.js":function(a,n,e){"use strict";var t=e("./node_modules/core-js/internals/iterators-core.js").IteratorPrototype,i=e("./node_modules/core-js/internals/object-create.js"),o=e("./node_modules/core-js/internals/create-property-descriptor.js"),l=e("./node_modules/core-js/internals/set-to-string-tag.js"),s=e("./node_modules/core-js/internals/iterators.js"),r=function(){return this};a.exports=function(c,d,p){var h=d+" Iterator";return c.prototype=i(t,{next:o(1,p)}),l(c,h,!1,!0),s[h]=r,c}},"./node_modules/core-js/internals/create-property-descriptor.js":function(a,n){a.exports=function(e,t){return{enumerable:!(e&1),configurable:!(e&2),writable:!(e&4),value:t}}},"./node_modules/core-js/internals/create-property.js":function(a,n,e){"use strict";var t=e("./node_modules/core-js/internals/to-primitive.js"),i=e("./node_modules/core-js/internals/object-define-property.js"),o=e("./node_modules/core-js/internals/create-property-descriptor.js");a.exports=function(l,s,r){var c=t(s);c in l?i.f(l,c,o(0,r)):l[c]=r}},"./node_modules/core-js/internals/define-iterator.js":function(a,n,e){"use strict";var t=e("./node_modules/core-js/internals/export.js"),i=e("./node_modules/core-js/internals/create-iterator-constructor.js"),o=e("./node_modules/core-js/internals/object-get-prototype-of.js"),l=e("./node_modules/core-js/internals/object-set-prototype-of.js"),s=e("./node_modules/core-js/internals/set-to-string-tag.js"),r=e("./node_modules/core-js/internals/hide.js"),c=e("./node_modules/core-js/internals/redefine.js"),d=e("./node_modules/core-js/internals/well-known-symbol.js"),p=e("./node_modules/core-js/internals/is-pure.js"),h=e("./node_modules/core-js/internals/iterators.js"),u=e("./node_modules/core-js/internals/iterators-core.js"),g=u.IteratorPrototype,f=u.BUGGY_SAFARI_ITERATORS,y=d("iterator"),x="keys",m="values",M="entries",j=function(){return this};a.exports=function(b,A,S,Ve,E,Ee,pe){i(S,A,Ve);var K=function(C){if(C===E&&k)return k;if(!f&&C in P)return P[C];switch(C){case x:return function(){return new S(this,C)};case m:return function(){return new S(this,C)};case M:return function(){return new S(this,C)}}return function(){return new S(this)}},he=A+" Iterator",le=!1,P=b.prototype,z=P[y]||P["@@iterator"]||E&&P[E],k=!f&&z||K(E),ue=A=="Array"&&P.entries||z,T,U,G;if(ue&&(T=o(ue.call(new b)),g!==Object.prototype&&T.next&&(!p&&o(T)!==g&&(l?l(T,g):typeof T[y]!="function"&&r(T,y,j)),s(T,he,!0,!0),p&&(h[he]=j))),E==m&&z&&z.name!==m&&(le=!0,k=function(){return z.call(this)}),(!p||pe)&&P[y]!==k&&r(P,y,k),h[A]=k,E)if(U={values:K(m),keys:Ee?k:K(x),entries:K(M)},pe)for(G in U)(f||le||!(G in P))&&c(P,G,U[G]);else t({target:A,proto:!0,forced:f||le},U);return U}},"./node_modules/core-js/internals/descriptors.js":function(a,n,e){var t=e("./node_modules/core-js/internals/fails.js");a.exports=!t(function(){return Object.defineProperty({},"a",{get:function(){return 7}}).a!=7})},"./node_modules/core-js/internals/document-create-element.js":function(a,n,e){var t=e("./node_modules/core-js/internals/global.js"),i=e("./node_modules/core-js/internals/is-object.js"),o=t.document,l=i(o)&&i(o.createElement);a.exports=function(s){return l?o.createElement(s):{}}},"./node_modules/core-js/internals/enum-bug-keys.js":function(a,n){a.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},"./node_modules/core-js/internals/export.js":function(a,n,e){var t=e("./node_modules/core-js/internals/global.js"),i=e("./node_modules/core-js/internals/object-get-own-property-descriptor.js").f,o=e("./node_modules/core-js/internals/hide.js"),l=e("./node_modules/core-js/internals/redefine.js"),s=e("./node_modules/core-js/internals/set-global.js"),r=e("./node_modules/core-js/internals/copy-constructor-properties.js"),c=e("./node_modules/core-js/internals/is-forced.js");a.exports=function(d,p){var h=d.target,u=d.global,g=d.stat,f,y,x,m,M,j;if(u?y=t:g?y=t[h]||s(h,{}):y=(t[h]||{}).prototype,y)for(x in p){if(M=p[x],d.noTargetGet?(j=i(y,x),m=j&&j.value):m=y[x],f=c(u?x:h+(g?".":"#")+x,d.forced),!f&&m!==void 0){if(typeof M==typeof m)continue;r(M,m)}(d.sham||m&&m.sham)&&o(M,"sham",!0),l(y,x,M,d)}}},"./node_modules/core-js/internals/fails.js":function(a,n){a.exports=function(e){try{return!!e()}catch(t){return!0}}},"./node_modules/core-js/internals/function-to-string.js":function(a,n,e){var t=e("./node_modules/core-js/internals/shared.js");a.exports=t("native-function-to-string",Function.toString)},"./node_modules/core-js/internals/get-iterator-method.js":function(a,n,e){var t=e("./node_modules/core-js/internals/classof.js"),i=e("./node_modules/core-js/internals/iterators.js"),o=e("./node_modules/core-js/internals/well-known-symbol.js"),l=o("iterator");a.exports=function(s){if(s!=null)return s[l]||s["@@iterator"]||i[t(s)]}},"./node_modules/core-js/internals/global.js":function(a,n,e){(function(t){var i="object",o=function(l){return l&&l.Math==Math&&l};a.exports=o(typeof globalThis==i&&globalThis)||o(typeof window==i&&window)||o(typeof self==i&&self)||o(typeof t==i&&t)||Function("return this")()}).call(this,e("./node_modules/webpack/buildin/global.js"))},"./node_modules/core-js/internals/has.js":function(a,n){var e={}.hasOwnProperty;a.exports=function(t,i){return e.call(t,i)}},"./node_modules/core-js/internals/hidden-keys.js":function(a,n){a.exports={}},"./node_modules/core-js/internals/hide.js":function(a,n,e){var t=e("./node_modules/core-js/internals/descriptors.js"),i=e("./node_modules/core-js/internals/object-define-property.js"),o=e("./node_modules/core-js/internals/create-property-descriptor.js");a.exports=t?function(l,s,r){return i.f(l,s,o(1,r))}:function(l,s,r){return l[s]=r,l}},"./node_modules/core-js/internals/html.js":function(a,n,e){var t=e("./node_modules/core-js/internals/global.js"),i=t.document;a.exports=i&&i.documentElement},"./node_modules/core-js/internals/ie8-dom-define.js":function(a,n,e){var t=e("./node_modules/core-js/internals/descriptors.js"),i=e("./node_modules/core-js/internals/fails.js"),o=e("./node_modules/core-js/internals/document-create-element.js");a.exports=!t&&!i(function(){return Object.defineProperty(o("div"),"a",{get:function(){return 7}}).a!=7})},"./node_modules/core-js/internals/indexed-object.js":function(a,n,e){var t=e("./node_modules/core-js/internals/fails.js"),i=e("./node_modules/core-js/internals/classof-raw.js"),o="".split;a.exports=t(function(){return!Object("z").propertyIsEnumerable(0)})?function(l){return i(l)=="String"?o.call(l,""):Object(l)}:Object},"./node_modules/core-js/internals/internal-state.js":function(a,n,e){var t=e("./node_modules/core-js/internals/native-weak-map.js"),i=e("./node_modules/core-js/internals/global.js"),o=e("./node_modules/core-js/internals/is-object.js"),l=e("./node_modules/core-js/internals/hide.js"),s=e("./node_modules/core-js/internals/has.js"),r=e("./node_modules/core-js/internals/shared-key.js"),c=e("./node_modules/core-js/internals/hidden-keys.js"),d=i.WeakMap,p,h,u,g=function(b){return u(b)?h(b):p(b,{})},f=function(b){return function(A){var S;if(!o(A)||(S=h(A)).type!==b)throw TypeError("Incompatible receiver, "+b+" required");return S}};if(t){var y=new d,x=y.get,m=y.has,M=y.set;p=function(b,A){return M.call(y,b,A),A},h=function(b){return x.call(y,b)||{}},u=function(b){return m.call(y,b)}}else{var j=r("state");c[j]=!0,p=function(b,A){return l(b,j,A),A},h=function(b){return s(b,j)?b[j]:{}},u=function(b){return s(b,j)}}a.exports={set:p,get:h,has:u,enforce:g,getterFor:f}},"./node_modules/core-js/internals/is-array-iterator-method.js":function(a,n,e){var t=e("./node_modules/core-js/internals/well-known-symbol.js"),i=e("./node_modules/core-js/internals/iterators.js"),o=t("iterator"),l=Array.prototype;a.exports=function(s){return s!==void 0&&(i.Array===s||l[o]===s)}},"./node_modules/core-js/internals/is-forced.js":function(a,n,e){var t=e("./node_modules/core-js/internals/fails.js"),i=/#|\.prototype\./,o=function(d,p){var h=s[l(d)];return h==c?!0:h==r?!1:typeof p=="function"?t(p):!!p},l=o.normalize=function(d){return String(d).replace(i,".").toLowerCase()},s=o.data={},r=o.NATIVE="N",c=o.POLYFILL="P";a.exports=o},"./node_modules/core-js/internals/is-object.js":function(a,n){a.exports=function(e){return typeof e=="object"?e!==null:typeof e=="function"}},"./node_modules/core-js/internals/is-pure.js":function(a,n){a.exports=!1},"./node_modules/core-js/internals/iterators-core.js":function(a,n,e){"use strict";var t=e("./node_modules/core-js/internals/object-get-prototype-of.js"),i=e("./node_modules/core-js/internals/hide.js"),o=e("./node_modules/core-js/internals/has.js"),l=e("./node_modules/core-js/internals/well-known-symbol.js"),s=e("./node_modules/core-js/internals/is-pure.js"),r=l("iterator"),c=!1,d=function(){return this},p,h,u;[].keys&&(u=[].keys(),"next"in u?(h=t(t(u)),h!==Object.prototype&&(p=h)):c=!0),p==null&&(p={}),!s&&!o(p,r)&&i(p,r,d),a.exports={IteratorPrototype:p,BUGGY_SAFARI_ITERATORS:c}},"./node_modules/core-js/internals/iterators.js":function(a,n){a.exports={}},"./node_modules/core-js/internals/native-symbol.js":function(a,n,e){var t=e("./node_modules/core-js/internals/fails.js");a.exports=!!Object.getOwnPropertySymbols&&!t(function(){return!String(Symbol())})},"./node_modules/core-js/internals/native-weak-map.js":function(a,n,e){var t=e("./node_modules/core-js/internals/global.js"),i=e("./node_modules/core-js/internals/function-to-string.js"),o=t.WeakMap;a.exports=typeof o=="function"&&/native code/.test(i.call(o))},"./node_modules/core-js/internals/object-create.js":function(a,n,e){var t=e("./node_modules/core-js/internals/an-object.js"),i=e("./node_modules/core-js/internals/object-define-properties.js"),o=e("./node_modules/core-js/internals/enum-bug-keys.js"),l=e("./node_modules/core-js/internals/hidden-keys.js"),s=e("./node_modules/core-js/internals/html.js"),r=e("./node_modules/core-js/internals/document-create-element.js"),c=e("./node_modules/core-js/internals/shared-key.js"),d=c("IE_PROTO"),p="prototype",h=function(){},u=function(){var g=r("iframe"),f=o.length,y="<",x="script",m=">",M="java"+x+":",j;for(g.style.display="none",s.appendChild(g),g.src=String(M),j=g.contentWindow.document,j.open(),j.write(y+x+m+"document.F=Object"+y+"/"+x+m),j.close(),u=j.F;f--;)delete u[p][o[f]];return u()};a.exports=Object.create||function(f,y){var x;return f!==null?(h[p]=t(f),x=new h,h[p]=null,x[d]=f):x=u(),y===void 0?x:i(x,y)},l[d]=!0},"./node_modules/core-js/internals/object-define-properties.js":function(a,n,e){var t=e("./node_modules/core-js/internals/descriptors.js"),i=e("./node_modules/core-js/internals/object-define-property.js"),o=e("./node_modules/core-js/internals/an-object.js"),l=e("./node_modules/core-js/internals/object-keys.js");a.exports=t?Object.defineProperties:function(r,c){o(r);for(var d=l(c),p=d.length,h=0,u;p>h;)i.f(r,u=d[h++],c[u]);return r}},"./node_modules/core-js/internals/object-define-property.js":function(a,n,e){var t=e("./node_modules/core-js/internals/descriptors.js"),i=e("./node_modules/core-js/internals/ie8-dom-define.js"),o=e("./node_modules/core-js/internals/an-object.js"),l=e("./node_modules/core-js/internals/to-primitive.js"),s=Object.defineProperty;n.f=t?s:function(c,d,p){if(o(c),d=l(d,!0),o(p),i)try{return s(c,d,p)}catch(h){}if("get"in p||"set"in p)throw TypeError("Accessors not supported");return"value"in p&&(c[d]=p.value),c}},"./node_modules/core-js/internals/object-get-own-property-descriptor.js":function(a,n,e){var t=e("./node_modules/core-js/internals/descriptors.js"),i=e("./node_modules/core-js/internals/object-property-is-enumerable.js"),o=e("./node_modules/core-js/internals/create-property-descriptor.js"),l=e("./node_modules/core-js/internals/to-indexed-object.js"),s=e("./node_modules/core-js/internals/to-primitive.js"),r=e("./node_modules/core-js/internals/has.js"),c=e("./node_modules/core-js/internals/ie8-dom-define.js"),d=Object.getOwnPropertyDescriptor;n.f=t?d:function(h,u){if(h=l(h),u=s(u,!0),c)try{return d(h,u)}catch(g){}if(r(h,u))return o(!i.f.call(h,u),h[u])}},"./node_modules/core-js/internals/object-get-own-property-names.js":function(a,n,e){var t=e("./node_modules/core-js/internals/object-keys-internal.js"),i=e("./node_modules/core-js/internals/enum-bug-keys.js"),o=i.concat("length","prototype");n.f=Object.getOwnPropertyNames||function(s){return t(s,o)}},"./node_modules/core-js/internals/object-get-own-property-symbols.js":function(a,n){n.f=Object.getOwnPropertySymbols},"./node_modules/core-js/internals/object-get-prototype-of.js":function(a,n,e){var t=e("./node_modules/core-js/internals/has.js"),i=e("./node_modules/core-js/internals/to-object.js"),o=e("./node_modules/core-js/internals/shared-key.js"),l=e("./node_modules/core-js/internals/correct-prototype-getter.js"),s=o("IE_PROTO"),r=Object.prototype;a.exports=l?Object.getPrototypeOf:function(c){return c=i(c),t(c,s)?c[s]:typeof c.constructor=="function"&&c instanceof c.constructor?c.constructor.prototype:c instanceof Object?r:null}},"./node_modules/core-js/internals/object-keys-internal.js":function(a,n,e){var t=e("./node_modules/core-js/internals/has.js"),i=e("./node_modules/core-js/internals/to-indexed-object.js"),o=e("./node_modules/core-js/internals/array-includes.js"),l=e("./node_modules/core-js/internals/hidden-keys.js"),s=o(!1);a.exports=function(r,c){var d=i(r),p=0,h=[],u;for(u in d)!t(l,u)&&t(d,u)&&h.push(u);for(;c.length>p;)t(d,u=c[p++])&&(~s(h,u)||h.push(u));return h}},"./node_modules/core-js/internals/object-keys.js":function(a,n,e){var t=e("./node_modules/core-js/internals/object-keys-internal.js"),i=e("./node_modules/core-js/internals/enum-bug-keys.js");a.exports=Object.keys||function(l){return t(l,i)}},"./node_modules/core-js/internals/object-property-is-enumerable.js":function(a,n,e){"use strict";var t={}.propertyIsEnumerable,i=Object.getOwnPropertyDescriptor,o=i&&!t.call({1:2},1);n.f=o?function(s){var r=i(this,s);return!!r&&r.enumerable}:t},"./node_modules/core-js/internals/object-set-prototype-of.js":function(a,n,e){var t=e("./node_modules/core-js/internals/validate-set-prototype-of-arguments.js");a.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var i=!1,o={},l;try{l=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set,l.call(o,[]),i=o instanceof Array}catch(s){}return function(r,c){return t(r,c),i?l.call(r,c):r.__proto__=c,r}}():void 0)},"./node_modules/core-js/internals/own-keys.js":function(a,n,e){var t=e("./node_modules/core-js/internals/global.js"),i=e("./node_modules/core-js/internals/object-get-own-property-names.js"),o=e("./node_modules/core-js/internals/object-get-own-property-symbols.js"),l=e("./node_modules/core-js/internals/an-object.js"),s=t.Reflect;a.exports=s&&s.ownKeys||function(c){var d=i.f(l(c)),p=o.f;return p?d.concat(p(c)):d}},"./node_modules/core-js/internals/path.js":function(a,n,e){a.exports=e("./node_modules/core-js/internals/global.js")},"./node_modules/core-js/internals/redefine.js":function(a,n,e){var t=e("./node_modules/core-js/internals/global.js"),i=e("./node_modules/core-js/internals/shared.js"),o=e("./node_modules/core-js/internals/hide.js"),l=e("./node_modules/core-js/internals/has.js"),s=e("./node_modules/core-js/internals/set-global.js"),r=e("./node_modules/core-js/internals/function-to-string.js"),c=e("./node_modules/core-js/internals/internal-state.js"),d=c.get,p=c.enforce,h=String(r).split("toString");i("inspectSource",function(u){return r.call(u)}),(a.exports=function(u,g,f,y){var x=y?!!y.unsafe:!1,m=y?!!y.enumerable:!1,M=y?!!y.noTargetGet:!1;if(typeof f=="function"&&(typeof g=="string"&&!l(f,"name")&&o(f,"name",g),p(f).source=h.join(typeof g=="string"?g:"")),u===t){m?u[g]=f:s(g,f);return}else x?!M&&u[g]&&(m=!0):delete u[g];m?u[g]=f:o(u,g,f)})(Function.prototype,"toString",function(){return typeof this=="function"&&d(this).source||r.call(this)})},"./node_modules/core-js/internals/require-object-coercible.js":function(a,n){a.exports=function(e){if(e==null)throw TypeError("Can't call method on "+e);return e}},"./node_modules/core-js/internals/set-global.js":function(a,n,e){var t=e("./node_modules/core-js/internals/global.js"),i=e("./node_modules/core-js/internals/hide.js");a.exports=function(o,l){try{i(t,o,l)}catch(s){t[o]=l}return l}},"./node_modules/core-js/internals/set-to-string-tag.js":function(a,n,e){var t=e("./node_modules/core-js/internals/object-define-property.js").f,i=e("./node_modules/core-js/internals/has.js"),o=e("./node_modules/core-js/internals/well-known-symbol.js"),l=o("toStringTag");a.exports=function(s,r,c){s&&!i(s=c?s:s.prototype,l)&&t(s,l,{configurable:!0,value:r})}},"./node_modules/core-js/internals/shared-key.js":function(a,n,e){var t=e("./node_modules/core-js/internals/shared.js"),i=e("./node_modules/core-js/internals/uid.js"),o=t("keys");a.exports=function(l){return o[l]||(o[l]=i(l))}},"./node_modules/core-js/internals/shared.js":function(a,n,e){var t=e("./node_modules/core-js/internals/global.js"),i=e("./node_modules/core-js/internals/set-global.js"),o=e("./node_modules/core-js/internals/is-pure.js"),l="__core-js_shared__",s=t[l]||i(l,{});(a.exports=function(r,c){return s[r]||(s[r]=c!==void 0?c:{})})("versions",[]).push({version:"3.1.3",mode:o?"pure":"global",copyright:"\xA9 2019 Denis Pushkarev (zloirock.ru)"})},"./node_modules/core-js/internals/string-at.js":function(a,n,e){var t=e("./node_modules/core-js/internals/to-integer.js"),i=e("./node_modules/core-js/internals/require-object-coercible.js");a.exports=function(o,l,s){var r=String(i(o)),c=t(l),d=r.length,p,h;return c<0||c>=d?s?"":void 0:(p=r.charCodeAt(c),p<55296||p>56319||c+1===d||(h=r.charCodeAt(c+1))<56320||h>57343?s?r.charAt(c):p:s?r.slice(c,c+2):(p-55296<<10)+(h-56320)+65536)}},"./node_modules/core-js/internals/to-absolute-index.js":function(a,n,e){var t=e("./node_modules/core-js/internals/to-integer.js"),i=Math.max,o=Math.min;a.exports=function(l,s){var r=t(l);return r<0?i(r+s,0):o(r,s)}},"./node_modules/core-js/internals/to-indexed-object.js":function(a,n,e){var t=e("./node_modules/core-js/internals/indexed-object.js"),i=e("./node_modules/core-js/internals/require-object-coercible.js");a.exports=function(o){return t(i(o))}},"./node_modules/core-js/internals/to-integer.js":function(a,n){var e=Math.ceil,t=Math.floor;a.exports=function(i){return isNaN(i=+i)?0:(i>0?t:e)(i)}},"./node_modules/core-js/internals/to-length.js":function(a,n,e){var t=e("./node_modules/core-js/internals/to-integer.js"),i=Math.min;a.exports=function(o){return o>0?i(t(o),9007199254740991):0}},"./node_modules/core-js/internals/to-object.js":function(a,n,e){var t=e("./node_modules/core-js/internals/require-object-coercible.js");a.exports=function(i){return Object(t(i))}},"./node_modules/core-js/internals/to-primitive.js":function(a,n,e){var t=e("./node_modules/core-js/internals/is-object.js");a.exports=function(i,o){if(!t(i))return i;var l,s;if(o&&typeof(l=i.toString)=="function"&&!t(s=l.call(i))||typeof(l=i.valueOf)=="function"&&!t(s=l.call(i))||!o&&typeof(l=i.toString)=="function"&&!t(s=l.call(i)))return s;throw TypeError("Can't convert object to primitive value")}},"./node_modules/core-js/internals/uid.js":function(a,n){var e=0,t=Math.random();a.exports=function(i){return"Symbol(".concat(i===void 0?"":i,")_",(++e+t).toString(36))}},"./node_modules/core-js/internals/validate-set-prototype-of-arguments.js":function(a,n,e){var t=e("./node_modules/core-js/internals/is-object.js"),i=e("./node_modules/core-js/internals/an-object.js");a.exports=function(o,l){if(i(o),!t(l)&&l!==null)throw TypeError("Can't set "+String(l)+" as a prototype")}},"./node_modules/core-js/internals/well-known-symbol.js":function(a,n,e){var t=e("./node_modules/core-js/internals/global.js"),i=e("./node_modules/core-js/internals/shared.js"),o=e("./node_modules/core-js/internals/uid.js"),l=e("./node_modules/core-js/internals/native-symbol.js"),s=t.Symbol,r=i("wks");a.exports=function(c){return r[c]||(r[c]=l&&s[c]||(l?s:o)("Symbol."+c))}},"./node_modules/core-js/modules/es.array.from.js":function(a,n,e){var t=e("./node_modules/core-js/internals/export.js"),i=e("./node_modules/core-js/internals/array-from.js"),o=e("./node_modules/core-js/internals/check-correctness-of-iteration.js"),l=!o(function(s){Array.from(s)});t({target:"Array",stat:!0,forced:l},{from:i})},"./node_modules/core-js/modules/es.string.iterator.js":function(a,n,e){"use strict";var t=e("./node_modules/core-js/internals/string-at.js"),i=e("./node_modules/core-js/internals/internal-state.js"),o=e("./node_modules/core-js/internals/define-iterator.js"),l="String Iterator",s=i.set,r=i.getterFor(l);o(String,"String",function(c){s(this,{type:l,string:String(c),index:0})},function(){var d=r(this),p=d.string,h=d.index,u;return h>=p.length?{value:void 0,done:!0}:(u=t(p,h,!0),d.index+=u.length,{value:u,done:!1})})},"./node_modules/webpack/buildin/global.js":function(a,n){var e;e=function(){return this}();try{e=e||Function("return this")()||(0,eval)("this")}catch(t){typeof window=="object"&&(e=window)}a.exports=e},"./src/default-attrs.json":function(a){a.exports={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":2,"stroke-linecap":"round","stroke-linejoin":"round"}},"./src/icon.js":function(a,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var t=Object.assign||function(u){for(var g=1;g<arguments.length;g++){var f=arguments[g];for(var y in f)Object.prototype.hasOwnProperty.call(f,y)&&(u[y]=f[y])}return u},i=function(){function u(g,f){for(var y=0;y<f.length;y++){var x=f[y];x.enumerable=x.enumerable||!1,x.configurable=!0,"value"in x&&(x.writable=!0),Object.defineProperty(g,x.key,x)}}return function(g,f,y){return f&&u(g.prototype,f),y&&u(g,y),g}}(),o=e("./node_modules/classnames/dedupe.js"),l=c(o),s=e("./src/default-attrs.json"),r=c(s);function c(u){return u&&u.__esModule?u:{default:u}}function d(u,g){if(!(u instanceof g))throw new TypeError("Cannot call a class as a function")}var p=function(){function u(g,f){var y=arguments.length>2&&arguments[2]!==void 0?arguments[2]:[];d(this,u),this.name=g,this.contents=f,this.tags=y,this.attrs=t({},r.default,{class:"feather feather-"+g})}return i(u,[{key:"toSvg",value:function(){var f=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},y=t({},this.attrs,f,{class:(0,l.default)(this.attrs.class,f.class)});return"<svg "+h(y)+">"+this.contents+"</svg>"}},{key:"toString",value:function(){return this.contents}}]),u}();function h(u){return Object.keys(u).map(function(g){return g+'="'+u[g]+'"'}).join(" ")}n.default=p},"./src/icons.js":function(a,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var t=e("./src/icon.js"),i=c(t),o=e("./dist/icons.json"),l=c(o),s=e("./src/tags.json"),r=c(s);function c(d){return d&&d.__esModule?d:{default:d}}n.default=Object.keys(l.default).map(function(d){return new i.default(d,l.default[d],r.default[d])}).reduce(function(d,p){return d[p.name]=p,d},{})},"./src/index.js":function(a,n,e){"use strict";var t=e("./src/icons.js"),i=c(t),o=e("./src/to-svg.js"),l=c(o),s=e("./src/replace.js"),r=c(s);function c(d){return d&&d.__esModule?d:{default:d}}a.exports={icons:i.default,toSvg:l.default,replace:r.default}},"./src/replace.js":function(a,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var t=Object.assign||function(h){for(var u=1;u<arguments.length;u++){var g=arguments[u];for(var f in g)Object.prototype.hasOwnProperty.call(g,f)&&(h[f]=g[f])}return h},i=e("./node_modules/classnames/dedupe.js"),o=r(i),l=e("./src/icons.js"),s=r(l);function r(h){return h&&h.__esModule?h:{default:h}}function c(){var h=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(typeof document=="undefined")throw new Error("`feather.replace()` only works in a browser environment.");var u=document.querySelectorAll("[data-feather]");Array.from(u).forEach(function(g){return d(g,h)})}function d(h){var u=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},g=p(h),f=g["data-feather"];delete g["data-feather"];var y=s.default[f].toSvg(t({},u,g,{class:(0,o.default)(u.class,g.class)})),x=new DOMParser().parseFromString(y,"image/svg+xml"),m=x.querySelector("svg");h.parentNode.replaceChild(m,h)}function p(h){return Array.from(h.attributes).reduce(function(u,g){return u[g.name]=g.value,u},{})}n.default=c},"./src/tags.json":function(a){a.exports={activity:["pulse","health","action","motion"],airplay:["stream","cast","mirroring"],"alert-circle":["warning","alert","danger"],"alert-octagon":["warning","alert","danger"],"alert-triangle":["warning","alert","danger"],"align-center":["text alignment","center"],"align-justify":["text alignment","justified"],"align-left":["text alignment","left"],"align-right":["text alignment","right"],anchor:[],archive:["index","box"],"at-sign":["mention","at","email","message"],award:["achievement","badge"],aperture:["camera","photo"],"bar-chart":["statistics","diagram","graph"],"bar-chart-2":["statistics","diagram","graph"],battery:["power","electricity"],"battery-charging":["power","electricity"],bell:["alarm","notification","sound"],"bell-off":["alarm","notification","silent"],bluetooth:["wireless"],"book-open":["read","library"],book:["read","dictionary","booklet","magazine","library"],bookmark:["read","clip","marker","tag"],box:["cube"],briefcase:["work","bag","baggage","folder"],calendar:["date"],camera:["photo"],cast:["chromecast","airplay"],"chevron-down":["expand"],"chevron-up":["collapse"],circle:["off","zero","record"],clipboard:["copy"],clock:["time","watch","alarm"],"cloud-drizzle":["weather","shower"],"cloud-lightning":["weather","bolt"],"cloud-rain":["weather"],"cloud-snow":["weather","blizzard"],cloud:["weather"],codepen:["logo"],codesandbox:["logo"],code:["source","programming"],coffee:["drink","cup","mug","tea","cafe","hot","beverage"],columns:["layout"],command:["keyboard","cmd","terminal","prompt"],compass:["navigation","safari","travel","direction"],copy:["clone","duplicate"],"corner-down-left":["arrow","return"],"corner-down-right":["arrow"],"corner-left-down":["arrow"],"corner-left-up":["arrow"],"corner-right-down":["arrow"],"corner-right-up":["arrow"],"corner-up-left":["arrow"],"corner-up-right":["arrow"],cpu:["processor","technology"],"credit-card":["purchase","payment","cc"],crop:["photo","image"],crosshair:["aim","target"],database:["storage","memory"],delete:["remove"],disc:["album","cd","dvd","music"],"dollar-sign":["currency","money","payment"],droplet:["water"],edit:["pencil","change"],"edit-2":["pencil","change"],"edit-3":["pencil","change"],eye:["view","watch"],"eye-off":["view","watch","hide","hidden"],"external-link":["outbound"],facebook:["logo","social"],"fast-forward":["music"],figma:["logo","design","tool"],"file-minus":["delete","remove","erase"],"file-plus":["add","create","new"],"file-text":["data","txt","pdf"],film:["movie","video"],filter:["funnel","hopper"],flag:["report"],"folder-minus":["directory"],"folder-plus":["directory"],folder:["directory"],framer:["logo","design","tool"],frown:["emoji","face","bad","sad","emotion"],gift:["present","box","birthday","party"],"git-branch":["code","version control"],"git-commit":["code","version control"],"git-merge":["code","version control"],"git-pull-request":["code","version control"],github:["logo","version control"],gitlab:["logo","version control"],globe:["world","browser","language","translate"],"hard-drive":["computer","server","memory","data"],hash:["hashtag","number","pound"],headphones:["music","audio","sound"],heart:["like","love","emotion"],"help-circle":["question mark"],hexagon:["shape","node.js","logo"],home:["house","living"],image:["picture"],inbox:["email"],instagram:["logo","camera"],key:["password","login","authentication","secure"],layers:["stack"],layout:["window","webpage"],"life-buoy":["help","life ring","support"],link:["chain","url"],"link-2":["chain","url"],linkedin:["logo","social media"],list:["options"],lock:["security","password","secure"],"log-in":["sign in","arrow","enter"],"log-out":["sign out","arrow","exit"],mail:["email","message"],"map-pin":["location","navigation","travel","marker"],map:["location","navigation","travel"],maximize:["fullscreen"],"maximize-2":["fullscreen","arrows","expand"],meh:["emoji","face","neutral","emotion"],menu:["bars","navigation","hamburger"],"message-circle":["comment","chat"],"message-square":["comment","chat"],"mic-off":["record","sound","mute"],mic:["record","sound","listen"],minimize:["exit fullscreen","close"],"minimize-2":["exit fullscreen","arrows","close"],minus:["subtract"],monitor:["tv","screen","display"],moon:["dark","night"],"more-horizontal":["ellipsis"],"more-vertical":["ellipsis"],"mouse-pointer":["arrow","cursor"],move:["arrows"],music:["note"],navigation:["location","travel"],"navigation-2":["location","travel"],octagon:["stop"],package:["box","container"],paperclip:["attachment"],pause:["music","stop"],"pause-circle":["music","audio","stop"],"pen-tool":["vector","drawing"],percent:["discount"],"phone-call":["ring"],"phone-forwarded":["call"],"phone-incoming":["call"],"phone-missed":["call"],"phone-off":["call","mute"],"phone-outgoing":["call"],phone:["call"],play:["music","start"],"pie-chart":["statistics","diagram"],"play-circle":["music","start"],plus:["add","new"],"plus-circle":["add","new"],"plus-square":["add","new"],pocket:["logo","save"],power:["on","off"],printer:["fax","office","device"],radio:["signal"],"refresh-cw":["synchronise","arrows"],"refresh-ccw":["arrows"],repeat:["loop","arrows"],rewind:["music"],"rotate-ccw":["arrow"],"rotate-cw":["arrow"],rss:["feed","subscribe"],save:["floppy disk"],scissors:["cut"],search:["find","magnifier","magnifying glass"],send:["message","mail","email","paper airplane","paper aeroplane"],settings:["cog","edit","gear","preferences"],"share-2":["network","connections"],shield:["security","secure"],"shield-off":["security","insecure"],"shopping-bag":["ecommerce","cart","purchase","store"],"shopping-cart":["ecommerce","cart","purchase","store"],shuffle:["music"],"skip-back":["music"],"skip-forward":["music"],slack:["logo"],slash:["ban","no"],sliders:["settings","controls"],smartphone:["cellphone","device"],smile:["emoji","face","happy","good","emotion"],speaker:["audio","music"],star:["bookmark","favorite","like"],"stop-circle":["media","music"],sun:["brightness","weather","light"],sunrise:["weather","time","morning","day"],sunset:["weather","time","evening","night"],tablet:["device"],tag:["label"],target:["logo","bullseye"],terminal:["code","command line","prompt"],thermometer:["temperature","celsius","fahrenheit","weather"],"thumbs-down":["dislike","bad","emotion"],"thumbs-up":["like","good","emotion"],"toggle-left":["on","off","switch"],"toggle-right":["on","off","switch"],tool:["settings","spanner"],trash:["garbage","delete","remove","bin"],"trash-2":["garbage","delete","remove","bin"],triangle:["delta"],truck:["delivery","van","shipping","transport","lorry"],tv:["television","stream"],twitch:["logo"],twitter:["logo","social"],type:["text"],umbrella:["rain","weather"],unlock:["security"],"user-check":["followed","subscribed"],"user-minus":["delete","remove","unfollow","unsubscribe"],"user-plus":["new","add","create","follow","subscribe"],"user-x":["delete","remove","unfollow","unsubscribe","unavailable"],user:["person","account"],users:["group"],"video-off":["camera","movie","film"],video:["camera","movie","film"],voicemail:["phone"],volume:["music","sound","mute"],"volume-1":["music","sound"],"volume-2":["music","sound"],"volume-x":["music","sound","mute"],watch:["clock","time"],"wifi-off":["disabled"],wifi:["connection","signal","wireless"],wind:["weather","air"],"x-circle":["cancel","close","delete","remove","times","clear"],"x-octagon":["delete","stop","alert","warning","times","clear"],"x-square":["cancel","close","delete","remove","times","clear"],x:["cancel","close","delete","remove","times","clear"],youtube:["logo","video","play"],"zap-off":["flash","camera","lightning"],zap:["flash","camera","lightning"],"zoom-in":["magnifying glass"],"zoom-out":["magnifying glass"]}},"./src/to-svg.js":function(a,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var t=e("./src/icons.js"),i=o(t);function o(s){return s&&s.__esModule?s:{default:s}}function l(s){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(console.warn("feather.toSvg() is deprecated. Please use feather.icons[name].toSvg() instead."),!s)throw new Error("The required `key` (icon name) parameter is missing.");if(!i.default[s])throw new Error("No icon matching '"+s+"'. See the complete list of icons at https://feathericons.com");return i.default[s].toSvg(r)}n.default=l},0:function(a,n,e){e("./node_modules/core-js/es/array/from.js"),a.exports=e("./src/index.js")}})})});var Xe={};We(Xe,{default:()=>oe});module.exports=$e(Xe);var w=require("obsidian");var Ke=Be(ge()),O=require("obsidian");var N=a=>a.match(/\.MD$|\.md$/m)?a.split(/\.MD$|\.md$/m).slice(0,-1).join(".md"):a;var xe=require("obsidian"),Z=class a{static getBlock(n,e,t){var c,d;let i=e.getCursor("to"),o=n.metadataCache.getFileCache(t),l=o==null?void 0:o.sections;if(!l||l.length===0){console.log("error reading FileCache (empty file?)");return}let s=l.findIndex(p=>p.position.start.line>i.line),r=s>0?l[s-1]:l[l.length-1];return(r==null?void 0:r.type)=="list"&&(r=(d=(c=o.listItems)==null?void 0:c.find(p=>p.position.start.line<=i.line&&p.position.end.line>=i.line))!=null?d:r),r}static getIdOfBlock(n,e){let t=e.id;if(t)return t;let i=e.position.end,o={ch:i.col,line:i.line},l=Math.random().toString(36).substring(2,8),s=a.shouldInsertAfter(e)?`

`:" ";return n.replaceRange(`${s}^${l}`,o),l}static shouldInsertAfter(n){if(n.type)return["blockquote","code","table","heading","comment","footnoteDefinition"].includes(n.type)}static getBlockId(n){let e=n.workspace.getActiveViewOfType(xe.MarkdownView);if(e){let t=e.editor,i=e.file,o=this.getBlock(n,t,i);if(o)return this.getIdOfBlock(t,o)}}};var me={openFileOnWrite:!0,openDailyInNewPane:!1,openFileOnWriteInNewPane:!1,openFileWithoutWriteInNewPane:!1,idField:"id",useUID:!1,addFilepathWhenUsingUID:!1,allowEval:!1,includeVaultName:!0,vaultParam:"name"};var ve=require("obsidian");function je(a){let n=a.internalPlugins.plugins["daily-notes"];if(n&&n.enabled)return!0}function Ge(...a){let n=[];for(let t=0,i=a.length;t<i;t++)n=n.concat(a[t].split("/"));let e=[];for(let t=0,i=n.length;t<i;t++){let o=n[t];!o||o==="."||e.push(o)}return n[0]===""&&e.unshift(""),e.join("/")}async function we(a,n){let{format:e,folder:t}=n.internalPlugins.getEnabledPluginById("daily-notes").options,i=a.format(e);return i.endsWith(".md")||(i+=".md"),(0,ve.normalizePath)(Ge(t,i))}var v=require("obsidian");var be=require("obsidian"),L=class extends be.SuggestModal{constructor(e,t){super(e.app);this.file=t;this.modes=[null,"overwrite","append","prepend"];this.plugin=e,this.setPlaceholder("Type your data to be written to the file or leave it empty to just open it")}getSuggestions(e){e==""&&(e=null);let t=[];for(let i of this.modes)if(!(i==="overwrite"&&!e)){let o;e?i?o=`Write "${e}" in ${i} mode`:o=`Write "${e}"`:i?o=`Open in ${i} mode`:o="Open",t.push({data:e,display:o,mode:i,func:()=>{this.file?this.plugin.tools.copyURI({filepath:this.file,data:e,mode:i}):this.plugin.tools.copyURI({daily:"true",data:e,mode:i})}})}return t}renderSuggestion(e,t){t.innerText=e.display}onChooseSuggestion(e,t){e.func()}};var Me=require("obsidian"),R=class extends Me.FuzzySuggestModal{constructor(e,t,i=!0){super(e.app);this.placeHolder=t;this.allowNoFile=i;this.plugin=e,this.setPlaceholder(this.placeHolder)}getItems(){let e=[];this.allowNoFile&&e.push({display:"<Don't specify a file>",source:void 0});let t=this.app.workspace.getActiveFile();return t&&e.push({display:"<Current file>",source:t.path}),[...e,...this.app.vault.getFiles().map(i=>({display:i.path,source:i.path}))]}getItemText(e){return e.display}onChooseItem(e,t){}};var Ae=require("obsidian");function se(a){return a.viewmode?{state:{mode:a.viewmode,source:a.viewmode=="source"}}:void 0}function W(a){return navigator.clipboard.writeText(a)}function J(a,n){var o;let e=(o=n.parent)==null?void 0:o.path,t=e==="/"?"":e,i=n.name;for(let l=1;l<100;l++){let s=N(i),r=t+(t==""?"":"/")+s+` ${l}.md`;if(!(a.vault.getAbstractFileByPath(r)!==null))return r}}function Ie(a,n){let e=new URL(a.vault.getResourcePath(n));return e.host="localhosthostlocal",e.protocol="file",e.search="",e.pathname=decodeURIComponent(e.pathname),e.toString().replace("/localhosthostlocal/","/")}function re(a,n,e){var l,s;let t=a.metadataCache.getFileCache(n),i=t.sections,o=(l=t.headings)==null?void 0:l.find(r=>r.heading===e);if(o){let r=i.findIndex(u=>u.type==="heading"&&u.position.start.line===o.position.start.line),c=i.slice(r+1),d=c==null?void 0:c.findIndex(u=>u.type==="heading");return{lastLine:((s=c[(d!==-1?d:c.length)-1])!=null?s:i[r]).position.end.line+1,firstLine:i[r].position.end.line+1}}else new Ae.Notice("Can't find heading")}var D=class extends Error{constructor(e,t,i){super(e);this.name="KeyPathError",this.errKey=t,i&&(this.cause=i)}};function Fe(a){let{originalObject:n,key:e,data:t}=a;if(e.startsWith("[")&&e.endsWith("]")){let i=e.substring(1,e.length-1).split(","),o=n;for(let l=0;l<i.length;l++){let s=i[l],r;if(o instanceof Array){if(r=parseInt(s),Number.isNaN(r)&&(r=o.find(p=>p==s)),r===void 0)throw new D(`Failed to resolve or convert "${s}" to a valid array index.`,s);r=Math.min(r,o.length),r=Math.max(r,-1)}if(l==i.length-1){o instanceof Array?r===-1?o.unshift(t):o[r]=t:o[s]=t;return}let d=!Number.isNaN(parseInt(i[l+1]))?[]:{};try{o instanceof Array?r>=o.length?(o.push(d),o=d):r===-1?(o.unshift(d),o=d):o=o[Math.max(r,0)]:o[s]===void 0?(o[s]=d,o=d):o=o[s]}catch(p){throw new D(`Failed to resolve "${s}" as a valid object key`,s,p)}}}else n[e]=t}function Pe(a){let{obj:n,key:e}=a;if(e.startsWith("[")&&e.endsWith("]")){let t=e.substring(1,e.length-1).split(","),i=n;for(let o of t)if(i instanceof Array){let l=parseInt(o);Number.isNaN(l)?i=i.find(s=>s==o):i=i[l]}else if(i&&typeof i=="object")i=i[o];else return;return i}else return n?n[e]:void 0}var B=class{constructor(n){this.plugin=n;this.app=this.plugin.app}get tools(){return this.plugin.tools}handlePluginManagement(n){if(n["enable-plugin"]){let e=n["enable-plugin"];e in this.app.plugins.manifests&&!this.app.plugins.getPlugin(e)?(this.app.plugins.enablePluginAndSave(e),new v.Notice(`Enabled ${e}`)):this.app.internalPlugins.plugins[e]&&(this.app.internalPlugins.plugins[e].enable(!0),new v.Notice(`Enabled ${e}`))}else if(n["disable-plugin"]){let e=n["disable-plugin"];this.app.plugins.getPlugin(e)?(this.app.plugins.disablePluginAndSave(e),new v.Notice(`Disabled ${e}`)):this.app.internalPlugins.plugins[e]&&(this.app.internalPlugins.plugins[e].disable(!0),new v.Notice(`Disabled ${e}`))}}handleFrontmatterKey(n){var o;let e=n.frontmatterkey,t=this.app.vault.getAbstractFileByPath((o=n.filepath)!=null?o:this.app.workspace.getActiveFile().path);if(!(t instanceof v.TFile))return;let i=this.app.metadataCache.getFileCache(t).frontmatter;if(n.data){let l=n.data;try{l=JSON.parse(l)}catch(s){try{l=`"${l}"`,l=JSON.parse(l)}catch(r){new v.Notice("Failed to parse data, check console for more details"),console.error(r);return}}this.app.fileManager.processFrontMatter(t,s=>{try{Fe({originalObject:s,key:e,data:l})}catch(r){console.error(r),r instanceof D?new v.Notice(`Invalid key in path.
${r.message}`):new v.Notice("Failed to update frontmatter, check console for more details")}});return}W(Pe({obj:i,key:e}))}handleWorkspace(n){let e=this.app.internalPlugins.getEnabledPluginById("workspaces");if(!e)new v.Notice("Workspaces plugin is not enabled"),this.plugin.failure(n);else{if(n.saveworkspace=="true"){let t=e.activeWorkspace;e.saveWorkspace(t),new v.Notice(`Saved current workspace to ${t}`)}n.clipboard&&n.clipboard!="false"?this.tools.copyURI({workspace:e.activeWorkspace}):n.workspace!=null&&e.loadWorkspace(n.workspace),this.plugin.success(n)}}async handleCommand(n){if(n.filepath)if(n.mode){if(n.mode=="new"){let t=this.app.metadataCache.getFirstLinkpathDest(n.filepath,"/");t instanceof v.TFile&&(n.filepath=J(this.app,t))}await this.plugin.open({file:n.filepath,mode:"source",parameters:n});let e=this.app.workspace.getActiveViewOfType(v.MarkdownView);if(e){let t=e.editor,i=t.getValue();if(n.mode==="append"){t.setValue(i+`
`);let o=t.lineCount();t.setCursor({ch:0,line:o})}else n.mode==="prepend"?(t.setValue(`
`+i),t.setCursor({ch:0,line:0})):n.mode==="overwrite"&&t.setValue("")}}else n.line!=null||n.column!=null||n.offset!=null?(await this.plugin.open({file:n.filepath,mode:"source",parameters:n}),await this.plugin.setCursorInLine(n)):await this.plugin.open({file:n.filepath,setting:this.plugin.settings.openFileWithoutWriteInNewPane,parameters:n});else(n.openmode||n.viewmode)&&await this.plugin.open({parameters:n});if(n.commandid)this.app.commands.executeCommandById(n.commandid);else if(n.commandname){let e=this.app.commands.commands;for(let t in e)if(e[t].name===n.commandname){e[t].callback?await e[t].callback():e[t].checkCallback(!1);break}}if(n.confirm&&n.confirm!="false"){await new Promise(t=>setTimeout(t,750));let e=document.querySelector(".mod-cta:not([style*='display: none'])");e.click instanceof Function&&e.click()}this.plugin.success(n)}async handleEval(n){if(n.filepath)if(n.mode){if(n.mode=="new"){let t=this.app.metadataCache.getFirstLinkpathDest(n.filepath,"/");t instanceof v.TFile&&(n.filepath=J(this.app,t))}await this.plugin.open({file:n.filepath,mode:"source",parameters:n});let e=this.app.workspace.getActiveViewOfType(v.MarkdownView);if(e){let t=e.editor,i=t.getValue();if(n.mode==="append"){t.setValue(i+`
`);let o=t.lineCount();t.setCursor({ch:0,line:o})}else n.mode==="prepend"?(t.setValue(`
`+i),t.setCursor({ch:0,line:0})):n.mode==="overwrite"&&t.setValue("")}}else n.line!=null||n.column!=null||n.offset!=null?(await this.plugin.open({file:n.filepath,mode:"source",parameters:n}),await this.plugin.setCursorInLine(n)):await this.plugin.open({file:n.filepath,setting:this.plugin.settings.openFileWithoutWriteInNewPane,parameters:n});this.plugin.settings.allowEval?((0,eval)(n.eval),this.plugin.success(n)):(new v.Notice("Eval is not allowed. Please enable it in the settings."),this.plugin.failure(n))}async handleDoesFileExist(n){let e=await this.app.vault.adapter.exists(n.filepath);W((e?1:0).toString()),this.plugin.success(n)}async handleSearchAndReplace(n){let e;if(n.filepath){let t=this.app.vault.getAbstractFileByPath(n.filepath);t instanceof v.TFile&&(e=t)}else e=this.app.workspace.getActiveFile();if(e){let t=await this.app.vault.read(e);if(n.searchregex)try{let[,,i,o]=n.searchregex.match(/(\/?)(.+)\1([a-z]*)/i),l=new RegExp(i,o);t=t.replace(l,n.replace),this.plugin.success(n)}catch(i){new v.Notice(`Can't parse ${n.searchregex} as RegEx`),this.plugin.failure(n)}else t=t.replaceAll(n.search,n.replace),this.plugin.success(n);await this.plugin.writeAndOpenFile(e.path,t,n)}else new v.Notice("Cannot find file"),this.plugin.failure(n)}async handleSearch(n){n.filepath&&await this.plugin.open({file:n.filepath,parameters:n});let e=this.app.workspace.getActiveViewOfType(v.FileView);e.currentMode.showSearch();let t=e.currentMode.search;t.searchInputEl.value=n.search,t.searchInputEl.dispatchEvent(new Event("input"))}async handleWrite(n,e=!1){var i;let t;if(n.filepath?t=this.app.vault.getAbstractFileByPath(n.filepath):t=this.app.workspace.getActiveFile(),n.filepath||t){let o,l=(i=n.filepath)!=null?i:t.path;n.mode==="overwrite"?(o=await this.plugin.writeAndOpenFile(l,n.data,n),this.plugin.success(n)):n.mode==="prepend"?(t instanceof v.TFile?o=await this.plugin.prepend(t,n):o=await this.plugin.prepend(l,n),this.plugin.success(n)):n.mode==="append"?(t instanceof v.TFile?o=await this.plugin.append(t,n):o=await this.plugin.append(l,n),this.plugin.success(n)):n.mode==="new"?t instanceof v.TFile?(o=await this.plugin.writeAndOpenFile(J(this.app,t),n.data,n),this.plugin.hookSuccess(n,o)):(o=await this.plugin.writeAndOpenFile(l,n.data,n),this.plugin.hookSuccess(n,o)):!e&&t instanceof v.TFile?(new v.Notice("File already exists"),this.plugin.openExistingFileAndSetCursor(t.path,n),this.plugin.failure(n)):(o=await this.plugin.writeAndOpenFile(l,n.data,n),this.plugin.success(n)),n.uid&&this.tools.writeUIDToFile(o,n.uid)}else new v.Notice("Cannot find file"),this.plugin.failure(n)}async handleOpen(n){if(n.heading!=null){await this.plugin.open({file:n.filepath+"#"+n.heading,setting:this.plugin.settings.openFileWithoutWriteInNewPane,parameters:n});let e=this.app.workspace.getActiveViewOfType(v.MarkdownView);if(!e)return;let i=this.app.metadataCache.getFileCache(e.file).headings.find(o=>o.heading===n.heading);e.editor.focus(),e.editor.setCursor({line:i.position.start.line+1,ch:0})}else if(n.block!=null){await this.plugin.open({file:n.filepath+"#^"+n.block,setting:this.plugin.settings.openFileWithoutWriteInNewPane,parameters:n});let e=this.app.workspace.getActiveViewOfType(v.MarkdownView);if(!e)return;let i=this.app.metadataCache.getFileCache(e.file).blocks[n.block.toLowerCase()];e.editor.focus(),i&&e.editor.setCursor({line:i.position.start.line,ch:0})}else await this.plugin.open({file:n.filepath,setting:this.plugin.settings.openFileWithoutWriteInNewPane,parameters:n}),(n.line!=null||n.column!=null||n.offset!=null)&&await this.plugin.setCursorInLine(n);if(n.mode!=null&&await this.plugin.setCursor(n),n.uid){let e=this.app.workspace.getActiveViewOfType(v.MarkdownView);this.tools.writeUIDToFile(e.file,n.uid)}this.plugin.success(n)}async handleOpenBlock(n){let e=this.tools.getFileFromBlockID(n.block);e&&await this.plugin.chooseHandler({...n,filepath:e.path},!1)}handleCopyFileURI(n,e){let t=this.app.workspace.getActiveViewOfType(v.FileView);if(!(!t&&!e)){if(t instanceof v.MarkdownView){let i=t.editor.getCursor(),o=this.app.metadataCache.getFileCache(t.file);if(o.headings){for(let l of o.headings)if(l.position.start.line<=i.line&&l.position.end.line>=i.line){this.tools.copyURI({filepath:t.file.path,heading:l.heading});return}}if(o.blocks)for(let l of Object.keys(o.blocks)){let s=o.blocks[l];if(s.position.start.line<=i.line&&s.position.end.line>=i.line){this.tools.copyURI({filepath:t.file.path,block:s.id});return}}}if(n){let i=e!=null?e:this.app.workspace.getActiveFile();if(!i){new v.Notice("No file opened");return}this.tools.copyURI({filepath:i.path})}else{let i=new R(this.plugin,"Choose a file",!1);i.open(),i.onChooseItem=(o,l)=>{new L(this.plugin,o.source).open()}}}}handleOpenSettings(n){if(this.app.setting.containerEl.parentElement===null&&this.app.setting.open(),n.settingid=="plugin-browser"?(this.app.setting.openTabById("community-plugins"),this.app.setting.activeTab.containerEl.find(".mod-cta").click()):n.settingid=="theme-browser"?(this.app.setting.openTabById("appearance"),this.app.setting.activeTab.containerEl.find(".mod-cta").click()):this.app.setting.openTabById(n.settingid),n.settingsection){let e=this.app.setting.tabContentContainer.querySelectorAll("*"),t=Array.prototype.find.call(e,i=>i.textContent==n.settingsection);t&&t.scrollIntoView()}this.plugin.success(n)}async handleUpdatePlugins(n){new v.Notice("Checking for updates\u2026"),await this.app.plugins.checkForUpdates(),Object.keys(this.app.plugins.updates).length>0&&(n.settingid="community-plugins",this.handleOpenSettings(n),this.app.setting.activeTab.containerEl.findAll(".mod-cta").last().click()),this.plugin.success(n)}async handleBookmarks(n){let e=this.app.internalPlugins.getEnabledPluginById("bookmarks"),i=e.getBookmarks().find(l=>l.title==n.bookmark),o;n.openmode=="true"||n.openmode=="false"?o=n.openmode=="true":o=n.openmode,e.openBookmark(i,o)}async handleCanvas(n){n.filepath&&await this.plugin.open({file:n.filepath,setting:this.plugin.settings.openFileWithoutWriteInNewPane,parameters:n});let e=this.app.workspace.activeLeaf.view;if(e.getViewType()!="canvas"){new v.Notice("Active view is not a canvas");return}let t=e;if(n.canvasnodes){let i=n.canvasnodes.split(","),o=t.canvas.nodes,l=i.map(r=>o.get(r)),s=t.canvas.selection;t.canvas.updateSelection(()=>{for(let r of l)s.add(r)}),t.canvas.zoomToSelection()}if(n.canvasviewport){let[i,o,l]=n.canvasviewport.split(",");if(i!="-")if(i.startsWith("--")||i.startsWith("++")){let s=t.canvas.tx+Number(i.substring(1));t.canvas.tx=s}else t.canvas.tx=Number(i);if(o!="-")if(o.startsWith("--")||o.startsWith("++")){let s=t.canvas.ty+Number(o.substring(1));t.canvas.ty=s}else t.canvas.ty=Number(o);if(l!="-")if(l.startsWith("--")||l.startsWith("++")){let s=t.canvas.tZoom+Number(l.substring(1));t.canvas.tZoom=s}else t.canvas.tZoom=Number(l);t.canvas.markViewportChanged()}}};var Se=require("obsidian"),Q=class extends Se.FuzzySuggestModal{constructor(e,t){super(e.app);this.plugin=e,this.file=t}getItems(){let e=this.app.commands.commands;return Object.keys(e).map(i=>({id:e[i].id,name:e[i].name}))}getItemText(e){return e.name}onChooseItem(e,t){this.plugin.tools.copyURI({filepath:this.file,commandid:e.id})}};var Ce=require("obsidian"),X=class extends Ce.SuggestModal{constructor(e,t,i){super(e.app);this.search=t;this.filepath=i;this.emptyText="Empty text (replace with nothing)";this.plugin=e,this.setPlaceholder("Replacement text")}getSuggestions(e){return e===""&&(e=this.emptyText),[e]}renderSuggestion(e,t){t.innerText=e}onChooseSuggestion(e,t){this.search.isRegEx?this.plugin.tools.copyURI({filepath:this.filepath,searchregex:this.search.source,replace:e==this.emptyText?"":e}):this.plugin.tools.copyURI({filepath:this.filepath,search:this.search.source,replace:e==this.emptyText?"":e})}};var Oe=require("obsidian"),q=class extends Oe.SuggestModal{constructor(e){super(e.app);this.plugin=e,this.setPlaceholder("Searched text. RegEx is supported")}getSuggestions(e){e===""&&(e="...");let t;try{t=new RegExp(e)}catch(i){}return[{source:e,isRegEx:!1,display:e},{source:e,display:t?`As RegEx: ${e}`:"Can't parse RegEx",isRegEx:!0}]}renderSuggestion(e,t){t.innerText=e.display}onChooseSuggestion(e,t){}};var F=require("obsidian"),_=class extends F.PluginSettingTab{constructor(e,t){super(e,t);this.plugin=t}display(){let{containerEl:e}=this;e.empty(),e.createEl("h2",{text:this.plugin.manifest.name}),new F.Setting(e).setName("Open file on write").addToggle(t=>t.setValue(this.plugin.settings.openFileOnWrite).onChange(i=>{this.plugin.settings.openFileOnWrite=i,this.plugin.saveSettings()})),new F.Setting(e).setName("Open file on write in a new pane").setDisabled(this.plugin.settings.openFileOnWrite).addToggle(t=>t.setValue(this.plugin.settings.openFileOnWriteInNewPane).onChange(i=>{this.plugin.settings.openFileOnWriteInNewPane=i,this.plugin.saveSettings()})),new F.Setting(e).setName("Open daily note in a new pane").addToggle(t=>t.setValue(this.plugin.settings.openDailyInNewPane).onChange(i=>{this.plugin.settings.openDailyInNewPane=i,this.plugin.saveSettings()})),new F.Setting(e).setName("Open file without write in new pane").addToggle(t=>t.setValue(this.plugin.settings.openFileWithoutWriteInNewPane).onChange(i=>{this.plugin.settings.openFileWithoutWriteInNewPane=i,this.plugin.saveSettings()})),new F.Setting(e).setName("Use UID instead of file paths").addToggle(t=>t.setValue(this.plugin.settings.useUID).onChange(i=>{this.plugin.settings.useUID=i,this.plugin.saveSettings(),this.display()})),new F.Setting(e).setName("Include vault name/ID parameter").addToggle(t=>t.setValue(this.plugin.settings.includeVaultName).onChange(i=>{this.plugin.settings.includeVaultName=i,this.plugin.saveSettings(),this.display()})),this.plugin.settings.includeVaultName&&new F.Setting(e).setName("Vault identifying parameter").setDesc("Choose whether to use the vault Name or its internal ID as the identifying parameter.").addDropdown(t=>t.addOption("name","Name").addOption("id","ID").setValue(this.plugin.settings.vaultParam).onChange(i=>{this.plugin.settings.vaultParam=i,this.plugin.saveSettings()})),this.plugin.settings.useUID&&new F.Setting(e).setName("Add filepath parameter").setDesc("When using UID instead of file paths, you can still add the filepath parameter to know what this URI is about. It's NOT actually used.").addToggle(t=>t.setValue(this.plugin.settings.addFilepathWhenUsingUID).onChange(i=>{this.plugin.settings.addFilepathWhenUsingUID=i,this.plugin.saveSettings()})),new F.Setting(e).setName("UID field in frontmatter").addText(t=>t.setValue(this.plugin.settings.idField).onChange(i=>{this.plugin.settings.idField=i,this.plugin.saveSettings()})),new F.Setting(e).setName("Allow executing arbitrary code via eval").setDesc("\u26A0\uFE0F This can be dangerous as it allows executing arbitrary code. Only enable this if you trust the source of the URIs you are using and know what you are doing. \u26A0\uFE0F").addToggle(t=>t.setValue(this.plugin.settings.allowEval).onChange(i=>{this.plugin.settings.allowEval=i,this.plugin.saveSettings()})),new F.Setting(e).setName("Donate").setDesc("If you like this Plugin, consider donating to support continued development.").addButton(t=>{t.buttonEl.outerHTML="<a href='https://ko-fi.com/F1F195IQ5' target='_blank'><img height='36' style='border:0px;height:36px;' src='https://cdn.ko-fi.com/cdn/kofi3.png?v=3' border='0' alt='Buy Me a Coffee at ko-fi.com' /></a>"})}};var V=require("obsidian");var ee,Ye=new Uint8Array(16);function ce(){if(!ee&&(ee=typeof crypto!="undefined"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||typeof msCrypto!="undefined"&&typeof msCrypto.getRandomValues=="function"&&msCrypto.getRandomValues.bind(msCrypto),!ee))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return ee(Ye)}var ke=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;function Ze(a){return typeof a=="string"&&ke.test(a)}var Te=Ze;var I=[];for(te=0;te<256;++te)I.push((te+256).toString(16).substr(1));var te;function Je(a){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,e=(I[a[n+0]]+I[a[n+1]]+I[a[n+2]]+I[a[n+3]]+"-"+I[a[n+4]]+I[a[n+5]]+"-"+I[a[n+6]]+I[a[n+7]]+"-"+I[a[n+8]]+I[a[n+9]]+"-"+I[a[n+10]]+I[a[n+11]]+I[a[n+12]]+I[a[n+13]]+I[a[n+14]]+I[a[n+15]]).toLowerCase();if(!Te(e))throw TypeError("Stringified UUID is invalid");return e}var Re=Je;function Qe(a,n,e){a=a||{};var t=a.random||(a.rng||ce)();if(t[6]=t[6]&15|64,t[8]=t[8]&63|128,n){e=e||0;for(var i=0;i<16;++i)n[e+i]=t[i];return n}return Re(t)}var de=Qe;var $=class{constructor(n){this.plugin=n;this.app=this.plugin.app}get settings(){return this.plugin.settings}async writeUIDToFile(n,e){var c;let t=(c=this.app.metadataCache.getFileCache(n))==null?void 0:c.frontmatter,i=await this.app.vault.read(n),o=(!t||t.length===0)&&!i.match(/^-{3}\s*\n*\r*-{3}/),l=i.split(`
`),s=`${this.plugin.settings.idField}:`;if(o)l.unshift("---"),l.unshift(`${s} ${e}`),l.unshift("---");else{let d=l.findIndex(p=>p.startsWith(s));d!=-1?l[d]=`${s} ${e}`:l.splice(1,0,`${s} ${e}`)}let r=l.join(`
`);return await this.app.vault.modify(n,r),e}async getUIDFromFile(n){var i;let e=(i=this.app.metadataCache.getFileCache(n))!=null?i:await new Promise(o=>{let l=this.app.metadataCache.on("changed",s=>{if(s.path==n.path){let r=this.app.metadataCache.getFileCache(n);this.app.metadataCache.offref(l),o(r)}})}),t=(0,V.parseFrontMatterEntry)(e.frontmatter,this.plugin.settings.idField);return t!=null?t instanceof Array?t[0]:t:await this.writeUIDToFile(n,de())}async generateURI(n){let e="obsidian://adv-uri",t="",i=this.app.vault.getAbstractFileByPath(n.filepath);this.settings.includeVaultName&&(t+="?vault=",this.settings.vaultParam=="id"&&this.app.appId?t+=encodeURIComponent(this.app.appId):t+=encodeURIComponent(this.app.vault.getName())),this.settings.useUID&&i instanceof V.TFile&&i.extension=="md"&&(this.settings.addFilepathWhenUsingUID||(n.filepath=void 0),n.uid=await this.getUIDFromFile(i));let o=Object.keys(n).filter(l=>n[l]).sort((l,s)=>{let r=["filepath","filename","uid","daily"],c=["data","eval"];return r.includes(l)?-1:r.includes(s)||c.includes(l)?1:c.includes(s)?-1:0});for(let l of o)n[l]!=null&&(t+=t?"&":"?",t+=`${l}=${encodeURIComponent(n[l])}`);return t.endsWith("%20")&&(t+="&"),e+t}async copyURI(n){let e=await this.generateURI(n);await W(e),new V.Notice("Advanced URI copied to your clipboard")}getFileFromUID(n){var i;let e=this.app.vault.getMarkdownFiles(),t=this.settings.idField;for(let o of e){let l=(0,V.parseFrontMatterEntry)((i=this.app.metadataCache.getFileCache(o))==null?void 0:i.frontmatter,t);if(l instanceof Array){if(l.contains(n))return o}else if(l==n)return o}}getFileFromBlockID(n){var t,i;let e=this.app.vault.getMarkdownFiles();n=n.toLowerCase();for(let o of e)if(((i=(t=this.app.metadataCache.getFileCache(o))==null?void 0:t.blocks)==null?void 0:i[n])!=null)return o}};var ie=require("obsidian"),ne=class extends ie.FuzzySuggestModal{constructor(e){super(e.app);this.plugin=e,this.setPlaceholder("Choose a workspace")}getItems(){let e=this.app.internalPlugins.getEnabledPluginById("workspaces");if(!e)new ie.Notice("Workspaces plugin is not enabled");else return Object.keys(e.workspaces)}getItemText(e){return e}onChooseItem(e,t){this.plugin.tools.copyURI({workspace:e})}};var oe=class extends w.Plugin{constructor(){super(...arguments);this.handlers=new B(this);this.tools=new $(this)}async onload(){await this.loadSettings(),this.addSettingTab(new _(this.app,this)),this.addCommand({id:"copy-uri-current-file",name:"Copy URI for file with options",callback:()=>this.handlers.handleCopyFileURI(!1)}),this.addCommand({id:"copy-uri-current-file-simple",name:"Copy URI for current file",callback:()=>this.handlers.handleCopyFileURI(!0)}),this.addCommand({id:"copy-uri-daily",name:"Copy URI for daily note",callback:()=>new L(this).open()}),this.addCommand({id:"copy-uri-search-and-replace",name:"Copy URI for search and replace",callback:()=>{let e=new R(this,"Used file for search and replace");e.open(),e.onChooseItem=t=>{let i=new q(this);i.open(),i.onChooseSuggestion=o=>{new X(this,o,t==null?void 0:t.source).open()}}}}),this.addCommand({id:"copy-uri-command",name:"Copy URI for command",callback:()=>{let e=new R(this,"Select a file to be opened before executing the command");e.open(),e.onChooseItem=t=>{new Q(this,t==null?void 0:t.source).open()}}}),this.addCommand({id:"copy-uri-block",name:"Copy URI for current block",checkCallback:e=>{let t=this.app.workspace.getActiveViewOfType(w.MarkdownView);if(e)return t!=null;let i=Z.getBlockId(this.app);i&&this.tools.copyURI({filepath:t.file.path,block:i})}}),this.addCommand({id:"copy-uri-workspace",name:"Copy URI for workspace",callback:()=>{new ne(this).open()}}),this.addCommand({id:"copy-uri-canvas-node",name:"Copy URI for selected canvas nodes",checkCallback:e=>{let t=this.app.workspace.activeLeaf.view;if(e)return t.getViewType()==="canvas"&&t.canvas.selection.size>0;if(t.getViewType()!=="canvas")return!1;let i=t,o=[];i.canvas.selection.forEach(l=>{o.push(l.id)}),this.tools.copyURI({canvasnodes:o.join(","),filepath:t.file.path})}}),this.addCommand({id:"copy-uri-canvas-viewport",name:"Copy URI for current canvas viewport",checkCallback:e=>{let t=this.app.workspace.activeLeaf.view;if(e)return t.getViewType()==="canvas";if(t.getViewType()!=="canvas")return!1;let o=t.canvas,l=o.tx.toFixed(0),s=o.ty.toFixed(0),r=o.tZoom.toFixed(3);this.tools.copyURI({filepath:t.file.path,canvasviewport:`${l},${s},${r}`})}}),this.registerObsidianProtocolHandler("advanced-uri",async e=>{let t=e;for(let i in t)t[i]=decodeURIComponent(t[i]);this.onUriCall(t)}),this.registerObsidianProtocolHandler("adv-uri",async e=>{let t=e;this.onUriCall(t)}),this.registerObsidianProtocolHandler("hook-get-advanced-uri",async e=>{let t=e;for(let o in t)t[o]=decodeURIComponent(t[o]);let i=this.app.workspace.getActiveFile();i?this.hookSuccess(t,i):this.failure(t,{errorMessage:"No file opened"})}),this.registerEvent(this.app.workspace.on("file-menu",(e,t,i)=>{(i==="more-options"||i==="tab-header"||i=="file-explorer-context-menu")&&t instanceof w.TFile&&e.addItem(o=>{o.setTitle("Copy Advanced URI").setIcon("link").setSection("info").onClick(l=>this.handlers.handleCopyFileURI(!0,t))})}))}async onUriCall(e){var i,o,l;let t=!1;if(this.lastParameters={...e},e.uid){let s=(i=this.tools.getFileFromUID(e.uid))==null?void 0:i.path;s!=null&&(e.filepath=s,e.uid=void 0)}else if(e.filename){let s=this.app.metadataCache.getFirstLinkpathDest(e.filename,"");s||(s=this.app.vault.getMarkdownFiles().find(d=>{var p;return(p=(0,w.parseFrontMatterAliases)(this.app.metadataCache.getFileCache(d).frontmatter))==null?void 0:p.includes(e.filename)}));let r=this.app.fileManager.getNewFileParent((o=this.app.workspace.getActiveFile())==null?void 0:o.path),c=r.isRoot()?"":r.path+"/";e.filepath=(l=s==null?void 0:s.path)!=null?l:c+(0,w.normalizePath)(e.filename)}if(e.filepath){e.filepath=(0,w.normalizePath)(e.filepath);let s=e.filepath.lastIndexOf(".");e.filepath.substring(s<0?e.filepath.length:s)===""&&(e.filepath=e.filepath+".md")}else if(e.daily==="true"){if(!je(this.app)){new w.Notice("Daily notes plugin is not loaded");return}let s=window.moment(Date.now()),r=await we(s,this.app),c=this.app.vault.getAbstractFileByPath(r);c||(e.exists==="true"?e.filepath=r:(c=await this.app.internalPlugins.getEnabledPluginById("daily-notes").getDailyNote(),t=!0)),c&&(e.filepath=c.path)}e.clipboard==="true"&&(e.data=await navigator.clipboard.readText()),this.chooseHandler(e,t)}async chooseHandler(e,t){e["enable-plugin"]||e["disable-plugin"]?this.handlers.handlePluginManagement(e):e.frontmatterkey?this.handlers.handleFrontmatterKey(e):e.workspace||e.saveworkspace=="true"?this.handlers.handleWorkspace(e):e.commandname||e.commandid?this.handlers.handleCommand(e):e.bookmark?this.handlers.handleBookmarks(e):e.eval?this.handlers.handleEval(e):e.filepath&&e.exists==="true"?this.handlers.handleDoesFileExist(e):e.canvasnodes||e.canvasviewport?this.handlers.handleCanvas(e):e.data?this.handlers.handleWrite(e,t):e.filepath&&e.heading?(await this.handlers.handleOpen(e),e.filepath=void 0,e.heading=void 0,this.chooseHandler(e,t)):e.filepath&&e.block?(await this.handlers.handleOpen(e),e.filepath=void 0,e.block=void 0,this.chooseHandler(e,t)):(e.search||e.searchregex)&&e.replace!=null?this.handlers.handleSearchAndReplace(e):e.search?this.handlers.handleSearch(e):e.filepath?this.handlers.handleOpen(e):e.block?this.handlers.handleOpenBlock(e):e.settingid?this.handlers.handleOpenSettings(e):e.updateplugins&&this.handlers.handleUpdatePlugins(e)}async hookSuccess(e,t){if(!e["x-success"])return;let i={title:N(t.name),advanceduri:await this.tools.generateURI({filepath:t.path}),urlkey:"advanceduri",fileuri:Ie(this.app,t)};this.success(e,i)}success(e,t){if(e["x-success"]){let i=new URL(e["x-success"]);for(let o in t)i.searchParams.set(o,t[o]);window.open(i.toString())}}failure(e,t){if(e["x-error"]){let i=new URL(e["x-error"]);for(let o in t)i.searchParams.set(o,t[o]);window.open(i.toString())}}async append(e,t){var l,s;let i,o;if(e instanceof w.TFile){i=e.path;let c=(await this.app.vault.read(e)).split(`
`),d;if(t.heading){let p=re(this.app,e,t.heading);if(d=p==null?void 0:p.lastLine,d===void 0)return;p.firstLine==p.lastLine&&t.separator&&(t.separator=`
`+t.separator)}else t.line?d=Number(t.line):d=c.length;d=Math.max(1,d),c[d-1]=((l=c[d-1])!=null?l:"")+((s=t.separator)!=null?s:`
`)+t.data,o=c.join(`
`)}else i=e,o=t.data;return this.writeAndOpenFile(i,o,t)}async prepend(e,t){var l,s,r;let i,o;if(e instanceof w.TFile){i=e.path;let c=await this.app.vault.read(e),d=this.app.metadataCache.getFileCache(e),p=c.split(`
`),h;if(t.heading){if(h=(l=re(this.app,e,t.heading))==null?void 0:l.firstLine,h===void 0)return;h+=1}else t.line?h=Number(t.line):d.frontmatterPosition?h=d.frontmatterPosition.end.line+2:h=1;h=Math.max(1,h),p[h-1]=`${t.data}${(s=t.separator)!=null?s:`
`}${(r=p[h-1])!=null?r:""}`,o=p.join(`
`)}else i=e,o=t.data;return this.writeAndOpenFile(i,o,t)}async writeAndOpenFile(e,t,i){let o=this.app.vault.getAbstractFileByPath(e);if(o instanceof w.TFile)await this.app.vault.modify(o,t);else{let l=e.split("/"),s=l.slice(0,l.length-1).join("/");l.length>1&&!(this.app.vault.getAbstractFileByPath(s)instanceof w.TFolder)&&await this.app.vault.createFolder(s),/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/.test(t)?await this.app.vault.createBinary(e,(0,w.base64ToArrayBuffer)(t)):await this.app.vault.create(e,t)}return this.openExistingFileAndSetCursor(e,i),this.app.vault.getAbstractFileByPath(e)}async openExistingFileAndSetCursor(e,t){t.openmode!="silent"&&this.settings.openFileOnWrite&&(await this.open({file:e,setting:this.settings.openFileOnWriteInNewPane,parameters:t}),(t.line!=null||t.column!=null||t.offset!=null)&&await this.setCursorInLine(t))}async open({file:e,setting:t,parameters:i,supportPopover:o,mode:l}){var r;let s;if(i.openmode=="popover"&&(o==null||o)){let c=this.app.plugins.plugins["obsidian-hover-editor"];c||(new w.Notice("Cannot find Hover Editor plugin. Please file an issue."),this.failure(i)),await new Promise(d=>{s=c.spawnPopover(void 0,()=>{this.app.workspace.setActiveLeaf(s,{focus:!0}),d()})})}else{let c=t;if(i.newpane!==void 0&&(c=i.newpane=="true"),i.openmode!==void 0&&(i.openmode=="true"||i.openmode=="false"?c=i.openmode=="true":i.openmode=="popover"?c=!1:w.Platform.isMobile&&i.openmode=="window"||(c=i.openmode)),c=="silent")return;if(w.Platform.isMobileApp&&c=="window"&&(c=!0),e!=null){let d=!1;isBoolean(c)&&this.app.workspace.iterateAllLeaves(p=>{var h;if(((h=p.view.file)==null?void 0:h.path)===i.filepath){if(d&&p.width==0)return;d=!0,this.app.workspace.setActiveLeaf(p,{focus:!0}),s=p}})}s||(s=this.app.workspace.getLeaf(c),this.app.workspace.setActiveLeaf(s,{focus:!0}))}if(e instanceof w.TFile?await s.openFile(e):e!=null&&await this.app.workspace.openLinkText(e,"/",!1,l!=null?{state:{mode:l}}:se(i)),s.view instanceof w.MarkdownView){let c=s.getViewState();l!=null?c.state.mode=l:c.state={...c.state,...(r=se(i))==null?void 0:r.state},await s.setViewState(c)}return s}async setCursor(e){let t=this.app.workspace.getActiveViewOfType(w.MarkdownView);if(!t)return;let i=e.mode,o=t.editor,l=t.leaf.getViewState();if(l.state.mode="source",i==="append"){let s=o.lastLine(),r=o.getLine(s).length;await t.leaf.setViewState(l,{focus:!0}),o.setCursor({ch:r,line:s})}else i==="prepend"&&(await t.leaf.setViewState(l,{focus:!0}),o.setCursor({ch:0,line:0}));await new Promise(s=>setTimeout(s,10)),e.viewmode=="preview"&&(l.state.mode="preview",await t.leaf.setViewState(l))}async setCursorInLine(e){let t=this.app.workspace.getActiveViewOfType(w.MarkdownView);if(!t)return;let i=t.leaf.getViewState(),o=e.line!=null?Number(e.line):void 0,l=e.column?Number(e.column):void 0;i.state.mode="source",await t.leaf.setViewState(i);let s,r;if(e.offset!=null){let c=t.editor.offsetToPos(Number(e.offset));s=c.line,r=c.ch}else{s=o!=null?Math.min(o-1,t.editor.lineCount()-1):t.editor.getCursor().line;let c=t.editor.getLine(s).length-1;r=Math.min(l-1,c)}t.editor.focus(),t.editor.setCursor({line:s,ch:r}),t.editor.scrollIntoView({from:{line:s,ch:r},to:{line:s,ch:r}},!0),await new Promise(c=>setTimeout(c,10)),e.viewmode=="preview"&&(i.state.mode="preview",await t.leaf.setViewState(i))}async loadSettings(){this.settings=Object.assign(me,await this.loadData())}async saveSettings(){await this.saveData(this.settings)}};
//! All of these methods are taken from https://www.npmjs.com/package/obsidian-daily-notes-interface.
/*! Bundled license information:

feather-icons/dist/feather.js:
  (*!
    Copyright (c) 2016 Jed Watson.
    Licensed under the MIT License (MIT), see
    http://jedwatson.github.io/classnames
  *)
*/

/* nosourcemap */