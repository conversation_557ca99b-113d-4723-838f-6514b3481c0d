{"path": "2-Areas/Network-Administration/Pasted image 20250805153023.png", "text": "LSccCiclicuaacuon v When this setting is enabled, <PERSON><PERSON> will scan blocks, the contents of Warp Drive objects, and Agent Mode queries for potential sensitive information and prevent saving or sending this data to any servers. You can customize this list via regexes. Hide secrets in block list - Visually hide secrets in the block list while keeping them searchable. Custom secret redaction ey Use regex to define additional secrets or data you'd like to redact. This will take effect when the next command runs. You can use the inline (?i) flag as a prefix to your regex to make it case-insensitive. IPv4 Address X \\b((25[0-5]I(2[0-4]11\\dI[1-9])\\d)\\.2\\b){4}\\b IPv6 Address X \\b((([0-9A-Fa-fl{1,4}:){1,6}:)I(([0-9A-Fa-f{1,4}){7})) ((0-9A-Fa-fl{1,4})\\b Slack App Token X \\bxapp-[0-9]+-[A-Za-z0-9_]+-[0-9]+-[a-f0-9]+\\b Phone Number X \\b(\\+\\d{1,21\\s)?\\(2\\d{3}\\) ?[\\s.-]\\d{3}[\\s.-|\\d{4\"\\b AWS Access ID X \\b(AKIAJAST|AGPA|AIDA|AROA|AIPAJANPAJANVAJASIA)[A-Z0-9){12,\\b MAC Address X \\b((([a-zA-20-9K2}-1){5}([a-2A-20-9K2})) (([a-zA-z0-9){2}:){5}([a-zA-z0-9){2})))\\b Google API Key X \\bAlza[0-9A-Za-z-_{35}\\b Google OAuth ID X \\b[0-9]+-[0-9A-Za-z_]{32}\\.apps\\.googleusercontent\\.com\\b GitHub Classic Personal Access Token X \\bghp_[A-Za-z0-9_{36}\\b GitHub Fine-Grained Personal Access Token X \\bgithub_pat_[A-Za-z0-9_]{82}\\b GitHub OAuth Access Token X \\bgho_[A-Za-z0-9_{36}\\b GitHub User-to-Server Token X \\bghu_[A-Za-z0-9_K{36}\\b GitHub Server-to-Server Token X \\bghs_[A-Za-z0-9_]{36}\\b Stripe Key X \\b(?:r|s)k_(test|live)_[0-9a-zA-Z|{24}\\b Firebase Auth Domain X \\b([a-z0-9-]){1,30}(\\.firebaseapp\\.com)\\b JWT X \\b(ey[a-zA-z0-9_\\-=[{10,}\\.){2}[a-zA-z0-9_\\-={10,\\b (o] LYW NG X \\bsk-[a-zA-Z0-9]{48)\\b Anthropic API Key X \\bsk-ant-api\\d{0,2}-[a-zA-Z0-9\\-{80,120}\\b Generic SK API Key X \\bsk-[a-zA-Z0-9\\-}{10,100)\\b Fireworks API Key X \\bfw_[a-zA-Z0-9]{24)\\b", "libVersion": "0.5.0", "langs": "eng"}