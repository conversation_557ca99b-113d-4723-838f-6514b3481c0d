{"path": "Templates/PaceySpace Wiki/2-Areas/Legal/Templates/Service_Agreement_Template.docx", "text": "Service_Agreement_Template WEB DEVELOPMENT SERVICE AGREEMENT PARTIES Service Provider:<PERSON> trading as PaceySpace DigitalABN: 81 148 180 908Address: 1 Adelaide Drive, Caboolture, Queensland, Australia 4510Email: <EMAIL>: 07 2111 0402 Client:[CLIENT NAME] - [CLIENT BUSINESS TYPE]Contact: [CLIENT CONTACT NAME]Address: [CLIENT ADDRESS]Email: [CLIENT EMAIL]Phone: [CLIENT PHONE] 1. AGREEMENT OVERVIEW This Web Development Service Agreement (\"Agreement\") is entered into on [DATE] between Jordan Phillip Pacey trading as PaceySpace Digital (\"Service Provider\") and [CLIENT NAME] (\"Client\") for the development of a [PROJECT DESCRIPTION]. This Agreement is effective upon the date of execution and shall remain in force until the complete provision of services by the Provider, as detailed herein, and the fulfilment of all corresponding obligations by the Client. The Agreement encompasses the entire understanding and agreement between the parties and supersedes all prior negotiations, understandings, and agreements between the parties. 2. SCOPE OF WORK 2.1 Web Development Services The Service Provider agrees to design, develop, and deploy a complete web solution including: Frontend Development: Responsive website design optimized for all devices and screen sizes [PROJECT SPECIFIC FEATURES] Contact forms and inquiry management system Performance-optimized loading with lazy loading and compression Cross-browser compatibility and accessibility compliance (WCAG 2.1) SEO optimization for search engine visibility Backend Development: [BACKEND TECHNOLOGY] with secure [AUTHENTICATION TYPE] authentication [DATABASE TYPE] database design and implementation with optimized schema User management and role-based authorization system Content management system for [CONTENT] administration Comprehensive error handling, logging, and monitoring API documentation with [DOCUMENTATION TYPE] specifications Cloud Integration & Infrastructure: [STORAGE PROVIDER] integration for [CONTENT] management [CDN PROVIDER] configuration for global content delivery Automated backup and disaster recovery systems SSL certificate implementation and automatic renewal Security hardening and vulnerability protection Additional Services: [SPECIFIC SYSTEM] with rich metadata management [HOSTING TYPE] containerization and deployment on [HOSTING PLATFORM] Comprehensive documentation and training materials 30 days post-launch support and maintenance included 2.2 Technical Specifications Frontend: HTML5, CSS3, [FRONTEND TECHNOLOGY] with responsive design Backend: [BACKEND TECHNOLOGY] with [FRAMEWORK] and [PATTERN] pattern Database: [DATABASE TYPE] with optimized schema and indexing Storage: [STORAGE PROVIDER] with rich metadata management Hosting: [HOSTING PLATFORM] with [CONTAINER TECHNOLOGY] containers Security: [AUTHENTICATION TYPE] authentication, HTTPS, input validation, and data protection 3. PROJECT TIMELINE AND DELIVERABLES 3.1 Development Schedule Total Estimated Duration: [TIMELINE] weeks from contract signing Phase 1 - Planning & Design (Weeks [RANGE]) Requirements analysis and technical specification document Database schema and API design documentation UI/UX wireframes and design mockups for client approval Development environment setup and project initialization Phase 2 - Backend Development (Weeks [RANGE]) Database implementation with migration scripts and seeding API development with authentication and authorization systems [STORAGE/CLOUD] integration and configuration Security implementation and comprehensive testing Phase 3 - Frontend Development (Weeks [RANGE]) Responsive website development with mobile-first approach [MAIN FEATURE] interface with [FUNCTIONALITY] capabilities Admin interface for content management and user administration Integration testing with backend services and APIs Phase 4 - Testing & Deployment (Weeks [RANGE]) Comprehensive testing across all platforms and browsers Performance optimization and security validation Production deployment and configuration on hosting infrastructure Documentation delivery and client training sessions 3.2 Client Responsibilities The Client agrees to: Provide all necessary content, [CONTENT TYPE], and branding materials in a timely manner Review and approve designs, functionality, and deliverables within agreed timeframes Provide constructive and timely feedback on milestone reviews and deliverables Ensure availability for scheduled meetings, reviews, and training sessions Make payments according to the agreed schedule outlined in Section 4 Designate a primary point of contact for all project communications 4. FINANCIAL TERMS 4.1 Project Investment Total Project Cost: $[AMOUNT] AUD (including GST) Development Services: $[AMOUNT MINUS GST] AUD GST (10%): $[GST AMOUNT] AUD 4.2 Payment Schedule Deposit (50%): $[DEPOSIT] AUD - Due upon contract executionFinal Payment (50%): $[FINAL] AUD - Due upon project completion and client acceptance 4.3 Payment Terms All payments due within 14 days of invoice date Late payment fee of $25 AUD per week on overdue amounts Work may be suspended for accounts over 30 days past due All prices are in Australian Dollars (AUD) Service Provider is not GST registered; GST shown for transparency 4.4 Additional Work Any work outside the agreed scope will be documented and quoted separately at the following rates: Development Work: $[HOURLY RATE] AUD per hour depending on complexity Consultation and Support: $[CONSULTATION RATE] AUD per hour Emergency Support: $[EMERGENCY RATE] AUD per hour (outside business hours) Additional Features: Quoted based on complexity and time requirements 5. INTELLECTUAL PROPERTY RIGHTS 5.1 Ownership of Deliverables Upon final payment, Client will own: All custom-developed source code and databases All design elements, graphics, and creative materials All documentation, training materials, and user guides Domain name registration and hosting account credentials All content and data uploaded to the system 5.2 Third-Party Components Open-source libraries and frameworks remain under their respective licenses Third-party services (hosting, storage, CDN) subject to their terms of service Service Provider retains rights to general development methodologies and techniques 5.3 Portfolio and Marketing Rights Service Provider may: Include project in portfolio and marketing materials with client consent Reference Client as a customer in proposals and case studies Use general project details for marketing purposes (anonymized if requested) Showcase technical achievements and methodologies used 6. CONFIDENTIALITY AND DATA PROTECTION 6.1 Confidential Information Both parties agree to maintain strict confidentiality regarding: Proprietary business information, trade secrets, and business strategies Customer data, personal information, and contact details Technical specifications, system architecture, and implementation details Financial information, pricing strategies, and business plans 6.2 Data Protection Compliance Service Provider will: Comply with Australian Privacy Principles and applicable data protection laws Implement appropriate technical and organizational security measures Ensure secure handling, processing, and storage of personal and sensitive data Provide immediate data breach notification within 72 hours if required by law Maintain data processing records and provide access upon reasonable request 6.3 Data Security Measures Encrypted data transmission using TLS/SSL protocols Secure data storage with access controls and authentication Regular security updates and vulnerability assessments Secure backup procedures with encryption and access controls Incident response procedures for security breaches 7. WARRANTIES AND REPRESENTATIONS 7.1 Service Provider Warranties Service Provider warrants that: All work will be performed in a professional and workmanlike manner Deliverables will be free from material defects for 30 days post-delivery All work will comply with applicable laws, regulations, and industry standards No third-party intellectual property rights will be knowingly infringed All team members have appropriate skills and qualifications 7.2 Client Warranties Client warrants that: All provided content is original or properly licensed for use Client has full authority to enter into this agreement All information provided is accurate, complete, and up-to-date Client will not use the system for illegal, harmful, or unethical purposes Client owns or has rights to all content provided for the website 7.3 Disclaimer EXCEPT AS EXPRESSLY SET FORTH HEREIN, SERVICE PROVIDER MAKES NO OTHER WARRANTIES, EXPRESS OR IMPLIED, INCLUDING WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, OR NON-INFRINGEMENT. 8. LIMITATION OF LIABILITY 8.1 Liability Cap Service Provider's total liability under this agreement shall not exceed the total amount paid by Client under this agreement in the 12 months preceding the claim. 8.2 Excluded Damages Service Provider shall not be liable for: Indirect, incidental, consequential, special, or punitive damages Loss of profits, revenue, business opportunities, or anticipated savings Data loss or corruption not directly caused by Service Provider's negligence Damages caused by third-party services, force majeure events, or client actions Business interruption or loss of use beyond Service Provider's control 8.3 Indemnification Client agrees to indemnify and hold harmless Service Provider from claims arising from: Content provided by Client that infringes third-party intellectual property rights Client's use of the system in violation of applicable laws or regulations Modifications made by Client or third parties after project delivery Client's breach of this agreement or violation of third-party terms of service 9. SUPPORT AND MAINTENANCE 9.1 Included Support (30 Days Post-Launch) Bug fixes and error resolution for issues present at delivery Minor content updates and modifications (up to 2 hours) Performance monitoring and basic optimization Security updates and critical patches Technical support via email and phone during business hours 9.2 Ongoing Maintenance (Optional) Monthly Maintenance Package: $[MONTHLY AMOUNT] AUD/month Continued security updates and system monitoring Content updates and minor modifications (up to [HOURS] hours monthly) Performance optimization and detailed reporting Priority technical support with faster response times Monthly performance and security reports 10. TERMINATION 10.1 Termination for Convenience Either party may terminate this agreement with 30 days written notice, provided all work completed to date is paid for. 10.2 Termination for Cause Either party may terminate immediately upon: Material breach of agreement terms with 15 days written cure period Insolvency, bankruptcy, or cessation of business operations Failure to make payments when due after 30 days past due notice 10.3 Effect of Termination Upon termination: Client pays for all work completed and expenses incurred to date Service Provider delivers all completed work, source code, and documentation Both parties return or destroy confidential information as requested Survival of confidentiality, intellectual property, and liability provisions 11. FORCE MAJEURE Neither party shall be liable for delays or failures in performance due to circumstances beyond their reasonable control, including but not limited to acts of God, natural disasters, government actions, pandemics, cyber attacks, or third-party service failures. 12. DISPUTE RESOLUTION 12.1 Governing Law This agreement is governed by the laws of Queensland, Australia, without regard to conflict of law principles. 12.2 Dispute Resolution Process Direct Negotiation: Parties will attempt to resolve disputes through good faith negotiation Mediation: If negotiation fails, disputes will be submitted to binding mediation Arbitration: Unresolved disputes will be settled through binding arbitration Jurisdiction: Queensland courts have exclusive jurisdiction for any legal proceedings 13. GENERAL PROVISIONS 13.1 Entire Agreement This agreement constitutes the entire agreement between the parties and supersedes all prior negotiations, representations, or agreements relating to the subject matter. 13.2 Amendments Any modifications must be in writing and signed by both parties. Oral modifications are not binding. 13.3 Severability If any provision is deemed invalid or unenforceable, the remainder of the agreement remains in full force and effect. 13.4 Assignment Neither party may assign this agreement without the other party's prior written consent, except Service Provider may assign to affiliates or in connection with a sale of business. 13.5 Notices All notices must be in writing and delivered to the addresses specified above or updated addresses provided in writing. 14. ACCEPTANCE AND SIGNATURES By signing below, both parties acknowledge they have read, understood, and agree to be bound by all terms and conditions of this agreement. SERVICE PROVIDER: Jordan Phillip Pacey (PaceySpace Digital)Signature: _Date: _ CLIENT: [CLIENT NAME]Name: _Title: _Signature: _Date: _ This agreement is effective from the date of signing and remains in effect until all obligations are fulfilled by both parties.", "libVersion": "0.5.0", "langs": ""}