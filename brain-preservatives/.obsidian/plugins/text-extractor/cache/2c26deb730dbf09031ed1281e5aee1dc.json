{"path": "Templates/PaceySpace Wiki/2-Areas/Service-Agreements-Contracts/Service_Level_Agreement_CICD-PaceySpace.pdf", "text": "SERVICE LEVEL AGREEMENT (SLA). CI/CD PIPELINE MANAGEMENT AND WEBSITE HOSTING SERVICES AGREEMENT BETWEEN Name & Business: PaceySpace Digital - Jordan Pacey ABN: ************** Address: 1 Adelaide Drive, Caboolture, Queensland, Australia 4510 Email: <EMAIL> Phone: 07 2111 0402 GST Registered: No AND Name & Business: ABN: Address: Email: Phone: 1. SERVICE OVERVIEW: This document constitutes a Service Level Agreement (SLA) between PaceySpace Digital and Yendor Main Coon, effective as of the date of the agreement. This SLA details the terms and conditions under which PaceySpace Digital will provide CI/CD pipeline management and website hosting services for the Yendor Main Coon website. These services will be delivered using the Enhance Control Panel and related infrastructure. This Agreement is established under the laws of Queensland, Australia, and is subject to the Australian Consumer Law. 2. TERM: 2.1: This SLA (Service Level Agreement) shall commence on ______________ and continue for a period of twelve (12) months until the initial term _____________. 2.2: This SLA shall automatically renew for successive twelve (12) month periods unless either party provides written notice of non-renewal at least sixty (60) days prior to the end of the current Term. 3. SERVICES PROVIDED 3.1. CI/CD Pipeline Management SERVICE LEVEL AGREEMENT (SLA). The Service Provider undertakes to perform the following activities: a) Establish and manage a continuous integration and continuous deployment (CI/CD) pipeline for the Yendor Main Coon website, leveraging the capabilities of the Enhance Control Panel. b) Design and implement automated testing protocols, build procedures, and deployment sequences within the CI/CD environment. c) Continuously monitor the CI/CD pipeline for operational anomalies and promptly remediate any identified failures. d) Uphold rigorous version control practices and maintain established code quality assurance measures throughout the CI/CD lifecycle. e) Generate and deliver comprehensive documentation outlining the architecture and configuration of the implemented CI/CD pipeline. f) Apply recognized security best practices in the development and operation of the CI/CD pipeline to protect the integrity and confidentiality of the Client's data and systems. g) Achieve initial operational status of the CI/CD pipeline within a period of ______ days from the Commencement Date. Recognizing the potential for unforeseen delays, a contingency period of fourteen _______ calendar days is hereby provided, subject to prior written notification to the Client detailing the reasons for the potential delay. 3.2. WEBSITE HOSTING SERVICE: The Service Provider shall: Provision and maintain appropriate infrastructure for hosting the Yendor Maine Coon Cat website, including: • Docker containers for all services (API, Database, File Uploader) • MariaDB database for data storage • Backblaze B2 for S3-compatible storage • Cloudflare integration for CDN, WAF, and DNS services b) Configure and maintain HTTPS for secure communication. c) Implement and maintain backup systems for website data. d) Monitor website performance and availability. e) Apply security patches and updates to all infrastructure components. f) Provide DNS management services. g) Complete initial hosting infrastructure setup within _______ days of the commencement date, with a 2-week leeway period for unforeseen circumstances. SERVICE LEVEL AGREEMENT (SLA). 3.3. SCALIBILITY SERVICES The Service Provider shall undertake the following measures to ensure infrastructure scalability: a) Establish and maintain infrastructure capable of effectively managing anticipated traffic surges during peak periods, specifically including, but not limited to, cat breeding seasons and major public announcements related to Yendor Maine Coon Cats. b) Implement and configure auto-scaling functionalities within the hosting environment where technically feasible and deemed beneficial for maintaining service levels.c) Provision and manage replicated server instances or equivalent resources as necessary to ensure consistent performance and availability during periods of heightened website traffic.d) Continuously monitor key resource utilization metrics (e.g., CPU, memory, bandwidth) and proactively scale infrastructure resources in anticipation of increased demand to prevent performance degradation. 4. SERVICE LEVELS a) Website Uptime: The Service Provider warrants a minimum uptime of 99.9% for the Yendor Maine Coon Cat website, with uptime being calculated on a monthly basis. b) The guaranteed uptime specified in section (a) excludes scheduled maintenance periods. The Client shall receive notification of such scheduled maintenance at least forty-eight (48) hours prior to commencement. c) Scheduled maintenance activities will be performed during off-peak hours, specifically between 23:00 and 05:00 Australian Eastern Standard Time (AEST), unless otherwise mutually agreed upon in writing by both parties. 4.1. PERFORMANCE METRICS a) Page Load Time: Under normal operating conditions, the Yendor Main Coon Cat website shall achieve a page load time of three (3) seconds or less for at least ninety percent (90%) of all page requests. This metric will be measured and reported on a: Weekly ( ) Fortnightly ( ) b) API Response Time: Under normal operating conditions, the API endpoints associated with the Yendor Main Coon website shall respond to at least ninety-five percent (95%) of all requests within a timeframe not exceeding five hundred (500) milliseconds. This metric will be measured and reported on a ____________ basis. c) Database Query Performance: At least ninety-five percent (95%) of all database queries executed in support of the Yendor Main Coon website shall complete within a duration of two hundred (200) milliseconds or less. This metric will be measured and reported on a specify frequency, e.g., monthly basis. 4.2. DATA BACKUP AND RECOVERY PRODUCERS a) Database Backup: The Service Provider shall perform daily backups of all databases associated SERVICE LEVEL AGREEMENT (SLA). with the Yendor Main Coon website. These backups will be retained for a period of thirty (30) calendar days. b) Recovery Point Objective (RPO): In the event of a disaster or significant service interruption resulting in data loss, the maximum acceptable data loss shall be limited to data created or modified within the preceding twenty-four (24) hours. c) Recovery Time Objective (RTO): Following the formal reporting of a disaster or significant service interruption, the Service Provider shall use commercially reasonable efforts to restore all critical services within four ____ hours. 5. SUPPORT AND INCIDENT MANAGEMENT 5.1 Standard Support Hours: Standard support for the Yendor Main Coon Cats website will be available during the following hours: Monday to Friday, 9:00 AM to 5:00 PM Australian Eastern Standard Time (AEST), excluding gazetted public holidays within Queensland, Australia. 5.3. INCIDENT CLASSIFICATION, RESPONSE TIMES & GAUARENTEE: After-hours support is provided solely for Critical Incidents. The following table outlines the classification of incidents and the corresponding target response and resolution times: 5.3. HOW TO REPORT AN INCIDENT Experiencing an issue? We're here to help! Here's how you can report an incident to our team! Severity Description Time Response Resolution Time Critical Service Outage & Data Breach 30 Minutes 4 Hours High Partial service outage or severe performance degradation 2 Hours 8 Hours Medium Non-critical feature unavailable or minor performance issues 4 Hours 24 Hours Low Cosmetic issues or feature requests 8 Hours 72 Hours SERVICE LEVEL AGREEMENT (SLA). Choose Your Reporting Method: Email Us: Send <NAME_EMAIL> Call Us: Reach us by phone at 07 2111 0402 What to Include in Your Report: To help us resolve your issue quickly, please provide the following information: What's happening? - Describe the problem clearly. When did it start? - Tell us the exact time you first noticed the issue. Can we replicate it? - If possible, list the steps to reproduce the problem. How is it affecting you? - Explain the impact on your business. 5.4. OUR INCIDENT MANAGEMENT PROCESS We follow a clear process to manage and resolve incidents, aiming for minimal disruption to your operations. a) Your Report, Our Confirmation As soon as you report an incident, we'll send you a confirmation. This means we've received it and are starting the clock on our agreed-upon response time. b) Uncovering the 'Why' Our developers and support team will investigate thoroughly to understand the core reason behind the incident, ensuring we fix the source, not just the symptom. c) Getting You Back Online We work to resolve incidents within the timeframes detailed in Section 5.2. For complex issues that might involve a third-party tool or significant development work, we factor in a 2-week buffer to ensure a complete and stable solution. d) Staying Connected Through Updates For critical or high-severity incidents, we understand the need for constant communication. We'll provide regular updates: Critical Incidents: Every 4 hours. High-Severity Incidents: Every 8 hours. e) Learning from Critical Incidents Once a critical incident is resolved, we'll provide a post-incident report within 48 hours. This helps us share insights and continuously improve our systems to minimize future issues. 6. MONITORING AND REPORTING: 6.1. Monitoring a) The Service Provider shall implement comprehensive monitoring of all infrastructure components, including: • Server health and resource utilization • Database performance • Website availability and performance • CI/CD pipeline status • Security events SERVICE LEVEL AGREEMENT (SLA). b) Automated alerts shall be configured for potential issues. 6.2. Regular Reporting a) The Service Provider shall provide monthly performance reports including: • Website uptime statistics • Performance metrics • Incident summary • Backup status • Security status b) Monthly reports shall be delivered within 10 business days after the end of each month, with a 2- week leeway period for complex reporting requirements. c) Quarterly service review meetings shall be scheduled to discuss performance, issues, and improvement opportunities. These meetings shall be scheduled at least 2 weeks in advance. 7. FEES AND PAYMENT 7.1 Service Fees Here's a breakdown of our pricing for ongoing services, ensuring transparency and clarity for your web project: a) Base Monthly Service Fee: Our base monthly fee for the comprehensive services outlined in Section 3 is $ (Example amount) per month. b) Currency & Tax: All fees are quoted in Australian Dollars (AUD) and include Goods and Services Tax (GST), unless explicitly stated otherwise. c) Resource Scaling for High Traffic: To ensure your website always performs optimally, even during high-traffic periods, we offer flexible resource scaling. Any additional resources used will be calculated based on actual usage, at the following rates: Additional container instances: $ ______________ per instance per day Additional database resources: $ _______________ per GB of storage per month Additional bandwidth: $ _________________ per GB beyond your included allocation Additional S3 storage: $ _________________ per GB per Tax Invoices** You will always receive a detailed tax invoice from us that fully complies with Australian GST requirements for all charges. b) What's Included (AUD & GST): All prices are in Australian Dollars (AUD) and are inclusive of GST, unless we specify otherwise. What you see is what you pay! c) Scaling for Success (Usage-Based Charges): Sometimes your website experiences traffic spikes or needs extra horsepower. We've got you SERVICE LEVEL AGREEMENT (SLA). covered with flexible scaling options. If your project utilizes additional resources during these times, charges will be based on actual usage at these transparent rates: Extra container instances: $ ____________ per instance per day Extra database resources: $ _____________ per GB of storage per month Extra bandwidth: $ ____________ per GB (once you've exceeded your included allowance) Extra S3 storage: $ ______________ per GB per month d) Your Invoice Every charge will be clearly presented on a tax invoice that meets all Australian GST regulations. 2. Payment Terms We proactively manage your website's health and keep you updated on its performance. a) How we monitor Our team constantly monitors key aspects of your site, such as: System Health: Servers, databases, and general resource usage. Site Performance: How fast and reliably your website is loading. Behind-the-Scenes Processes & Security: Ensuring updates are smooth and threats are spotted. We use automated alerts to catch and fix issues quickly. b) Your Performance Updates: You'll get a clear monthly performance report within 10 business days of the month's close (allowing an extra 2 weeks for detailed analysis). This report will cover uptime, speed, security, and more. We also set up quarterly review meetings (with 2 weeks' notice) to discuss performance and plan for the future ahead. 8. SERVICE CREDITS 8.1 How are Credits Are Calculated? If we fall short of the uptime guarantee specified in Section 4.1, you'll be eligible for service credits applied to your next monthly invoice, calculated as a percentage of your monthly fee: Monthly Uptime Service Credit (% of monthly fee) 99.0% - 99.9% 10% 98.0% - 98.9% 25% 97.0% - 97.9% 50% Below 97.0% 100% These service credits will be applied to your next monthly invoice. 8.2. Requesting Service Credits: To request a service credit, please follow these steps: SERVICE LEVEL AGREEMENT (SLA). Submit your request in writing within 30 days of the end of the month in which the service level failure occurred. Your request should include: a) The date and time of the service disruption. b) A description of what happened. c) The impact it had on your operations. d) We'll review your request and respond within 14 days. 9. CLIENT RESPONSIBILITIES For a smooth and successful collaboration, your cooperation is essential. The Client shall be responsible for: Providing Timely Access: Granting prompt access to necessary information, systems, and relevant personnel. Designating a Point of Contact: Appointing a primary contact person for all communications with PaceySpace. Prompt Issue Reporting: Immediately reporting any issues or incidents that arise. Adhering to Payment Terms: Ensuring all invoices are paid in accordance with the agreed-upon payment terms. Notifying of Traffic Spikes: Providing reasonable advance notice for anticipated high-traffic periods or special events, with at least 2 weeks' notice required for major events. Content & Data Compliance: Complying with all applicable laws and regulations concerning website content and data. Content Control & Rights: Maintaining full control over website content and guaranteeing it does not infringe upon any laws or third-party rights. Responding to Requests: Responding to our requests for information or approvals within 5 business days. For complex requests requiring more detailed input, we appreciate a response within 2 weeks. Providing Feedback on Deliverables: Submitting feedback on deliverables within 10 business days. For complex deliverables requiring extensive review, feedback within 2 weeks is requested. 10. CONFIDENTIALITY & DATA PROTECTION: We understand the sensitive nature of your business information. We are committed to protecting your confidential data, just as we expect the same respect for ours. 10.1 Mutual Confidentiality: Both parties agree to uphold the confidentiality of all proprietary and sensitive information shared during the course of our engagement. 10.2. What's Covered: SERVICE LEVEL AGREEMENT (SLA). Confidential information includes, but isn't limited to, details such as business plans, customer data, financial specifics, and technical designs or specifications. 10.3. Enduring Obligation: This commitment to confidentiality remains in effect and survives the termination or completion of our service agreement. 11. LIMITATION OF LIABILTY t's important for both parties to understand the limits of liability in our agreement. 11.1. Service Provider's Total Liability: Our total liability under this agreement for any claim shall not exceed the total fees paid by the Client to PaceySpace in the twelve (12) months immediately preceding the event that caused the liability. 11.2. Exclusion of Indirect Damages: Neither party will be liable for any indirect, special, incidental, or consequential damages, including but not limited to loss of profits, data, or business interruption. 2. TERMINATION: 12.1. Termination for Cause: Either party may terminate this agreement if the other materially breaches its terms and fails to remedy that breach within thirty (30) days of receiving written notice. 12.2. Payment Upon Termination: Upon termination, the Client will be responsible for all outstanding fees for services rendered up to the termination date. 12.3. Data Transition: Following termination, we'll provide all necessary data and assistance to help the Client transition to another service provider. This transition will occur within 30 days of the termination date, with an allowance of 2 additional weeks for complex migrations. 12.4. Transition Plan: We'll provide a comprehensive transition plan within 10 business days of receiving a termination notice. 13.1. Applicable Law: This agreement is governed by and interpreted in accordance with the laws of Queensland, Australia. 13.2. Jurisdiction: Any disputes arising from or connected with this agreement will be subject to the exclusive jurisdiction of the courts of Queensland, Australia. SERVICE LEVEL AGREEMENT (SLA). 14. Entire Agreement 14.1. Complete Agreement: This document represents the entire agreement between the parties regarding its subject matter, superseding all prior and concurrent agreements or communications. 14.2. Amendments: Any changes or modifications to this agreement must be made in writing and signed by authorized representatives of both parties. 15. PRIVACY & DATA COLLECTION 15.1. Compliance: We commit to complying with the Privacy Act 1988 (Cth) and the Australian Privacy Principles regarding any personal information collected, used, or shared under this agreement. 15.2. Data Security: We'll implement appropriate technical and organizational measures to protect your data from unauthorized access, use, disclosure, alteration, or destruction. 15.3. Data Usage: Your data will only be used for the purpose of providing services under this agreement. 15.4. Data Handling Upon Termination Upon termination, we'll return or securely destroy all Client data as directed by the Client, unless legally required to retain it. 16. INTELECTUAL PROPERTY 16.1. Pre-existing IP: Each party retains all rights, title, and interest in their own pre-existing intellectual property. 16.2. Client Content Ownership: The Client retains all rights, title, and interest in the content, data, and materials they provide for use with our services. 16.3. Service Provider IP License We grant the Client a non-exclusive, non-transferable license to use any of our intellectual property that's necessary for the Client to receive the services during the term of this agreement. SERVICE LEVEL AGREEMENT (SLA). 17. INSURANCE 17.1. Coverage: We will maintain, at our own expense, professional indemnity insurance and public liability insurance with sufficient coverage to meet our obligations under this agreement. 17.2. Proof of Insurance: Upon request, we'll provide the Client with certificates of insurance confirming the required coverage. 18. FORCE MAJEURE: 18.1. Unforeseeable Circumstances: Neither party will be liable for any failure or delay in performance caused by events beyond their reasonable control, including but not limited to acts of God, natural disasters, terrorism, war, civil unrest, labor disputes, or government actions. 18.2. Notification & Resumption: The affected party will notify the other as soon as reasonably possible of the force majeure event and will use commercially reasonable efforts to quickly resume performance. 18.3. Termination Due to Force Majeure: If a force majeure event continues for more than thirty (30) days, either party may terminate this agreement by providing written notice to the other. 19. DISPUTE & RESOLUTIONS 19.1. Good Faith Negotiations: The parties agree to attempt to resolve any dispute arising from or connected with this agreement through good faith negotiations. 19.2. Mediation: If negotiations fail to resolve the dispute within 30 days, either party may refer the dispute to mediation via a mediator appointed by the Australian Disputes Centre. 19.3. Legal Proceedings: If the dispute remains unresolved through mediation within 60 days, either party may initiate legal proceedings in the courts of Queensland, Australia. SERVICE LEVEL AGREEMENT (SLA). 19.4. Urgent Relief: Nothing in this clause prevents either party from seeking urgent injunctive or equitable relief from a court of competent jurisdiction. 20. NOTICES 20.1. Written Notices: All required notices under this agreement must be in writing and delivered by email (with delivery confirmation) or by registered mail to the addresses specified in this agreement. 20.2. Deemed Receipt Notices will be considered received: a) If delivered by email: Upon receipt of delivery confirmation. b) If delivered by registered mail: Three (3) business days after posting. 20.3. Contact Detail Changes: Each party must notify the other of any change in contact details within 5 business days. EXECUTION: This Service Level Agreement is executed as of the date first written above. Acknowledgement and agreement of the service constraints: By signing, you acknowledge that you have read and agree to the terms listed above * Customer/Client: Name: ___________________________________ Signature: _________________________________________ Date: ________________________________________________ Office Use Only SERVICE PROVIDER: Name: _________________________ Signature: ____________________________ Date: _______________________________ SERVICE LEVEL AGREEMENT (SLA).", "libVersion": "0.5.0", "langs": ""}