---
creation_date: 2025-07-02
modification_date: 2025-07-02
type: resource
source: documentation
tags: [para/resources, guide]
area: software-development
difficulty: hard
url: 
---
---
---

# HashiCorp Vault Installation Guide

## Overview

This guide provides both quick-start and comprehensive installation instructions for Hashi<PERSON><PERSON><PERSON> Vault on a VPS with Enhance control panel. Start with the Quick Start section for immediate deployment, then implement advanced security features as needed.

**Tags:** #vault #hashicorp #security #docker #vps #secrets-management #multi-tenant #installation #configuration #enhance

---

## 🚀 Quick Start Guide

### **Simple HashiCorp Vault Setup with Enhance Integration**

**Perfect for:** Initial deployment, client launches, getting up and running quickly with good security practices.

#### **What We're Doing:**
1. Install Docker (if needed)
2. Run HashiCorp Vault in Docker container
3. Add Vault as Custom Application in Enhance Control Panel
4. Configure DNS and SSL through Enhance

#### **Step 1: Install Docker**
```bash
# Install Docker (if not already installed)
sudo apt update
sudo apt install -y docker.io docker-compose
sudo systemctl start docker
sudo systemctl enable docker

# Add current user to docker group (optional, requires logout/login)
sudo usermod -aG docker $USER
```

#### **Step 2: Create Simple Vault Setup**
*using version 1.13.3, which was used in original launch on paceyspace on 02/07/25*
#image #vault #hashicorp #docker #registry 
```bash
# Create directory
sudo mkdir -p /opt/vault
cd /opt/vault

# Create basic docker-compose.yml
sudo tee docker-compose.yml > /dev/null <<EOF
version: '3.8'
services:
  vault:
    image: vault:1.13.3
    container_name: vault-server
    restart: unless-stopped
    ports:
      - "127.0.0.1:8200:8200"  # Only bind to localhost for security
    volumes:
      - vault-data:/vault/file
      - ./config:/vault/config
    environment:
      - VAULT_ADDR=http://127.0.0.1:8200
    cap_add:
      - IPC_LOCK
    entrypoint: vault server -config=/vault/config/vault.hcl

volumes:
  vault-data:
EOF

# Create basic config directory and file
sudo mkdir -p config
sudo tee config/vault.hcl > /dev/null <<EOF
storage "file" {
  path = "/vault/file"
}

listener "tcp" {
  address     = "0.0.0.0:8200"
  tls_disable = true  # Enhance will handle SSL termination
}

ui = true
disable_mlock = false
EOF

# Start Vault
sudo docker-compose up -d

# Verify it's running
docker ps | grep vault-server
```

#### **Step 3: Add to Enhance Control Panel**
1. **Login to Enhance Control Panel**
2. **Navigate to Applications/Websites section**
3. **Add New Application:**
   - **Name:** HashiCorp Vault
   - **Type:** Custom Application / Reverse Proxy
   - **Domain:** vault.yourdomain.com
   - **Target:** 127.0.0.1:8200
   - **Protocol:** HTTP (Enhance handles HTTPS)
4. **Enable SSL** (Let's Encrypt through Enhance)
5. **Save Configuration**

#### **Step 4: Initialize and Unseal Vault**
```bash
# Initialize Vault (SAVE THE OUTPUT SECURELY!)
docker exec -it vault-server vault operator init

# The output will show 5 unseal keys and 1 root token
# Store these securely - you'll need them!

# Unseal Vault using 3 of the 5 keys
docker exec -it vault-server vault operator unseal <unseal-key-1>
docker exec -it vault-server vault operator unseal <unseal-key-2>
docker exec -it vault-server vault operator unseal <unseal-key-3>

# Verify Vault is unsealed and ready
docker exec -it vault-server vault status
```

#### **Step 5: Access Vault UI**
- Navigate to: `https://vault.yourdomain.com`
- Login with the root token from Step 4
- You now have a working Vault installation!

#### **Quick Security Notes:**
- ✅ **Localhost Binding**: Vault only accessible through Enhance reverse proxy
- ✅ **SSL Termination**: Handled automatically by Enhance
- ✅ **Container Isolation**: Docker provides basic isolation
- ✅ **Restart Policy**: Container automatically restarts if it crashes

**Tags:** #quick-start #simple-setup #enhance-integration #docker #ssl-automation

---

## 📚 Table of Contents

**Quick Start (Above)** - Get running in 15 minutes
1. [Prerequisites](#prerequisites) - System requirements
2. [Security Planning](#security-planning) - Advanced security considerations
3. [Pre-Installation Setup](#pre-installation-setup) - Detailed preparation
4. [Docker Installation](#docker-installation) - Production Docker setup
5. [Vault Installation](#vault-installation) - Advanced Vault configuration
6. [Security Hardening](#security-hardening) - Production security measures
7. [Multi-Client Configuration](#multi-client-configuration) - Client isolation
8. [Monitoring & Maintenance](#monitoring--maintenance) - Ongoing operations
9. [Troubleshooting](#troubleshooting) - Common issues and solutions

---

## ⚠️ Advanced Installation Guide

**Note:** The sections below provide comprehensive, production-ready configurations. Use these after your initial deployment is working and you need enhanced security, monitoring, or multi-client features.

---

## Prerequisites

### System Requirements
- **Operating System:** Linux VPS (Ubuntu 20.04+ or CentOS 8+ recommended)
- **RAM:** Minimum 2GB, recommended 4GB+ for multi-client environments
- **Storage:** Minimum 20GB SSD with backup capabilities
- **Network:** Static IP address with firewall capabilities

### Access Requirements
- **Root/Sudo Access:** Administrative privileges on the VPS
- **SSH Access:** Secure shell access with key-based authentication
- **Control Panel:** Enhanced control panel administrative access
- **Backup Access:** Ability to configure automated backups

### Software Dependencies
- **Docker:** Version 20.10+ and Docker Compose v2.0+
- **SSL Certificates:** Valid SSL certificates for production deployment
- **Firewall:** UFW or iptables configured
- **Monitoring Tools:** Optional but recommended for production

**Tags:** #prerequisites #system-requirements #access-control

---

## Security Planning

### Network Security Assessment
Before installation, assess your VPS network security:

1. **Firewall Configuration**
   - Identify current firewall rules
   - Plan port access (8200 for Vault UI, 8201 for cluster communication)
   - Configure fail2ban for SSH protection

2. **SSL/TLS Planning**
   - Obtain valid SSL certificates (Let's Encrypt or commercial)
   - Plan certificate renewal automation
   - Configure reverse proxy if needed

3. **Access Control Strategy**
   - Define client isolation requirements
   - Plan authentication methods per client
   - Design policy structure for multi-tenant access

**Tags:** #security-planning #network-security #ssl-tls #access-control

---

## Pre-Installation Setup

### Step 1: Secure SSH Access

```bash
# Connect to your VPS with enhanced security
ssh -i /path/to/your/private-key.pem username@your-vps-ip

# Alternatively, if using password (less secure)
ssh username@your-vps-ip
```

### Step 2: System Updates and Security Hardening

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install essential security tools
sudo apt install -y ufw fail2ban htop curl wget git

# Configure basic firewall
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 8200/tcp  # Vault UI (restrict this later)
sudo ufw --force enable
```

### Step 3: Create Dedicated Vault User

```bash
# Create vault system user for enhanced security
sudo useradd -r -s /bin/false vault
sudo mkdir -p /opt/vault
sudo chown vault:vault /opt/vault
```

### Step 4: Transfer Project Files Securely

```bash
# From your local machine, transfer files
scp -r -i /path/to/your/key.pem . username@your-vps-ip:/opt/vault/

# Set proper permissions
sudo chown -R vault:vault /opt/vault/
sudo chmod 750 /opt/vault/
```

**Tags:** #pre-installation #ssh-security #system-hardening #file-transfer

---

## Docker Installation

### Step 1: Install Docker Engine (Production Method)

```bash
# Remove any existing Docker installations
sudo apt remove docker docker-engine docker.io containerd runc

# Install prerequisites
sudo apt install -y apt-transport-https ca-certificates curl gnupg lsb-release

# Add Docker's official GPG key
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# Add Docker repository
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# Install Docker Engine
sudo apt update
sudo apt install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
```

### Step 2: Configure Docker Security

```bash
# Start and enable Docker service
sudo systemctl start docker
sudo systemctl enable docker

# Configure Docker daemon for security
sudo mkdir -p /etc/docker
sudo tee /etc/docker/daemon.json > /dev/null <<EOF
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  },
  "live-restore": true,
  "userland-proxy": false,
  "no-new-privileges": true
}
EOF

# Restart Docker with new configuration
sudo systemctl restart docker
```

### Step 3: Docker User Management

```bash
# Add vault user to docker group (for container management)
sudo usermod -aG docker vault

# Verify Docker installation
docker --version
docker compose version

# Test Docker functionality
sudo -u vault docker run --rm hello-world
```

**Tags:** #docker-installation #container-security #docker-configuration

---

## Vault Installation

### Step 1: Prepare Vault Configuration

```bash
# Navigate to vault directory
cd /opt/vault

# Create enhanced vault configuration
sudo -u vault tee config/vault-config.hcl > /dev/null <<EOF
# Storage backend
storage "file" {
  path = "/vault/file"
}

# Network listener
listener "tcp" {
  address       = "0.0.0.0:8200"
  tls_disable   = false
  tls_cert_file = "/vault/tls/vault.crt"
  tls_key_file  = "/vault/tls/vault.key"
}

# Cluster configuration
api_addr = "https://$(hostname -I | awk '{print $1}'):8200"
cluster_addr = "https://$(hostname -I | awk '{print $1}'):8201"

# UI and logging
ui = true
log_level = "INFO"
disable_mlock = false

# Performance and security
default_lease_ttl = "168h"
max_lease_ttl = "720h"
EOF
```

### Step 2: SSL Certificate Setup

```bash
# Create TLS directory
sudo -u vault mkdir -p /opt/vault/tls

# Option A: Self-signed certificate (development/testing)
sudo -u vault openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout /opt/vault/tls/vault.key \
  -out /opt/vault/tls/vault.crt \
  -subj "/C=US/ST=State/L=City/O=Organization/CN=$(hostname -I | awk '{print $1}')"

# Option B: Let's Encrypt certificate (production)
# sudo certbot certonly --standalone -d your-domain.com
# sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem /opt/vault/tls/vault.crt
# sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem /opt/vault/tls/vault.key
# sudo chown vault:vault /opt/vault/tls/*

# Set proper permissions
sudo chmod 600 /opt/vault/tls/vault.key
sudo chmod 644 /opt/vault/tls/vault.crt
```

### Step 3: Enhanced Docker Compose Configuration

```bash
# Create production-ready docker-compose.yml
sudo -u vault tee docker-compose.yml > /dev/null <<EOF
version: '3.8'

services:
  vault:
    image: vault:1.15.4
    container_name: vault-server
    restart: unless-stopped
    volumes:
      - vault-data:/vault/file
      - ./config:/vault/config:ro
      - ./tls:/vault/tls:ro
      - ./logs:/vault/logs
    ports:
      - "8200:8200"
      - "8201:8201"
    environment:
      - VAULT_ADDR=https://127.0.0.1:8200
      - VAULT_API_ADDR=https://127.0.0.1:8200
      - VAULT_CLUSTER_ADDR=https://127.0.0.1:8201
    cap_add:
      - IPC_LOCK
    cap_drop:
      - ALL
    security_opt:
      - no-new-privileges:true
    user: "100:1000"  # vault user
    entrypoint: vault server -config=/vault/config/vault-config.hcl
    networks:
      - vault-network
    healthcheck:
      test: ["CMD", "vault", "status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  vault-data:
    driver: local

networks:
  vault-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
EOF
```

### Step 4: Start Vault Container

```bash
# Start Vault in detached mode
https//*************:8200

# Verify container is running
docker ps | grep vault-server

# Check container logs
docker logs vault-server
```

**Tags:** #vault-installation #ssl-configuration #docker-compose #container-security

---

## Security Hardening

### Step 1: Initialize Vault Securely

```bash
# Initialize Vault with custom key shares and threshold
docker exec -it vault-server vault operator init \
  -key-shares=5 \
  -key-threshold=3 \
  -format=json > /opt/vault/vault-init.json

# Secure the initialization file
sudo chmod 600 /opt/vault/vault-init.json
sudo chown vault:vault /opt/vault/vault-init.json

# Extract keys and root token (store these securely offline)
cat /opt/vault/vault-init.json | jq -r '.unseal_keys_b64[]'
cat /opt/vault/vault-init.json | jq -r '.root_token'
```

### Step 2: Unseal Vault

```bash
# Unseal Vault using 3 of the 5 keys
docker exec -it vault-server vault operator unseal <key1>
docker exec -it vault-server vault operator unseal <key2>
docker exec -it vault-server vault operator unseal <key3>

# Verify Vault status
docker exec -it vault-server vault status
```

### Step 3: Initial Security Configuration

```bash
# Set Vault address for CLI operations
export VAULT_ADDR="https://$(hostname -I | awk '{print $1}'):8200"
export VAULT_TOKEN="<root-token-from-init>"

# Enable audit logging
docker exec -it vault-server vault audit enable file file_path=/vault/logs/audit.log

# Create initial admin policy
docker exec -it vault-server vault policy write admin-policy - <<EOF
# Admin policy for Vault administrators
path "*" {
  capabilities = ["create", "read", "update", "delete", "list", "sudo"]
}
EOF
```

### Step 4: Network Security Enhancement

```bash
# Restrict Vault UI access to specific IPs (replace with your IPs)
sudo ufw delete allow 8200/tcp
sudo ufw allow from YOUR_ADMIN_IP to any port 8200
sudo ufw allow from YOUR_OFFICE_IP to any port 8200

# Configure fail2ban for Vault (optional)
sudo tee /etc/fail2ban/jail.d/vault.conf > /dev/null <<EOF
[vault]
enabled = true
port = 8200
filter = vault
logpath = /opt/vault/logs/audit.log
maxretry = 3
bantime = 3600
EOF
```

### Step 5: Secure Access Methods

```bash
# Access Vault UI securely via SSH tunnel
ssh -L 8200:localhost:8200 -N username@your-vps-ip

# Then access https://localhost:8200 in your browser
# Or access directly via https://your-vps-ip:8200 (if firewall allows)
```

**Tags:** #security-hardening #vault-initialization #audit-logging #network-security #ssh-tunnel

---

## Multi-Client Configuration

### Step 1: Enable Secrets Engines

```bash
# Enable KV v2 secrets engine for general secrets
docker exec -it vault-server vault secrets enable -path=kv kv-v2

# Enable AWS secrets engine for S3 credentials
docker exec -it vault-server vault secrets enable aws

# Enable database secrets engine for dynamic database credentials
docker exec -it vault-server vault secrets enable database

# List enabled secrets engines
docker exec -it vault-server vault secrets list
```

### Step 2: Create Client-Specific Policies

```bash
# Create policy for Client A
docker exec -it vault-server vault policy write client-a-policy - <<EOF
# Policy for Client A - restricted access to their secrets only
path "kv/data/client-a/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

path "kv/metadata/client-a/*" {
  capabilities = ["list", "read", "delete"]
}

path "aws/creds/client-a-s3-role" {
  capabilities = ["read"]
}

path "database/creds/client-a-db-role" {
  capabilities = ["read"]
}
EOF

# Create policy for Client B
docker exec -it vault-server vault policy write client-b-policy - <<EOF
# Policy for Client B - restricted access to their secrets only
path "kv/data/client-b/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

path "kv/metadata/client-b/*" {
  capabilities = ["list", "read", "delete"]
}

path "aws/creds/client-b-s3-role" {
  capabilities = ["read"]
}

path "database/creds/client-b-db-role" {
  capabilities = ["read"]
}
EOF
```

### Step 3: Configure Authentication Methods

```bash
# Enable AppRole authentication for applications
docker exec -it vault-server vault auth enable approle

# Create AppRole for Client A
docker exec -it vault-server vault write auth/approle/role/client-a-app \
    token_policies="client-a-policy" \
    token_ttl=1h \
    token_max_ttl=4h \
    bind_secret_id=true

# Create AppRole for Client B
docker exec -it vault-server vault write auth/approle/role/client-b-app \
    token_policies="client-b-policy" \
    token_ttl=1h \
    token_max_ttl=4h \
    bind_secret_id=true

# Get Role IDs (store these securely for each client)
docker exec -it vault-server vault read auth/approle/role/client-a-app/role-id
docker exec -it vault-server vault read auth/approle/role/client-b-app/role-id

# Generate Secret IDs (provide these to clients securely)
docker exec -it vault-server vault write -f auth/approle/role/client-a-app/secret-id
docker exec -it vault-server vault write -f auth/approle/role/client-b-app/secret-id
```

### Step 4: Configure AWS S3 Integration

```bash
# Configure AWS credentials for S3 access
docker exec -it vault-server vault write aws/config/root \
    access_key=YOUR_AWS_ACCESS_KEY \
    secret_key=YOUR_AWS_SECRET_KEY \
    region=us-east-1

# Create S3 role for Client A
docker exec -it vault-server vault write aws/roles/client-a-s3-role \
    credential_type=iam_user \
    policy_document=-<<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:GetObject",
        "s3:PutObject",
        "s3:DeleteObject"
      ],
      "Resource": "arn:aws:s3:::client-a-bucket/*"
    }
  ]
}
EOF

# Create S3 role for Client B
docker exec -it vault-server vault write aws/roles/client-b-s3-role \
    credential_type=iam_user \
    policy_document=-<<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:GetObject",
        "s3:PutObject",
        "s3:DeleteObject"
      ],
      "Resource": "arn:aws:s3:::client-b-bucket/*"
    }
  ]
}
EOF
```

### Step 5: Store Client Secrets

```bash
# Store API keys and secrets for Client A
docker exec -it vault-server vault kv put kv/client-a/api-keys \
    stripe_key="sk_test_..." \
    sendgrid_key="SG...." \
    google_analytics="GA1...."

# Store database credentials for Client A
docker exec -it vault-server vault kv put kv/client-a/database \
    host="client-a-db.example.com" \
    username="client_a_user" \
    password="secure_password_123"

# Store API keys and secrets for Client B
docker exec -it vault-server vault kv put kv/client-b/api-keys \
    paypal_client_id="AX..." \
    mailchimp_key="abc123..." \
    facebook_app_secret="def456..."
```

**Tags:** #multi-client #secrets-engines #policies #approle #aws-integration #s3-credentials

---

## Monitoring & Maintenance

### Step 1: Set Up Monitoring

```bash
# Create monitoring script
sudo tee /opt/vault/scripts/vault-health-check.sh > /dev/null <<EOF
#!/bin/bash
VAULT_ADDR="https://localhost:8200"
VAULT_TOKEN="your-monitoring-token"

# Check Vault status
STATUS=$(docker exec vault-server vault status -format=json 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "$(date): Vault is healthy" >> /var/log/vault-health.log
else
    echo "$(date): Vault health check failed" >> /var/log/vault-health.log
    # Send alert (email, Slack, etc.)
fi

# Check disk usage
DISK_USAGE=$(df /opt/vault | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "$(date): Disk usage high: ${DISK_USAGE}%" >> /var/log/vault-health.log
fi
EOF

sudo chmod +x /opt/vault/scripts/vault-health-check.sh

# Add to crontab for regular monitoring
echo "*/5 * * * * /opt/vault/scripts/vault-health-check.sh" | sudo crontab -
```

### Step 2: Backup Configuration

```bash
# Create backup script
sudo tee /opt/vault/scripts/vault-backup.sh > /dev/null <<EOF
#!/bin/bash
BACKUP_DIR="/opt/vault/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p $BACKUP_DIR

# Backup Vault data
docker exec vault-server vault operator raft snapshot save /vault/backup_$DATE.snap
docker cp vault-server:/vault/backup_$DATE.snap $BACKUP_DIR/

# Backup configuration
cp -r /opt/vault/config $BACKUP_DIR/config_$DATE

# Clean old backups (keep last 7 days)
find $BACKUP_DIR -name "*.snap" -mtime +7 -delete
find $BACKUP_DIR -name "config_*" -mtime +7 -exec rm -rf {} \;

echo "$(date): Backup completed: backup_$DATE.snap" >> /var/log/vault-backup.log
EOF

sudo chmod +x /opt/vault/scripts/vault-backup.sh

# Schedule daily backups
echo "0 2 * * * /opt/vault/scripts/vault-backup.sh" | sudo crontab -
```

### Step 3: Log Rotation

```bash
# Configure logrotate for Vault logs
sudo tee /etc/logrotate.d/vault > /dev/null <<EOF
/opt/vault/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    copytruncate
}
EOF
```

### Step 4: Update Procedures

```bash
# Create update script
sudo tee /opt/vault/scripts/vault-update.sh > /dev/null <<EOF
#!/bin/bash
# Vault update procedure

echo "Starting Vault update procedure..."

# Backup before update
/opt/vault/scripts/vault-backup.sh

# Pull latest Vault image
docker pull vault:latest

# Update docker-compose.yml with new version
sed -i 's/vault:1.15.4/vault:latest/' /opt/vault/docker-compose.yml

# Restart with new image
cd /opt/vault
docker compose down
docker compose up -d

echo "Vault update completed. Check logs for any issues."
EOF

sudo chmod +x /opt/vault/scripts/vault-update.sh
```

**Tags:** #monitoring #backup #maintenance #health-checks #log-rotation #updates

---

## Troubleshooting

### Common Issues and Solutions

#### 1. Container Won't Start
```bash
# Check Docker logs
docker logs vault-server

# Common fixes:
# - Check file permissions: sudo chown -R vault:vault /opt/vault
# - Verify configuration syntax: docker exec vault-server vault server -config=/vault/config/vault-config.hcl -test
# - Check port availability: sudo netstat -tlnp | grep 8200
```

#### 2. SSL Certificate Issues
```bash
# Verify certificate validity
openssl x509 -in /opt/vault/tls/vault.crt -text -noout

# Check certificate permissions
ls -la /opt/vault/tls/

# Regenerate self-signed certificate if needed
sudo -u vault openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout /opt/vault/tls/vault.key \
  -out /opt/vault/tls/vault.crt
```

#### 3. Vault Sealed State
```bash
# Check seal status
docker exec -it vault-server vault status

# Unseal if needed
docker exec -it vault-server vault operator unseal <key1>
docker exec -it vault-server vault operator unseal <key2>
docker exec -it vault-server vault operator unseal <key3>
```

#### 4. Permission Denied Errors
```bash
# Fix common permission issues
sudo chown -R vault:vault /opt/vault
sudo chmod 750 /opt/vault
sudo chmod 600 /opt/vault/tls/vault.key
sudo chmod 644 /opt/vault/tls/vault.crt
```

#### 5. Network Connectivity Issues
```bash
# Check firewall rules
sudo ufw status

# Test connectivity
curl -k https://localhost:8200/v1/sys/health

# Check Docker network
docker network ls
docker network inspect vault_vault-network
```

**Tags:** #troubleshooting #common-issues #ssl-issues #permissions #network-issues

---

## Security Best Practices Summary

### Critical Security Measures
- ✅ **TLS Encryption**: Always use HTTPS in production
- ✅ **Firewall Rules**: Restrict access to necessary IPs only
- ✅ **Regular Backups**: Automated daily backups with retention
- ✅ **Audit Logging**: Enable and monitor audit logs
- ✅ **Key Management**: Store unseal keys securely offline
- ✅ **Policy Isolation**: Strict client separation via policies
- ✅ **Regular Updates**: Keep Vault and Docker updated
- ✅ **Monitoring**: Continuous health and security monitoring

### Ongoing Maintenance Tasks
- **Weekly**: Review audit logs for suspicious activity
- **Monthly**: Update Vault and Docker images
- **Quarterly**: Rotate authentication credentials
- **Annually**: Review and update security policies

**Tags:** #security-best-practices #maintenance-schedule #critical-measures

---

## Enhance Control Panel Integration Strategy

### Recommended Approach: Hybrid Management

The optimal approach for your multi-client VPS environment is a **hybrid management strategy** that leverages Enhance's strengths while maintaining security isolation:

#### Option A: Enhance-Managed with Custom Application (RECOMMENDED)

**Advantages:**
- ✅ Leverage Enhance's backup system
- ✅ DNS management through Enhance
- ✅ SSL certificate automation
- ✅ Monitoring integration
- ✅ Centralized management interface

**Implementation Strategy:**

1. **Create Vault as Custom Application in Enhance**
2. **Use Enhance's reverse proxy for SSL termination**
3. **Integrate with Enhance's backup system**
4. **Maintain Docker isolation for security**

### Step-by-Step Enhance Integration

#### Step 1: Create Custom Application in Enhance

```bash
# 1. Access Enhance control panel
# 2. Navigate to "Applications" or "Custom Apps"
# 3. Create new application with these settings:
#    - Name: "HashiCorp Vault"
#    - Type: "Custom Application"
#    - Domain: vault.yourdomain.com
#    - Port: 8200
#    - Protocol: HTTP (Enhance will handle SSL)
```

#### Step 2: Modified Docker Configuration for Enhance

```bash
# Create Enhance-compatible docker-compose.yml
sudo -u vault tee /opt/vault/docker-compose-enhance.yml > /dev/null <<EOF
version: '3.8'

services:
  vault:
    image: vault:1.15.4
    container_name: vault-server
    restart: unless-stopped
    volumes:
      - vault-data:/vault/file
      - ./config:/vault/config:ro
      - ./logs:/vault/logs
      - /var/enhance/backups/vault:/vault/backups
    ports:
      - "127.0.0.1:8200:8200"  # Bind to localhost only
      - "127.0.0.1:8201:8201"  # Cluster port
    environment:
      - VAULT_ADDR=http://127.0.0.1:8200
      - VAULT_API_ADDR=http://127.0.0.1:8200
      - VAULT_CLUSTER_ADDR=http://127.0.0.1:8201
    cap_add:
      - IPC_LOCK
    cap_drop:
      - ALL
    security_opt:
      - no-new-privileges:true
    user: "vault:vault"
    entrypoint: vault server -config=/vault/config/vault-config-enhance.hcl
    networks:
      - vault-network
    healthcheck:
      test: ["CMD", "vault", "status"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  vault-data:
    driver: local

networks:
  vault-network:
    driver: bridge
    internal: true  # Isolate from other containers
EOF
```

#### Step 3: Enhance-Compatible Vault Configuration

```bash
# Create vault configuration for Enhance integration
sudo -u vault tee config/vault-config-enhance.hcl > /dev/null <<EOF
# Storage backend
storage "file" {
  path = "/vault/file"
}

# Network listener (HTTP only - Enhance handles SSL)
listener "tcp" {
  address     = "0.0.0.0:8200"
  tls_disable = true
}

# API configuration
api_addr = "http://127.0.0.1:8200"
cluster_addr = "http://127.0.0.1:8201"

# UI and logging
ui = true
log_level = "INFO"
disable_mlock = false

# Performance settings
default_lease_ttl = "168h"
max_lease_ttl = "720h"

# Enhance backup integration
raw_storage_endpoint = true
EOF
```

**Tags:** #enhance-integration #custom-application #reverse-proxy #ssl-termination

---

## Secure Isolation Strategy

### Network Isolation Layers

#### Layer 1: Docker Network Isolation

```bash
# Create isolated Docker network for Vault
docker network create \
  --driver bridge \
  --subnet=**********/16 \
  --ip-range=**********/24 \
  --gateway=********** \
  vault-isolated-network

# Update docker-compose to use isolated network
# This prevents Vault from accessing other containers
```

#### Layer 2: System User Isolation

```bash
# Create dedicated vault user with restricted permissions
sudo useradd -r -s /bin/false -d /opt/vault vault
sudo mkdir -p /opt/vault/{config,logs,backups,data}
sudo chown -R vault:vault /opt/vault
sudo chmod 750 /opt/vault

# Restrict vault user from accessing other enhance sites
sudo usermod -G docker vault  # Only docker group access
```

#### Layer 3: Filesystem Isolation

```bash
# Create separate filesystem mount for Vault (optional but recommended)
# This isolates Vault data from other applications

# Option A: Separate partition
sudo mkdir -p /vault-data
sudo mount /dev/sdb1 /vault-data  # Replace with your partition
sudo chown vault:vault /vault-data

# Option B: LVM logical volume
sudo lvcreate -L 10G -n vault-lv vg0
sudo mkfs.ext4 /dev/vg0/vault-lv
sudo mount /dev/vg0/vault-lv /vault-data
```

#### Layer 4: Process Isolation

```bash
# Configure systemd service for better isolation
sudo tee /etc/systemd/system/vault-docker.service > /dev/null <<EOF
[Unit]
Description=HashiCorp Vault Docker Container
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
User=vault
Group=vault
WorkingDirectory=/opt/vault
ExecStart=/usr/bin/docker compose -f docker-compose-enhance.yml up -d
ExecStop=/usr/bin/docker compose -f docker-compose-enhance.yml down
TimeoutStartSec=0

# Security restrictions
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/vault

[Install]
WantedBy=multi-user.target
EOF

sudo systemctl enable vault-docker.service
```

**Tags:** #network-isolation #user-isolation #filesystem-isolation #process-isolation

---

---
