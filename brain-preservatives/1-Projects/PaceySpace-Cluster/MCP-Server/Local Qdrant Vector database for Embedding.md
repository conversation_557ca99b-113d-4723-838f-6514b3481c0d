
```bash
➜  src source "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/.venv/bin/activate"
(.venv) ➜  src docker exec mcp-qdrant-debug printenv | grep QDRANT
QDRANT_URL=
QDRANT_API_KEY=
(.venv) ➜  src git:(main) ✗ docker stop mcp-qdrant-debug && docker rm mcp-qdrant-debug
mcp-qdrant-debug
mcp-qdrant-debug
(.venv) ➜  src git:(main) ✗ docker exec mcp-qdrant-debug bash -c "unset QDRANT_URL; unset QDRANT_API_KEY; FASTMCP_HOST=
0.0.0.0 FASTMCP_PORT=8000 FASTMCP_LOG_LEVEL=DEBUG FASTMCP_TRANSPORT=sse uvx mcp-server-qdrant"
Downloading numpy (13.9MiB)
Downloading grpcio (5.7MiB)
Downloading cryptography (4.0MiB)
Downloading hf-xet (2.9MiB)
Downloading sympy (6.0MiB)
Downloading pydantic-core (1.8MiB)
Downloading pygments (1.2MiB)
Downloading tokenizers (3.1MiB)
Downloading pillow (5.7MiB)
Downloading onnxruntime (13.8MiB)
   Building pyperclip==1.9.0
      Built pyperclip==1.9.0
 Downloading pygments
 Downloading pydantic-core
 Downloading hf-xet
 Downloading tokenizers
 Downloading cryptography
 Downloading grpcio
 Downloading pillow
 Downloading sympy
 Downloading numpy
 Downloading onnxruntime
Installed 88 packages in 152ms
Fetching 5 files: 100%|██████████| 5/5 [00:04<00:00,  1.14it/s]

╭────────────────────────────────────────────────────────────────────────────╮
│                                                                            │
│        _ __ ___  _____           __  __  _____________    ____    ____     │
│       _ __ ___ .____/___ ______/ /_/  |/  / ____/ __ \  |___ \  / __ \    │
│      _ __ ___ / /_  / __ `/ ___/ __/ /|_/ / /   / /_/ /  ___/ / / / / /    │
│     _ __ ___ / __/ / /_/ (__  ) /_/ /  / / /___/ ____/  /  __/_/ /_/ /     │
│    _ __ ___ /_/    \____/____/\__/_/  /_/\____/_/      /_____(*)____/      │
│                                                                            │
│                                                                            │
│                                FastMCP  2.0                                │
│                                                                            │
│                                                                            │
│                 🖥️  Server name:     mcp-server-qdrant                      │
│                 📦 Transport:       STDIO                                  │
│                                                                            │
│                 🏎️  FastMCP version: 2.12.1                                 │
│                 🤝 MCP SDK version: 1.13.1                                 │
│                                                                            │
│                 📚 Docs:            https://gofastmcp.com                  │
│                 🚀 Deploy:          https://fastmcp.cloud                  │
│                                                                            │
╰────────────────────────────────────────────────────────────────────────────╯


[09/03/25 19:59:28] INFO     Starting MCP server                  server.py:1493
                             'mcp-server-qdrant' with transport                 
                             'stdio'                                            
(.venv) ➜  src git:(main) ✗ docker exec mcp-qdrant-debug curl http://localhost:8000/tools
OCI runtimedengcao/Qwen3-Embedding-4B:Q4_K_M  exec failed: exec failed: unable to start container process: exec: "curl": executable file not found in $PATH
(.venv) ➜  src git:(main) ✗ docker stop mcp-qdrant-debug && docker rm mcp-qdrant-debug
mcp-qdrant-debug
mcp-qdrant-debug
(.venv) ➜  src git:(main) ✗ 
 *  History restored 

source "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src/.venv/bin/activate"
➜  src source "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Projects/yendorcats/src
/.venv/bin/activate"
(.venv) ➜  src git:(main) ✗ 
%%  %%```%%  %%