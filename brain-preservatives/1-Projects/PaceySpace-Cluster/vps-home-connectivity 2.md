# VPS to Home Network Connectivity Solutions

## Architecture Overview

```
Internet → VPS (Public IP) → Tunnel → Home Network (Private IPs)
                ↓
        Nginx Reverse Proxy
                ↓
    [Encrypted Tunnel Options]
                ↓
        Home Servers (Behind NAT)
```

## Option 1: ZeroTier Mesh (Recommended)

### Network Topology
```
ZeroTier Network: ***********/24
├── VPS: *********** (Public endpoint)
├── Server 1: ************ (Home)
└── Server 2: ************ (Home)
```

### VPS Setup (DigitalOcean/Vultr/etc.)
```bash
# Install ZeroTier on VPS
curl -s https://install.zerotier.com | sudo bash
sudo zerotier-cli join YOUR_NETWORK_ID

# Install Nginx
sudo apt update && sudo apt install nginx

# Configure Nginx as reverse proxy
sudo nano /etc/nginx/sites-available/cluster-proxy
```

### Nginx Configuration on VPS
```nginx
# /etc/nginx/sites-available/cluster-proxy
upstream home_cluster {
    server ************:80;    # Server 1 via ZeroTier
    server ************:8080;  # Server 2 via ZeroTier
}

server {
    listen 80;
    listen 443 ssl http2;
    server_name yourdomain.com *.yourdomain.com;
    
    # SSL configuration
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    
    location / {
        proxy_pass http://home_cluster;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Websocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}

# Portainer access
server {
    listen 443 ssl http2;
    server_name portainer.yourdomain.com;
    
    location / {
        proxy_pass https://************:9443;
        proxy_set_header Host $host;
        proxy_ssl_verify off;
    }
}
```

### Home Network Setup
```bash
# Both home servers
curl -s https://install.zerotier.com | sudo bash
sudo zerotier-cli join YOUR_NETWORK_ID

# Authorize devices in ZeroTier Central web interface
# Configure static routes if needed
```

**Pros:**
- Simple setup and management
- Automatic NAT traversal
- Encrypted connections
- Works from anywhere
- No port forwarding needed

**Cons:**
- Third-party dependency
- Potential latency (usually <50ms)

## Option 2: WireGuard VPN Tunnel

### Network Architecture
```
VPS (WireGuard Client) ←→ Home Server (WireGuard Server)
********                   ********
```

### Home Server (WireGuard Server)
```bash
# Install WireGuard
sudo apt install wireguard

# Generate keys
wg genkey | tee privatekey | wg pubkey > publickey

# Server configuration
sudo nano /etc/wireguard/wg0.conf
```

```ini
# /etc/wireguard/wg0.conf (Home Server)
[Interface]
PrivateKey = HOME_SERVER_PRIVATE_KEY
Address = ********/24
ListenPort = 51820
PostUp = iptables -A FORWARD -i wg0 -j ACCEPT; iptables -t nat -A POSTROUTING -o eth0 -j MASQUERADE
PostDown = iptables -D FORWARD -i wg0 -j ACCEPT; iptables -t nat -D POSTROUTING -o eth0 -j MASQUERADE

[Peer]
# VPS
PublicKey = VPS_PUBLIC_KEY
AllowedIPs = ********/32
```

### VPS (WireGuard Client)
```ini
# /etc/wireguard/wg0.conf (VPS)
[Interface]
PrivateKey = VPS_PRIVATE_KEY
Address = ********/24

[Peer]
PublicKey = HOME_SERVER_PUBLIC_KEY
Endpoint = YOUR_HOME_PUBLIC_IP:51820
AllowedIPs = 10.0.0.0/24, ***********/24
PersistentKeepalive = 25
```

### Router Configuration
```bash
# Port forward on home router
Port 51820 UDP → ************ (Server 1)

# Or use UPnP if available
sudo apt install miniupnpc
upnpc -a ************ 51820 51820 UDP
```

**Pros:**
- Self-hosted solution
- Minimal overhead
- Very secure
- Full control

**Cons:**
- Requires port forwarding
- More complex setup
- Single point of failure

## Option 3: Cloudflare Tunnel (Zero Trust)

### Setup on Home Server
```bash
# Install cloudflared
wget https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-linux-amd64.deb
sudo dpkg -i cloudflared-linux-amd64.deb

# Authenticate
cloudflared tunnel login

# Create tunnel
cloudflared tunnel create home-cluster
cloudflared tunnel route dns home-cluster yourdomain.com
```

### Tunnel Configuration
```yaml
# ~/.cloudflared/config.yml
tunnel: home-cluster
credentials-file: ~/.cloudflared/YOUR_TUNNEL_ID.json

ingress:
  - hostname: yourdomain.com
    service: http://************:80
  - hostname: portainer.yourdomain.com
    service: https://************:9443
    originServerName: portainer.yourdomain.com
  - hostname: "*.yourdomain.com"
    service: http://************:80
  - service: http_status:404
```

### VPS Nginx Configuration
```nginx
# Simple proxy to Cloudflare
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}

# Cloudflare handles HTTPS termination
```

**Pros:**
- No port forwarding needed
- DDoS protection included
- Global CDN
- Zero-config HTTPS

**Cons:**
- Cloudflare dependency
- Limited to HTTP/HTTPS
- Potential bandwidth limits

## Option 4: Tailscale Funnel (New Feature)

### Setup
```bash
# Install Tailscale on all devices
curl -fsSL https://tailscale.com/install.sh | sh

# Home server
sudo tailscale up --advertise-exit-node

# VPS
sudo tailscale up

# Enable Tailscale Funnel (public access)
tailscale funnel --bg 8080
```

**Pros:**
- Extremely easy setup
- Professional-grade security
- Excellent documentation

**Cons:**
- Commercial service ($6/user/month for teams)
- Still in beta for some features

## Recommended Implementation: ZeroTier + VPS

### Step-by-Step Setup

**1. Create ZeroTier Network**
- Go to my.zerotier.com
- Create new network
- Note the Network ID
- Set to Private
- Configure IP assignment: ***********/24

**2. Setup Home Servers**
```bash
# Both servers
curl -s https://install.zerotier.com | sudo bash
sudo zerotier-cli join YOUR_NETWORK_ID

# Check status
sudo zerotier-cli listnetworks
```

**3. Setup VPS**
```bash
# Install ZeroTier and Nginx
curl -s https://install.zerotier.com | sudo bash
sudo zerotier-cli join YOUR_NETWORK_ID
sudo apt install nginx certbot python3-certbot-nginx

# Test connectivity to home
ping ************  # Should reach Server 1
ping ************  # Should reach Server 2
```

**4. Configure DNS**
```bash
# Point your domain to VPS
yourdomain.com → VPS_PUBLIC_IP
*.yourdomain.com → VPS_PUBLIC_IP
```

**5. SSL Certificates**
```bash
# Get Let's Encrypt certificates
sudo certbot --nginx -d yourdomain.com -d "*.yourdomain.com"

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Health Monitoring
```bash
# Create health check script
nano /usr/local/bin/tunnel-check.sh
```

```bash
#!/bin/bash
# tunnel-check.sh
if ! ping -c 1 ************ > /dev/null 2>&1; then
    echo "$(date): Home network unreachable" >> /var/log/tunnel-health.log
    # Send alert (email, Discord, etc.)
    systemctl restart zerotier-one
fi
```

### Performance Optimization

**Nginx Caching on VPS**
```nginx
# Add to nginx config
proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=home_cache:10m max_size=1g inactive=60m;

location / {
    proxy_cache home_cache;
    proxy_cache_valid 200 5m;
    proxy_cache_valid 404 1m;
    proxy_pass http://home_cluster;
}
```

**Connection Pooling**
```nginx
upstream home_cluster {
    server ************:80 max_fails=3 fail_timeout=30s;
    server ************:8080 max_fails=3 fail_timeout=30s;
    keepalive 32;
}
```

## Security Considerations

### VPS Firewall
```bash
# UFW rules
sudo ufw allow 22/tcp   # SSH
sudo ufw allow 80/tcp   # HTTP
sudo ufw allow 443/tcp  # HTTPS
sudo ufw deny incoming
sudo ufw enable
```

### Rate Limiting
```nginx
# Add to nginx config
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;

location /api/ {
    limit_req zone=api burst=20 nodelay;
    proxy_pass http://home_cluster;
}
```

### Monitoring
```bash
# Install monitoring agent
curl -sSL https://repos.insights.digitalocean.com/install.sh | sudo bash
# Or use your preferred monitoring solution
```

This ZeroTier + VPS approach gives you the best balance of simplicity, security, and reliability for connecting your public-facing proxy to your home cluster.