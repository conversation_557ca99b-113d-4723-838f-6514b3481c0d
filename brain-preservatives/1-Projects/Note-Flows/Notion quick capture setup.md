To quickly create a new Note and bring it up as a floating window on top of all workspaces in macOS using Notion, follow these steps:

 1. **Install the Notion CLI (Command Line Interface):**
   - Open Terminal and install the Notion CLI by running the following command:
     ```sh
     npm install -g notion-cli
     ```

2. **Create a New Note:**
   - Use the `notion create` command to create a new Note with minimal input fields, which will result in an empty note buffer on top of all workspaces. The quickest way to do this is by using placeholders:
     ```sh
     notion create "Placeholder for my new note" --in "https://your-notion-workspace-url.com/dashboards/" --type=Note
     ```
   - Replace `your-notion-workspace-url` with the actual URL of your Notion workspace, and update `"https://your-notion-workspace-url.com/dashboards/"` to point to a dashboard containing all the pages you want your new note to appear in.

3. **Move the Note Buffer:**
   - To make this Note buffer always visible on top of other workspaces, use the `notion move` command with the `--in` flag pointing to the dashboard where you want the Note to show up:
     ```sh
     notion move "https://your-notion-workspace-url.com/dashboards/" --out=https://your-notion-workspace-url.com/dashboards/YourDashboardName/YourNoteTitle#0 --type=Note
     ```
   - Replace `https://your-notion-workspace-url.com` with the URL of your Notion workspace and `"YourDashboardName/YourNoteTitle"` with the actual dashboard name and note title where you want the new Note to appear. The `#0` specifies that it should be created as a new note in this specific space.

4. **Use a Global Hotkey:**
   - To create a floating Note buffer on top of all workspaces without typing, set up a global hotkey using a tool like `autohotkey`. Here's an example script:
     ```ahk
     #IfWinActive Ahk_Class Notion

     NOTION_NOTE_BUFFER := "https://your-notion-workspace-url.com/dashboards/"
     NOTION_OUT_URL := "https://your-notion-workspace-url.com/dashboards/" & SubStr(NOTION_NOTE_BUFFER, 5) & "/YourNoteTitle#0"

     OnKey, F1 {
         Send, %NOTION_NOTE_BUFFER%
         Sleep, 200
     }

     OnKey, Ctrl+F1 {
         SetVariable, _NotionHotkeyState, 1
         Send, %NOTION_OUT_URL%
         Sleep, 200
     }

     ExitApp
```
   - Replace `your-notion-workspace-url` with the actual URL of your Notion workspace and `YourNoteTitle` with the title you want to display in the Note. This script will allow you to create a new Note using F1 hotkey, and it will move the created note buffer to the desired dashboard when pressing Ctrl+F1 (Command on Mac).

By following these steps, you'll be able to quickly create new Notes with minimal input fields and bring them up as floating windows on top of all workspaces in macOS using Notion.