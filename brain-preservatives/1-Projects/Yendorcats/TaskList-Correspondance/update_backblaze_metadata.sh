#!/bin/bash

# Update Backblaze B2 metadata for YendorCats images
# Using Backblaze B2's S3-compatible API

BUCKET="yendor"
PREFIX="YendorCats-General-SiteAccess"
ENDPOINT="https://s3.us-west-004.backblazeb2.com"
PROFILE="backblaze"

echo "Starting Backblaze B2 metadata update for YendorCats galleries..."
echo "Bucket: $BUCKET"
echo "Path: $PREFIX"
echo ""

# Function to update metadata for a gallery
update_gallery() {
    local gallery=$1
    echo "=== Updating $gallery gallery ==="
    
    # List all image files in the gallery
    aws s3 ls "s3://$BUCKET/$PREFIX/$gallery/" --profile $PROFILE --endpoint-url $ENDPOINT --recursive | while read -r line; do
        # Extract the file key from the listing
        key=$(echo "$line" | awk '{print $4}')
        
        # Skip if key is empty or is .bzEmpty
        if [ -z "$key" ] || [[ "$key" == *".bzEmpty"* ]]; then
            continue
        fi
        
        # Check if it's an image file
        if [[ "$key" =~ \.(jpg|jpeg|png|gif|webp|avif|JPG|JPEG|PNG)$ ]]; then
            echo "  Updating: $key"
            
            # Determine content type based on extension
            extension="${key##*.}"
            extension_lower=$(echo "$extension" | tr '[:upper:]' '[:lower:]')
            
            case "$extension_lower" in
                jpg|jpeg)
                    content_type="image/jpeg"
                    ;;
                png)
                    content_type="image/png"
                    ;;
                gif)
                    content_type="image/gif"
                    ;;
                webp)
                    content_type="image/webp"
                    ;;
                avif)
                    content_type="image/avif"
                    ;;
                *)
                    content_type="application/octet-stream"
                    ;;
            esac
            
            # Extract just the filename for content-disposition
            filename=$(basename "$key")
            
            # Update the object metadata by copying it to itself with new metadata
            # Note: Backblaze B2 supports cache-control through x-amz-meta-cache-control
            aws s3 cp "s3://$BUCKET/$key" "s3://$BUCKET/$key" \
                --profile $PROFILE \
                --endpoint-url $ENDPOINT \
                --metadata-directive REPLACE \
                --content-type "$content_type" \
                --cache-control "public, max-age=31536000, immutable" \
                --content-disposition "inline; filename=\"$filename\"" \
                --metadata "original-name=$filename,image-type=$extension_lower,gallery=$gallery" \
                2>/dev/null
            
            if [ $? -eq 0 ]; then
                echo "    ✓ Successfully updated"
            else
                echo "    ✗ Failed to update"
            fi
        fi
    done
    echo ""
}

# Update each gallery
for gallery in queens studs kittens; do
    update_gallery $gallery
done

echo "Metadata update complete!"
echo ""

# Verify a sample image from each gallery
echo "=== Verification ==="
for gallery in queens studs kittens; do
    # Get first image from gallery
    first_image=$(aws s3 ls "s3://$BUCKET/$PREFIX/$gallery/" --profile $PROFILE --endpoint-url $ENDPOINT | grep -E "\.(jpg|jpeg|png|gif|JPG|JPEG|PNG)" | head -1 | awk '{print $4}')
    
    if [ ! -z "$first_image" ]; then
        echo "Sample from $gallery gallery: $first_image"
        aws s3api head-object \
            --bucket $BUCKET \
            --key "$PREFIX/$gallery/$first_image" \
            --profile $PROFILE \
            --endpoint-url $ENDPOINT 2>/dev/null | jq '{
            ContentType: .ContentType,
            CacheControl: .CacheControl,
            ContentDisposition: .ContentDisposition,
            Metadata: .Metadata
        }'
        echo ""
    fi
done
