#!/bin/bash

# Update S3 metadata for all images in the yendorcats bucket

echo "Starting S3 metadata update for yendorcats bucket..."

# Get all image files from the bucket
aws s3 ls s3://yendorcats/resources/ --recursive | while read -r line; do
    # Extract the file key from the listing
    key=$(echo "$line" | awk '{print $4}')
    
    # Skip if key is empty
    if [ -z "$key" ]; then
        continue
    fi
    
    # Check if it's an image file (jpg, jpeg, png, gif, webp, avif)
    if [[ "$key" =~ \.(jpg|jpeg|png|gif|webp|avif)$ ]]; then
        echo "Updating metadata for: $key"
        
        # Determine content type based on extension
        extension="${key##*.}"
        case "$extension" in
            jpg|jpeg)
                content_type="image/jpeg"
                ;;
            png)
                content_type="image/png"
                ;;
            gif)
                content_type="image/gif"
                ;;
            webp)
                content_type="image/webp"
                ;;
            avif)
                content_type="image/avif"
                ;;
            *)
                content_type="application/octet-stream"
                ;;
        esac
        
        # Extract just the filename for content-disposition
        filename=$(basename "$key")
        
        # Update the object metadata by copying it to itself with new metadata
        aws s3 cp "s3://yendorcats/$key" "s3://yendorcats/$key" \
            --metadata-directive REPLACE \
            --content-type "$content_type" \
            --cache-control "public, max-age=31536000, immutable" \
            --content-disposition "inline; filename=\"$filename\"" \
            --metadata "original-name=$filename,image-type=$extension" \
            2>/dev/null
        
        if [ $? -eq 0 ]; then
            echo "  ✓ Successfully updated $key"
        else
            echo "  ✗ Failed to update $key"
        fi
    fi
done

echo "Metadata update complete!"

# Verify a sample image to confirm changes
echo -e "\nVerifying metadata for sample image (kitten1.jpg):"
aws s3api head-object --bucket yendorcats --key "resources/kitten1.jpg" | jq '{
    ContentType: .ContentType,
    CacheControl: .CacheControl,
    ContentDisposition: .ContentDisposition,
    Metadata: .Metadata
}'
