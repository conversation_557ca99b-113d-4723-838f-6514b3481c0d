#!/usr/bin/env python3
"""
Test S3 Connection to Backblaze B2
"""

import boto3
from botocore.exceptions import ClientError

# S3 Configuration
S3_CONFIG = {
    'endpoint_url': 'https://s3.us-west-004.backblazeb2.com',
    'access_key_id': '004d0cd685eb5360000000008',
    'secret_access_key': 'K0049vAJ9EscCkyDMkP978wwES+Z2NI'
}

def test_connection():
    """Test S3 connection and list available buckets"""
    
    print("Testing S3 Connection to Backblaze B2")
    print("=" * 60)
    print(f"Endpoint: {S3_CONFIG['endpoint_url']}")
    print(f"Access Key ID: {S3_CONFIG['access_key_id']}")
    print("=" * 60)
    
    try:
        # Create S3 client
        s3_client = boto3.client(
            's3',
            endpoint_url=S3_CONFIG['endpoint_url'],
            aws_access_key_id=S3_CONFIG['access_key_id'],
            aws_secret_access_key=S3_CONFIG['secret_access_key']
        )
        
        # Try to list buckets
        print("\nAttempting to list buckets...")
        response = s3_client.list_buckets()
        
        print(f"\n✓ Connection successful!")
        print(f"\nAvailable buckets:")
        for bucket in response.get('Buckets', []):
            print(f"  - {bucket['Name']}")
            
        # Try to list objects in 'yendor' bucket
        print("\n" + "=" * 60)
        print("Testing access to 'yendor' bucket...")
        
        try:
            response = s3_client.list_objects_v2(
                Bucket='yendor',
                MaxKeys=5  # Just get first 5 objects
            )
            
            if 'Contents' in response:
                print(f"✓ Successfully accessed 'yendor' bucket")
                print(f"Found {response.get('KeyCount', 0)} objects (showing max 5):")
                for obj in response['Contents'][:5]:
                    print(f"  - {obj['Key']} ({obj['Size']} bytes)")
            else:
                print("✓ Bucket accessible but appears to be empty")
                
        except ClientError as e:
            error_code = e.response.get('Error', {}).get('Code', 'Unknown')
            error_msg = e.response.get('Error', {}).get('Message', str(e))
            print(f"✗ Error accessing 'yendor' bucket:")
            print(f"  Error Code: {error_code}")
            print(f"  Message: {error_msg}")
            
            # Try alternate bucket names
            print("\nTrying alternate bucket names...")
            for bucket_name in ['yendorcats', 'YendorCats', 'yendor-cats']:
                try:
                    response = s3_client.list_objects_v2(
                        Bucket=bucket_name,
                        MaxKeys=1
                    )
                    print(f"  ✓ Found accessible bucket: {bucket_name}")
                    break
                except:
                    print(f"  ✗ {bucket_name} - not accessible")
                    
    except ClientError as e:
        error_code = e.response.get('Error', {}).get('Code', 'Unknown')
        error_msg = e.response.get('Error', {}).get('Message', str(e))
        print(f"\n✗ Connection failed!")
        print(f"Error Code: {error_code}")
        print(f"Message: {error_msg}")
        
        if error_code == 'InvalidAccessKeyId':
            print("\n⚠️  The Access Key ID appears to be invalid")
        elif error_code == 'SignatureDoesNotMatch':
            print("\n⚠️  The Secret Access Key appears to be incorrect")
        elif error_code == 'AccessDenied':
            print("\n⚠️  Access denied - check permissions for this application key")
            
    except Exception as e:
        print(f"\n✗ Unexpected error: {e}")

if __name__ == "__main__":
    test_connection()
