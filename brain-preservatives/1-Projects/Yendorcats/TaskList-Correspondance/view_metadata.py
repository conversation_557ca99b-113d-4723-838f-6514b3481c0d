#!/usr/bin/env python3
"""
Interactive S3 Metadata Viewer for YendorCats
"""

import boto3
from botocore.exceptions import ClientError
import json
from tabulate import tabulate
import sys
from typing import Dict, List

# S3 Configuration
S3_CONFIG = {
    'endpoint_url': 'https://s3.us-west-004.backblazeb2.com',
    'bucket_name': 'yendor',
    'access_key_id': '004d0cd685eb5360000000008',
    'secret_access_key': 'K0049vAJ9EscCkyDMkP978wwES+Z2NI'
}

class MetadataViewer:
    def __init__(self):
        self.s3_client = boto3.client(
            's3',
            endpoint_url=S3_CONFIG['endpoint_url'],
            aws_access_key_id=S3_CONFIG['access_key_id'],
            aws_secret_access_key=S3_CONFIG['secret_access_key']
        )
        self.bucket_name = S3_CONFIG['bucket_name']
        self.base_prefix = 'YendorCats-General-SiteAccess/'
    
    def list_cats(self, category: str = None) -> List[Dict]:
        """List all cats with their metadata"""
        prefix = self.base_prefix
        if category:
            prefix += category + '/'
        
        cats = {}
        
        try:
            paginator = self.s3_client.get_paginator('list_objects_v2')
            pages = paginator.paginate(Bucket=self.bucket_name, Prefix=prefix)
            
            for page in pages:
                if 'Contents' in page:
                    for obj in page['Contents']:
                        key = obj['Key']
                        # Skip non-image files
                        if not any(key.lower().endswith(ext) for ext in ['.jpg', '.jpeg', '.png']):
                            continue
                        
                        # Get metadata
                        try:
                            response = self.s3_client.head_object(
                                Bucket=self.bucket_name,
                                Key=key
                            )
                            metadata = response.get('Metadata', {})
                            
                            if metadata and 'name' in metadata:
                                cat_name = metadata['name']
                                if cat_name not in cats:
                                    cats[cat_name] = {
                                        'name': cat_name,
                                        'gender': metadata.get('gender', ''),
                                        'breed': metadata.get('breed', ''),
                                        'color': metadata.get('color', ''),
                                        'birth_date': metadata.get('date-of-birth', ''),
                                        'category': metadata.get('category', ''),
                                        'images': []
                                    }
                                cats[cat_name]['images'].append(key.replace(self.base_prefix, ''))
                        except:
                            pass
        except ClientError as e:
            print(f"Error: {e}")
            
        return list(cats.values())
    
    def view_single_image(self, image_path: str):
        """View metadata for a single image"""
        if not image_path.startswith(self.base_prefix):
            image_path = self.base_prefix + image_path
            
        try:
            response = self.s3_client.head_object(
                Bucket=self.bucket_name,
                Key=image_path
            )
            
            print(f"\n{'='*60}")
            print(f"Image: {image_path}")
            print(f"{'='*60}")
            
            metadata = response.get('Metadata', {})
            
            if metadata:
                # Format metadata nicely
                data = []
                for key, value in sorted(metadata.items()):
                    formatted_key = key.replace('-', ' ').title()
                    data.append([formatted_key, value])
                
                print(tabulate(data, headers=['Field', 'Value'], tablefmt='grid'))
            else:
                print("No metadata found for this image")
                
            # Show file info
            print(f"\nFile Info:")
            print(f"  Size: {response.get('ContentLength', 0):,} bytes")
            print(f"  Type: {response.get('ContentType', 'Unknown')}")
            print(f"  Last Modified: {response.get('LastModified', 'Unknown')}")
            
        except ClientError as e:
            error_code = e.response.get('Error', {}).get('Code', '')
            if error_code == '404':
                print(f"Error: Image not found: {image_path}")
            else:
                print(f"Error: {e}")
    
    def export_metadata(self, output_file: str = 'metadata_export.json'):
        """Export all metadata to JSON file"""
        all_metadata = {}
        
        try:
            paginator = self.s3_client.get_paginator('list_objects_v2')
            pages = paginator.paginate(Bucket=self.bucket_name, Prefix=self.base_prefix)
            
            for page in pages:
                if 'Contents' in page:
                    for obj in page['Contents']:
                        key = obj['Key']
                        # Skip non-image files
                        if not any(key.lower().endswith(ext) for ext in ['.jpg', '.jpeg', '.png']):
                            continue
                        
                        try:
                            response = self.s3_client.head_object(
                                Bucket=self.bucket_name,
                                Key=key
                            )
                            metadata = response.get('Metadata', {})
                            
                            if metadata:
                                all_metadata[key] = metadata
                        except:
                            pass
            
            # Save to file
            with open(output_file, 'w') as f:
                json.dump(all_metadata, f, indent=2)
            
            print(f"✓ Exported metadata for {len(all_metadata)} images to {output_file}")
            
        except Exception as e:
            print(f"Error exporting metadata: {e}")

def main():
    viewer = MetadataViewer()
    
    while True:
        print("\n" + "="*60)
        print("YendorCats S3 Metadata Viewer")
        print("="*60)
        print("\n1. List all cats with summary")
        print("2. List queens only")
        print("3. List studs only")
        print("4. View specific image metadata")
        print("5. Export all metadata to JSON")
        print("6. Exit")
        
        choice = input("\nSelect option (1-6): ").strip()
        
        if choice == '1':
            cats = viewer.list_cats()
            if cats:
                headers = ['Name', 'Gender', 'Color', 'Birth Date', 'Category', '# Images']
                data = []
                for cat in sorted(cats, key=lambda x: x['name']):
                    data.append([
                        cat['name'],
                        cat['gender'],
                        cat['color'][:30] + '...' if len(cat['color']) > 30 else cat['color'],
                        cat['birth_date'],
                        cat['category'],
                        len(cat['images'])
                    ])
                print("\n" + tabulate(data, headers=headers, tablefmt='grid'))
            else:
                print("No cats found with metadata")
                
        elif choice == '2':
            cats = viewer.list_cats('queens')
            if cats:
                headers = ['Name', 'Gender', 'Color', 'Birth Date', '# Images']
                data = []
                for cat in sorted(cats, key=lambda x: x['name']):
                    data.append([
                        cat['name'],
                        cat['gender'],
                        cat['color'][:30] + '...' if len(cat['color']) > 30 else cat['color'],
                        cat['birth_date'],
                        len(cat['images'])
                    ])
                print("\n" + tabulate(data, headers=headers, tablefmt='grid'))
            else:
                print("No queens found with metadata")
                
        elif choice == '3':
            cats = viewer.list_cats('studs')
            if cats:
                headers = ['Name', 'Gender', 'Color', 'Birth Date', '# Images']
                data = []
                for cat in sorted(cats, key=lambda x: x['name']):
                    data.append([
                        cat['name'],
                        cat['gender'],
                        cat['color'][:30] + '...' if len(cat['color']) > 30 else cat['color'],
                        cat['birth_date'],
                        len(cat['images'])
                    ])
                print("\n" + tabulate(data, headers=headers, tablefmt='grid'))
            else:
                print("No studs found with metadata")
                
        elif choice == '4':
            print("\nEnter image path (e.g., 'queens/IMG_8520.jpg' or full path):")
            image_path = input("> ").strip()
            if image_path:
                viewer.view_single_image(image_path)
                
        elif choice == '5':
            filename = input("Enter output filename (default: metadata_export.json): ").strip()
            if not filename:
                filename = 'metadata_export.json'
            viewer.export_metadata(filename)
            
        elif choice == '6':
            print("\nExiting...")
            break
        else:
            print("Invalid option")

if __name__ == "__main__":
    main()
