#!/usr/bin/env python3
"""
Test different prefix combinations to find accessible paths in Backblaze B2
"""

import boto3
from botocore.exceptions import ClientError

# S3 Configuration
S3_CONFIG = {
    'endpoint_url': 'https://s3.us-west-004.backblazeb2.com',
    'access_key_id': '004d0cd685eb5360000000008',
    'secret_access_key': 'K0049vAJ9EscCkyDMkP978wwES+Z2NI'
}

def test_prefixes():
    """Test various prefix combinations"""
    
    print("Testing Prefix Access for Backblaze B2")
    print("=" * 60)
    print(f"Bucket: yendor")
    print(f"Key Name: YendorCats-S3-Access")
    print(f"Name Prefix: YendorCats-General-SiteAccess")
    print("=" * 60)
    
    # Create S3 client
    s3_client = boto3.client(
        's3',
        endpoint_url=S3_CONFIG['endpoint_url'],
        aws_access_key_id=S3_CONFIG['access_key_id'],
        aws_secret_access_key=S3_CONFIG['secret_access_key']
    )
    
    # Test different prefix patterns
    prefixes_to_test = [
        '',  # No prefix
        'YendorCats-General-SiteAccess',
        'YendorCats-General-SiteAccess/',
        'YendorCats-General-SiteAccess/queens',
        'YendorCats-General-SiteAccess/queens/',
        'YendorCats-General-SiteAccess/studs',
        'YendorCats-General-SiteAccess/studs/',
        'YendorCats-General-SiteAccess/kittens',
        'YendorCats-General-SiteAccess/kittens/',
        'queens',
        'queens/',
        'studs',
        'studs/',
        'kittens',
        'kittens/',
        'images',
        'images/',
        'gallery',
        'gallery/',
    ]
    
    print("\nTesting different prefixes:\n")
    
    accessible_prefixes = []
    
    for prefix in prefixes_to_test:
        try:
            # Try to list objects with this prefix
            response = s3_client.list_objects_v2(
                Bucket='yendor',
                Prefix=prefix,
                MaxKeys=3  # Just check if we can access
            )
            
            if 'Contents' in response:
                count = response.get('KeyCount', 0)
                print(f"✓ PREFIX: '{prefix}' - Found {count} objects")
                accessible_prefixes.append(prefix)
                
                # Show first few objects
                for obj in response['Contents'][:2]:
                    print(f"    - {obj['Key']} ({obj['Size']} bytes)")
                    
            else:
                # Can access but no objects
                print(f"✓ PREFIX: '{prefix}' - Accessible but empty")
                accessible_prefixes.append(prefix)
                
        except ClientError as e:
            error_code = e.response.get('Error', {}).get('Code', '')
            if error_code == 'AccessDenied':
                print(f"✗ PREFIX: '{prefix}' - Access Denied")
            else:
                print(f"✗ PREFIX: '{prefix}' - Error: {error_code}")
    
    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    
    if accessible_prefixes:
        print(f"\nAccessible prefixes ({len(accessible_prefixes)}):")
        for prefix in accessible_prefixes:
            print(f"  - '{prefix}'")
        
        print("\nRECOMMENDATION:")
        print("Your images should be stored under one of these prefixes.")
        
        if 'YendorCats-General-SiteAccess' in accessible_prefixes[0]:
            print("\nIt appears your app key requires the 'YendorCats-General-SiteAccess' prefix.")
            print("Make sure your cat images are stored with paths like:")
            print("  - YendorCats-General-SiteAccess/queens/image.jpg")
            print("  - YendorCats-General-SiteAccess/studs/image.jpg")
    else:
        print("\n✗ No accessible prefixes found!")
        print("\nThis app key appears to have no access to the 'yendor' bucket.")
        print("Please check:")
        print("1. The app key has the correct bucket permissions")
        print("2. The prefix name in the app key settings matches your file structure")

if __name__ == "__main__":
    test_prefixes()
