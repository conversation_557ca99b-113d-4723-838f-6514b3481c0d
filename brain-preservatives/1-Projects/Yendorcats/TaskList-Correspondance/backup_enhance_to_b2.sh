#!/bin/bash

# Enhance Control Panel Backup to Backblaze B2
# This script backs up the entire Enhance installation before system wipe

set -e  # Exit on error

# Configuration
BACKUP_DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="enhance_backup_${BACKUP_DATE}"
TEMP_DIR="/tmp/${BACKUP_NAME}"
B2_BUCKET_NAME="enhance-backups"  # Change this to your bucket name

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if running as root
if [ "$EUID" -ne 0 ]; then 
    error "This script must be run as root (use sudo)"
    exit 1
fi

# Check if b2 CLI is installed
if ! command -v b2 &> /dev/null; then
    error "Backblaze B2 CLI is not installed or not in PATH"
    echo "Install it with: pip install b2"
    exit 1
fi

log "Starting Enhance Control Panel backup process..."

# Step 1: Check B2 authentication
log "Checking B2 authentication..."
if ! b2 account get 2>/dev/null; then
    error "B2 is not authenticated. Please run: b2 account authorize"
    exit 1
fi

# Step 2: Check/Create B2 bucket
log "Checking B2 bucket: ${B2_BUCKET_NAME}"
if ! b2 bucket list | grep -q "${B2_BUCKET_NAME}"; then
    warning "Bucket ${B2_BUCKET_NAME} doesn't exist. Creating it..."
    b2 bucket create "${B2_BUCKET_NAME}" allPrivate
    log "Bucket created successfully"
fi

# Step 3: Stop Enhance services (optional but recommended for consistency)
log "Stopping Enhance services for consistent backup..."
if systemctl is-active --quiet enhance; then
    systemctl stop enhance || warning "Could not stop enhance service"
    ENHANCE_WAS_RUNNING=true
else
    ENHANCE_WAS_RUNNING=false
fi

# Step 4: Create temporary directory
log "Creating temporary backup directory: ${TEMP_DIR}"
mkdir -p "${TEMP_DIR}"

# Step 5: Backup Enhance data directories
log "Backing up Enhance data directories..."

# Main Enhance data
if [ -d "/var/local/enhance" ]; then
    log "Archiving /var/local/enhance (this may take a while)..."
    tar -czf "${TEMP_DIR}/var_local_enhance.tar.gz" -C / var/local/enhance 2>/dev/null || \
        warning "Some files could not be read, continuing..."
else
    warning "/var/local/enhance not found"
fi

# PHP installations
if [ -d "/opt" ]; then
    log "Archiving PHP installations from /opt..."
    tar -czf "${TEMP_DIR}/opt_ecp_php.tar.gz" -C /opt ecp-php* 2>/dev/null || \
        warning "Some PHP installations might be missing"
fi

# Backup Enhance configuration files
log "Backing up configuration files..."
mkdir -p "${TEMP_DIR}/configs"

# System configuration files that might be related to Enhance
for config_path in \
    "/etc/enhance" \
    "/etc/nginx" \
    "/etc/apache2" \
    "/etc/systemd/system/enhance"* \
    "/etc/cron.d/enhance"*
do
    if [ -e "${config_path}" ]; then
        config_name=$(echo "${config_path}" | tr '/' '_')
        log "Backing up ${config_path}..."
        if [ -d "${config_path}" ]; then
            tar -czf "${TEMP_DIR}/configs/${config_name}.tar.gz" -C / "${config_path#/}" 2>/dev/null
        else
            cp -p "${config_path}" "${TEMP_DIR}/configs/" 2>/dev/null
        fi
    fi
done

# Step 6: Create metadata file
log "Creating backup metadata..."
cat > "${TEMP_DIR}/backup_metadata.txt" << EOF
Enhance Control Panel Backup
Date: $(date)
Hostname: $(hostname)
OS: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)
Kernel: $(uname -r)
Enhance Version: $(enhance version 2>/dev/null || echo "Unknown")
Backup Size: $(du -sh ${TEMP_DIR} | cut -f1)
EOF

# Step 7: List all backed up files
log "Creating file manifest..."
find "${TEMP_DIR}" -type f -exec ls -lh {} \; > "${TEMP_DIR}/file_manifest.txt"

# Step 8: Upload to Backblaze B2
log "Starting upload to Backblaze B2..."
log "This may take a long time depending on your internet speed and data size..."

# Upload each file individually for better progress tracking
for file in "${TEMP_DIR}"/*.tar.gz "${TEMP_DIR}"/*.txt; do
    if [ -f "${file}" ]; then
        filename=$(basename "${file}")
        log "Uploading ${filename}..."
        b2 file upload \
            --contentType "application/gzip" \
            "${B2_BUCKET_NAME}" \
            "${file}" \
            "${BACKUP_NAME}/${filename}" || \
            error "Failed to upload ${filename}"
    fi
done

# Upload configs directory
if [ -d "${TEMP_DIR}/configs" ]; then
    log "Uploading configuration files..."
    for config_file in "${TEMP_DIR}/configs"/*; do
        if [ -f "${config_file}" ]; then
            filename=$(basename "${config_file}")
            b2 file upload \
                "${B2_BUCKET_NAME}" \
                "${config_file}" \
                "${BACKUP_NAME}/configs/${filename}" || \
                warning "Failed to upload config: ${filename}"
        fi
    done
fi

# Step 9: Verify upload
log "Verifying uploaded files..."
b2 ls "${B2_BUCKET_NAME}" "${BACKUP_NAME}/" > "${TEMP_DIR}/upload_verification.txt"
log "Upload verification saved to ${TEMP_DIR}/upload_verification.txt"

# Step 10: Create restore instructions
log "Creating restore instructions..."
cat > "${TEMP_DIR}/RESTORE_INSTRUCTIONS.md" << 'EOF'
# Enhance Control Panel Restore Instructions

## Prerequisites
1. Fresh Ubuntu/Debian installation
2. Backblaze B2 CLI installed: `pip install b2`
3. B2 credentials configured

## Restore Process

1. Authenticate with B2:
   ```bash
   b2 account authorize
   ```

2. List available backups:
   ```bash
   b2 ls enhance-backups
   ```

3. Download backup files:
   ```bash
   mkdir /tmp/enhance_restore
   cd /tmp/enhance_restore
   b2 sync b2://enhance-backups/enhance_backup_YYYYMMDD_HHMMSS .
   ```

4. Extract the archives:
   ```bash
   sudo tar -xzf var_local_enhance.tar.gz -C /
   sudo tar -xzf opt_ecp_php.tar.gz -C /
   ```

5. Restore configuration files:
   ```bash
   cd configs
   for file in *.tar.gz; do
       sudo tar -xzf "$file" -C /
   done
   ```

6. Reinstall Enhance (if needed):
   ```bash
   curl -s https://enhance.com/install | bash
   ```

7. Restart services:
   ```bash
   sudo systemctl restart enhance
   ```

## Important Notes
- Database data is included in /var/local/enhance
- SSL certificates are preserved in the backup
- Container data is included but may need reconfiguration
EOF

# Upload restore instructions
b2 file upload \
    "${B2_BUCKET_NAME}" \
    "${TEMP_DIR}/RESTORE_INSTRUCTIONS.md" \
    "${BACKUP_NAME}/RESTORE_INSTRUCTIONS.md"

# Step 11: Restart Enhance if it was running
if [ "${ENHANCE_WAS_RUNNING}" = true ]; then
    log "Restarting Enhance services..."
    systemctl start enhance || warning "Could not restart enhance service"
fi

# Step 12: Cleanup (optional - comment out if you want to keep local backup)
log "Cleaning up temporary files..."
# rm -rf "${TEMP_DIR}"

# Step 13: Final summary
echo ""
log "================================="
log "Backup completed successfully!"
log "Backup name: ${BACKUP_NAME}"
log "Bucket: ${B2_BUCKET_NAME}"
log "Local temp files: ${TEMP_DIR}"
log "================================="
log ""
log "To restore this backup later, download and follow:"
log "b2://enhance-backups/${BACKUP_NAME}/RESTORE_INSTRUCTIONS.md"
log ""
warning "IMPORTANT: Save this backup name for future reference: ${BACKUP_NAME}"
warning "Consider downloading a local copy as well before wiping the drive!"
