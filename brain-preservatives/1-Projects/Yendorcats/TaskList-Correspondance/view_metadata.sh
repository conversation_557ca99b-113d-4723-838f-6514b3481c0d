#!/bin/bash

# YendorCats S3 Metadata Viewer - CLI Version
# Usage: ./view_metadata.sh [image-path]

BUCKET="yendor"
ENDPOINT="https://s3.us-west-004.backblazeb2.com"
PROFILE="backblaze"
PREFIX="YendorCats-General-SiteAccess"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

function view_metadata() {
    local key="$1"
    
    # Add prefix if not already there
    if [[ ! "$key" == "$PREFIX"* ]]; then
        key="$PREFIX/$key"
    fi
    
    echo -e "${BLUE}Fetching metadata for: $key${NC}"
    echo "================================================"
    
    # Get metadata using AWS CLI
    result=$(aws s3api head-object \
        --bucket "$BUCKET" \
        --key "$key" \
        --endpoint-url="$ENDPOINT" \
        --profile "$PROFILE" \
        --output json 2>/dev/null)
    
    if [ $? -eq 0 ]; then
        # Extract and display metadata
        echo -e "${GREEN}✓ Metadata found:${NC}"
        echo "$result" | jq -r '.Metadata | to_entries[] | "  \(.key): \(.value)"'
        
        echo ""
        echo -e "${YELLOW}File Info:${NC}"
        echo "$result" | jq -r '"  Size: \(.ContentLength) bytes"'
        echo "$result" | jq -r '"  Type: \(.ContentType)"'
        echo "$result" | jq -r '"  Last Modified: \(.LastModified)"'
    else
        echo -e "${RED}✗ Error: Could not fetch metadata${NC}"
        echo "  Make sure the image path is correct"
        echo "  Example: queens/IMG_8520.jpg"
    fi
}

function list_images() {
    local path="$PREFIX/$1"
    
    echo -e "${BLUE}Listing images in: $path${NC}"
    echo "================================================"
    
    aws s3 ls "s3://$BUCKET/$path" \
        --endpoint-url="$ENDPOINT" \
        --profile "$PROFILE" \
        --recursive \
        | grep -E '\.(jpg|jpeg|png)$' \
        | awk '{print $4}' \
        | sed "s|^$PREFIX/||"
}

function show_help() {
    echo "YendorCats S3 Metadata Viewer"
    echo "=============================="
    echo ""
    echo "Usage:"
    echo "  $0                           - Show this help"
    echo "  $0 list [category]           - List images (category: queens/studs/kittens)"
    echo "  $0 view <image-path>         - View metadata for specific image"
    echo ""
    echo "Examples:"
    echo "  $0 list queens               - List all queen images"
    echo "  $0 view queens/IMG_8520.jpg  - View metadata for specific image"
    echo ""
}

# Main script logic
if [ $# -eq 0 ]; then
    show_help
elif [ "$1" == "list" ]; then
    list_images "$2"
elif [ "$1" == "view" ]; then
    if [ -z "$2" ]; then
        echo -e "${RED}Error: Please provide an image path${NC}"
        echo "Example: $0 view queens/IMG_8520.jpg"
    else
        view_metadata "$2"
    fi
else
    view_metadata "$1"
fi
