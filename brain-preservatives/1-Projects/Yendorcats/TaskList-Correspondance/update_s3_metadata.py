#!/usr/bin/env python3
"""
YendorCats S3 Metadata Batch Update Script
Updates metadata for cat images stored in Backblaze B2 S3-compatible storage
"""

import boto3
from botocore.exceptions import ClientError
import json
from datetime import datetime
from typing import Dict, List, Optional
import os
import sys

# S3 Configuration
S3_CONFIG = {
    'endpoint_url': 'https://s3.us-west-004.backblazeb2.com',
    'bucket_name': 'yendor',
    'access_key_id': '004d0cd685eb5360000000008',
    'secret_access_key': 'K0049vAJ9EscCkyDMkP978wwES+Z2NI'  # You'll need to add this
}

# Cat metadata based on client correspondence
CATS_METADATA = {
    # Queens
    'queens': [
        {
            'name': 'Indy',
            'filename_patterns': ['4400', 'indy'],
            'metadata': {
                'name': 'Indy',
                'gender': 'F',
                'breed': 'Maine Coon',
                'color': 'black smoke tortie',
                'hair_color': 'black smoke tortie',
                'date_of_birth': '27.10.20',
                'birth_date': '2020-10-27',
                'category': 'queens',
                'description': 'Black smoke tortie queen',
                'type': 'queen'
            }
        },
        {
            'name': 'Rosie',
            'filename_patterns': ['4406', 'rosie'],
            'metadata': {
                'name': 'Rosie',
                'gender': 'F',
                'breed': 'Maine Coon',
                'color': 'black silver tortie tabby',
                'hair_color': 'black silver tortie tabby',
                'date_of_birth': '8.12.21',
                'birth_date': '2021-12-08',
                'category': 'queens',
                'description': 'Black silver tortie tabby queen',
                'type': 'queen'
            }
        },
        {
            'name': 'Athena',
            'filename_patterns': ['4275', 'athena'],
            'metadata': {
                'name': 'Athena',
                'gender': 'F',
                'breed': 'Maine Coon',
                'color': 'black smoke and white',
                'hair_color': 'black smoke and white',
                'date_of_birth': '27.4.22',
                'birth_date': '2022-04-27',
                'category': 'queens',
                'description': 'Black smoke and white queen',
                'type': 'queen'
            }
        },
        {
            'name': 'Jeannie',
            'filename_patterns': ['8520', 'jeannie'],
            'metadata': {
                'name': 'Jeannie',
                'gender': 'F',
                'breed': 'Maine Coon',
                'color': 'black silver and white tortie tabby',
                'hair_color': 'black silver and white tortie tabby',
                'date_of_birth': '18.3.23',
                'birth_date': '2023-03-18',
                'category': 'queens',
                'description': 'Black silver and white tortie tabby queen',
                'type': 'queen'
            }
        },
        {
            'name': 'Sera',
            'filename_patterns': ['6277', 'sera'],
            'metadata': {
                'name': 'Sera',
                'gender': 'F',
                'breed': 'Maine Coon',
                'color': 'black tabby bi colour',
                'hair_color': 'black tabby bi colour',
                'date_of_birth': '9.4.23',
                'birth_date': '2023-04-09',
                'category': 'queens',
                'description': 'Black tabby bi colour queen',
                'type': 'queen'
            }
        },
        {
            'name': 'Anji',
            'filename_patterns': ['7867', 'anji'],
            'metadata': {
                'name': 'Anji',
                'gender': 'F',
                'breed': 'Maine Coon',
                'color': 'black tortie tabby',
                'hair_color': 'black tortie tabby',
                'date_of_birth': '2.6.23',
                'birth_date': '2023-06-02',
                'category': 'queens',
                'description': 'Black tortie tabby queen',
                'type': 'queen'
            }
        },
        {
            'name': 'Loretta',
            'filename_patterns': ['9260', 'loretta'],
            'metadata': {
                'name': 'Loretta',
                'gender': 'F',
                'breed': 'Maine Coon',
                'color': 'black silver tabby',
                'hair_color': 'black silver tabby',
                'date_of_birth': '29.8.23',
                'birth_date': '2023-08-29',
                'category': 'queens',
                'description': 'Black silver tabby queen',
                'type': 'queen'
            }
        }
    ],
    # Studs
    'studs': [
        {
            'name': 'Louie',
            'filename_patterns': ['louie', '510586750', '510810423', '511025583', '510851562', '511277439', '511983065'],
            'metadata': {
                'name': 'Louie',
                'gender': 'M',
                'breed': 'Maine Coon',
                'color': 'blue silver tabby',
                'hair_color': 'blue silver tabby',
                'date_of_birth': '6.6.24',
                'birth_date': '2024-06-06',
                'category': 'studs',
                'mother': 'Athena',
                'father': 'Soren',
                'description': 'Blue silver tabby stud, son of Soren and Athena. Youngest stud in the Yendor cattery.',
                'personality': 'From just a little kitten not that long ago to the promise of great things to come',
                'type': 'stud'
            }
        },
        {
            'name': 'Soren',
            'filename_patterns': ['soren', '501049857', '509926341', '511218592', '511991955', '512647539'],
            'metadata': {
                'name': 'Soren',
                'gender': 'M',
                'breed': 'Maine Coon',
                'color': 'blue/silver tabby and white',
                'hair_color': 'blue/silver tabby and white',
                'date_of_birth': '01.10.20',
                'birth_date': '2020-10-01',
                'category': 'studs',
                'description': 'Blue/silver tabby and white stud. Big purr machine rumbling away like a V8.',
                'personality': 'He has attitude but he\'s a softie at heart. At 4 he\'s an invaluable part of the Yendor cattery',
                'type': 'stud'
            }
        },
        {
            'name': 'Dennis',
            'filename_patterns': ['dennis', '501052875', '508553919', '511009104', '511273385'],
            'metadata': {
                'name': 'Dennis',
                'gender': 'M',
                'breed': 'Maine Coon',
                'color': 'black silver mackerel tabby',
                'hair_color': 'black silver mackerel tabby',
                'date_of_birth': '01.08.14',
                'birth_date': '2014-08-01',
                'category': 'studs',
                'description': 'Black silver mackerel tabby stud. An older gentleman in the Yendor cattery.',
                'personality': 'He\'s a big boy, a real sweetheart with a lovely nature that lives on in so many of the gorgeous kittens he\'s fathered',
                'type': 'stud'
            }
        }
    ]
}


class S3MetadataUpdater:
    """Handles updating S3 object metadata for cat images"""
    
    def __init__(self, access_key_id: str, secret_access_key: str, endpoint_url: str, bucket_name: str):
        """Initialize S3 client with Backblaze B2 configuration"""
        self.bucket_name = bucket_name
        self.s3_client = boto3.client(
            's3',
            endpoint_url=endpoint_url,
            aws_access_key_id=access_key_id,
            aws_secret_access_key=secret_access_key
        )
        
    def list_objects(self, prefix: str = '') -> List[str]:
        """List all objects in the bucket with optional prefix"""
        objects = []
        
        # Handle potential prefix restrictions from Backblaze B2 app key
        # The key name suggests it might require 'YendorCats-General-SiteAccess' prefix
        prefixes_to_try = [
            prefix,  # Original prefix
            'YendorCats-General-SiteAccess/' + prefix,  # With app key prefix
            'YendorCats-General-SiteAccess',  # Just the app key prefix
        ]
        
        for try_prefix in prefixes_to_try:
            try:
                print(f"Trying prefix: '{try_prefix}'")
                paginator = self.s3_client.get_paginator('list_objects_v2')
                pages = paginator.paginate(Bucket=self.bucket_name, Prefix=try_prefix)
                
                found_objects = False
                for page in pages:
                    if 'Contents' in page:
                        found_objects = True
                        for obj in page['Contents']:
                            objects.append(obj['Key'])
                
                if found_objects:
                    print(f"✓ Found objects with prefix: '{try_prefix}'")
                    break
                    
            except ClientError as e:
                error_code = e.response.get('Error', {}).get('Code', '')
                if error_code != 'AccessDenied':
                    print(f"Error listing objects with prefix '{try_prefix}': {e}")
        
        if not objects:
            print("No objects found. The app key might be restricted to a specific prefix.")
            print("Check if your images are stored under 'YendorCats-General-SiteAccess/' prefix")
            
        return objects
    
    def get_object_metadata(self, key: str) -> Dict:
        """Get current metadata for an object"""
        try:
            response = self.s3_client.head_object(Bucket=self.bucket_name, Key=key)
            return response.get('Metadata', {})
        except ClientError as e:
            print(f"Error getting metadata for {key}: {e}")
            return {}
    
    def update_object_metadata(self, key: str, metadata: Dict, dry_run: bool = False) -> bool:
        """
        Update metadata for an S3 object
        Note: In S3, updating metadata requires copying the object to itself with new metadata
        """
        if dry_run:
            print(f"[DRY RUN] Would update {key} with metadata: {json.dumps(metadata, indent=2)}")
            return True
            
        try:
            # Get current object information
            response = self.s3_client.head_object(Bucket=self.bucket_name, Key=key)
            content_type = response.get('ContentType', 'image/jpeg')
            
            # Copy object to itself with new metadata
            copy_source = {'Bucket': self.bucket_name, 'Key': key}
            
            # Prepare metadata - S3 metadata keys should be lowercase
            clean_metadata = {k.replace('_', '-').lower(): str(v) for k, v in metadata.items()}
            
            self.s3_client.copy_object(
                Bucket=self.bucket_name,
                Key=key,
                CopySource=copy_source,
                Metadata=clean_metadata,
                MetadataDirective='REPLACE',
                ContentType=content_type,
                ACL='public-read'  # Maintain public read access for website display
            )
            
            print(f"✓ Updated metadata for: {key}")
            return True
            
        except ClientError as e:
            print(f"✗ Error updating metadata for {key}: {e}")
            return False
    
    def find_cat_for_image(self, image_key: str) -> Optional[Dict]:
        """Find matching cat metadata based on image filename patterns"""
        image_lower = image_key.lower()
        
        # Check both categories
        for category in ['queens', 'studs']:
            for cat in CATS_METADATA.get(category, []):
                for pattern in cat['filename_patterns']:
                    if pattern.lower() in image_lower:
                        return cat['metadata']
        
        return None
    
    def batch_update_metadata(self, dry_run: bool = True, prefix: str = ''):
        """
        Batch update metadata for all matching images
        
        Args:
            dry_run: If True, only show what would be updated without making changes
            prefix: Optional prefix to filter objects (e.g., 'queens/', 'studs/')
        """
        print(f"\n{'='*60}")
        print(f"Starting S3 Metadata Batch Update")
        print(f"Mode: {'DRY RUN' if dry_run else 'LIVE UPDATE'}")
        print(f"Bucket: {self.bucket_name}")
        
        # Adjust prefix for Backblaze B2 app key restrictions
        base_prefix = 'YendorCats-General-SiteAccess/'
        if prefix:
            full_prefix = base_prefix + prefix
            print(f"Prefix: {full_prefix}")
        else:
            full_prefix = base_prefix
            print(f"Base Prefix: {base_prefix}")
        print(f"{'='*60}\n")
        
        # Get all objects
        objects = self.list_objects(full_prefix)
        print(f"Found {len(objects)} objects in bucket\n")
        
        updated = 0
        skipped = 0
        errors = 0
        
        for obj_key in objects:
            # Skip non-image files
            if not any(obj_key.lower().endswith(ext) for ext in ['.jpg', '.jpeg', '.png', '.gif', '.webp']):
                continue
                
            print(f"\nProcessing: {obj_key}")
            
            # Find matching cat metadata
            cat_metadata = self.find_cat_for_image(obj_key)
            
            if cat_metadata:
                # Get current metadata
                current_metadata = self.get_object_metadata(obj_key)
                
                # Add timestamp
                cat_metadata['date_uploaded'] = datetime.utcnow().isoformat() + 'Z'
                
                # Check if update is needed
                needs_update = False
                for key, value in cat_metadata.items():
                    current_value = current_metadata.get(key.replace('_', '-').lower(), '')
                    if str(value) != current_value:
                        needs_update = True
                        break
                
                if needs_update:
                    if self.update_object_metadata(obj_key, cat_metadata, dry_run):
                        updated += 1
                    else:
                        errors += 1
                else:
                    print(f"  → Metadata already up to date")
                    skipped += 1
            else:
                print(f"  → No matching cat metadata found")
                skipped += 1
        
        # Summary
        print(f"\n{'='*60}")
        print(f"Batch Update Summary")
        print(f"{'='*60}")
        print(f"Total images processed: {updated + skipped + errors}")
        print(f"Updated: {updated}")
        print(f"Skipped: {skipped}")
        print(f"Errors: {errors}")
        print(f"{'='*60}\n")


def main():
    """Main execution function"""
    
    # Check for secret key
    secret_key = S3_CONFIG.get('secret_access_key')
    
    if not secret_key:
        secret_key = os.environ.get('BACKBLAZE_SECRET_KEY')
        
    if not secret_key:
        print("\n⚠️  Secret access key not found!")
        print("\nPlease provide the secret access key in one of these ways:")
        print("1. Set it directly in the script (S3_CONFIG['secret_access_key'])")
        print("2. Set environment variable: export BACKBLAZE_SECRET_KEY='your-secret-key'")
        print("3. Enter it now:")
        
        secret_key = input("Secret Access Key: ").strip()
        
        if not secret_key:
            print("\n❌ Cannot proceed without secret access key")
            sys.exit(1)
    
    # Initialize updater
    updater = S3MetadataUpdater(
        access_key_id=S3_CONFIG['access_key_id'],
        secret_access_key=secret_key,
        endpoint_url=S3_CONFIG['endpoint_url'],
        bucket_name=S3_CONFIG['bucket_name']
    )
    
    # Menu
    print("\n" + "="*60)
    print("YendorCats S3 Metadata Update Tool")
    print("="*60)
    print("\nOptions:")
    print("1. Dry run - Show what would be updated (no changes)")
    print("2. Update all images")
    print("3. Update only queens images")
    print("4. Update only studs images")
    print("5. Exit")
    
    choice = input("\nSelect option (1-5): ").strip()
    
    if choice == '1':
        updater.batch_update_metadata(dry_run=True)
    elif choice == '2':
        confirm = input("\n⚠️  This will update metadata for ALL images. Continue? (yes/no): ").strip().lower()
        if confirm == 'yes':
            updater.batch_update_metadata(dry_run=False)
        else:
            print("Update cancelled")
    elif choice == '3':
        confirm = input("\n⚠️  This will update metadata for QUEENS images. Continue? (yes/no): ").strip().lower()
        if confirm == 'yes':
            updater.batch_update_metadata(dry_run=False, prefix='queens/')
        else:
            print("Update cancelled")
    elif choice == '4':
        confirm = input("\n⚠️  This will update metadata for STUDS images. Continue? (yes/no): ").strip().lower()
        if confirm == 'yes':
            updater.batch_update_metadata(dry_run=False, prefix='studs/')
        else:
            print("Update cancelled")
    elif choice == '5':
        print("\nExiting...")
        sys.exit(0)
    else:
        print("\n❌ Invalid option")
        sys.exit(1)


if __name__ == "__main__":
    main()
