---
# CORE METADATA
creation_date: 2025-08-18
modification_date: 2025-08-18
type: project
status: active
priority: critical
area_category: Software-Development
owner: Jordan
tags: [para/projects, software-dev]

# PROJECT MANAGEMENT
project_client: Personal
completion_percentage: 0
estimated_hours: 10
actual_hours: 0
deadline: 2025-09-17
start_date: 2025-08-18

# RELATIONSHIPS (Enhanced System)
related_areas: ["[[Software-Development]]"]
related_resources: []
related_people: ["[[Jordan]]"]
stakeholders: []
dependencies: []
deliverables: []

# TASK MANAGEMENT
task_priority: urgent
task_context: admin
---

# Remote Deployment Script Usage

## Overview
<!-- Brief description of the project -->

## Objectives
<!-- What are you trying to achieve? -->
#remote #instance #vpc #vps #docker #deployment #scripts #pull #docker-pull #ecr #repo #image #yendor #deploy #tag 
For future deployments after the repository reconfiguration, you can use:
```bash
# If you tag with 'latest':
./deploy-with-tag.sh latest

# Or if you tag with a specific commit hash:
./deploy-with-tag.sh <commit-hash>
```
*Note: You might want to update the update-compose-tag.sh script to handle replacing any existing tag (not just :latest) with the new tag to make it more robust.*

---

## Success Criteria
<!-- How will you know when the project is successful? -->
- Updates reflected on the remote machine

## 🎯 Priority Tasks
### 🔥 Critical & Urgent
- [ ]

### ⚡ High Priority
- [ ]

### 📋 Medium Priority
- [ ]

## 🚀 Project Tasks by Priority
```dataview

TABLE WITHOUT ID
  "🔥" as "",
  file.link as "URGENT TASKS",
  choice(estimated_time, estimated_time, "?") as "Time",
  choice(deadline, deadline, "ASAP") as "Due"
FROM this.file.folder
WHERE !completed
  AND task_priority = "urgent"
SORT file.path ASC
LIMIT 5

```

```dataview

TABLE WITHOUT ID
  "⚡" as "",
  file.link as "HIGH PRIORITY",
  choice(estimated_time, estimated_time, "?") as "Time",
  choice(deadline, deadline, "Soon") as "Due"
FROM this.file.folder
WHERE !completed
  AND task_priority = "high"
SORT due ASC, file.path ASC
LIMIT 8

```



## Timeline
- **Start Date**: 2025-08-18
- **Deadline**: 2025-09-17
- **Milestones**:
  - [ ] Initial Planning - 2025-08-25
  - [ ] Development - 2025-09-01
  - [ ] Testing - 2025-09-08
  - [ ] Completion - 2025-09-17

## Resources
<!-- Links to relevant resources -->
-

## 📋 Related Areas
```dataview

TABLE WITHOUT ID
  file.link as "📋 Area",
  responsibility_level as "Priority",
  next_review_date as "Next Review"
FROM "2-Areas"
WHERE contains(related_projects, "Remote Deployment Script Usage")
  AND status = "active"
SORT choice(responsibility_level = "critical", 1, choice(responsibility_level = "high", 2, 3)) ASC
LIMIT 3

```



## 📚 Essential Resources
```dataview

TABLE WITHOUT ID
  file.link as "📚 Resource",
  choice(difficulty = "beginner", "🟢 Easy", choice(difficulty = "intermediate", "🟡 Medium", "🔴 Advanced")) as "Level",
  source as "Source"
FROM "3-Resources"
WHERE (contains(related_projects, "Remote Deployment Script Usage")
   OR contains(tags, "software-dev")
   OR area_category = "Software-Development")
  AND file.name != "Remote Deployment Script Usage"
SORT choice(contains(related_projects, "Remote Deployment Script Usage"), 1, 2) ASC, difficulty ASC
LIMIT 4

```



## 🔗 Project Dependencies
```dataview

TABLE WITHOUT ID
  file.link as "🔗 Depends On",
  type as "Type",
  choice(status = "completed", "✅", choice(status = "active", "🔄", "⏸️")) as "Status"
FROM ""
WHERE contains(this.dependencies, file.name)
SORT choice(status = "completed", 3, choice(status = "active", 1, 2)) ASC

```



## 📝 Recent Project Activity
```dataview

TABLE WITHOUT ID
  file.link as "📝 Recent Activity",
  type as "Type",
  file.mtime as "Modified"
FROM ""
WHERE (contains(related_projects, "Remote Deployment Script Usage")
   OR contains(related_areas, "Remote Deployment Script Usage")
   OR contains(related_resources, "Remote Deployment Script Usage"))
  AND file.mtime >= date(today) - dur(7 days)
SORT file.mtime DESC
LIMIT 5

```



## 📅 Meeting Notes
```dataview

TABLE WITHOUT ID
  file.link as "📅 Meeting",
  date as "Date",
  participants as "Participants"
FROM #meeting
WHERE contains(related_projects, "Remote Deployment Script Usage") OR contains(file.name, "Remote Deployment Script Usage")
SORT date DESC
LIMIT 5

```



## Progress Updates
<!-- Regular updates on project progress -->
### 2025-08-18 - Initial Setup
- Project created
- Initial planning started

## Quick Links
- [[Remote Deployment Script Usage Meeting|New Meeting]]
- [[Remote Deployment Script Usage Resource|New Resource]]
- [[1-Projects|All Projects]]
