---
title: "Docker Deployment Guide - YendorCats"
tags: [docker, deployment, staging, production, setup, configuration]
aliases: [deployment-guide, docker-deploy, container-deployment]
created: 2025-07-27
updated: 2025-07-27
type: guide
status: complete
---

# Docker Deployment Guide

> **Navigation**: [[README]] | [[Docker Quick Reference]] | [[Docker Production Setup]]

---

## 📋 Prerequisites

### System Requirements
- **Operating System**: Linux, macOS, or Windows with WSL2
- **Docker**: Version 20.10 or higher
- **Docker Compose**: Version 2.0 or higher
- **Memory**: Minimum 4GB RAM (8GB recommended)
- **Storage**: 10GB free disk space (20GB recommended)
- **Network**: Internet access for image downloads

### Software Installation
```bash
# Install Docker (Ubuntu/Debian)
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Verify installation
docker --version
docker-compose --version
```

### Required Credentials
- **S3/B2 Storage**: Access keys and bucket configuration
- **JWT Secret**: Secure random string (32+ characters)
- **Admin Credentials**: Default admin user setup

> **Detailed Setup**: [[Environment Variables Reference]]

---

## 🏗 Architecture Overview

### Service Architecture
```mermaid
graph TB
    subgraph "Docker Network: yendorcats-staging-network"
        Frontend[Frontend Container<br/>nginx:alpine<br/>Port 80/443]
        API[API Container<br/>.NET Core 8.0<br/>Port 5003]
        Uploader[Uploader Container<br/>Node.js 18<br/>Port 5002]
    end
    
    subgraph "External Access"
        Client[Client Browser] --> Frontend
    end
    
    subgraph "Internal Communication"
        Frontend --> |/api/*| API
        Frontend --> |/uploader/*| Uploader
    end
    
    subgraph "Persistent Storage"
        API --> DB[(SQLite Database<br/>Volume: api-data-staging)]
        API --> Logs[(Application Logs<br/>Volume: api-logs-staging)]
        Uploader --> Temp[(Temp Files<br/>Volume: uploader-temp-staging)]
    end
    
    subgraph "External Storage"
        API --> S3[S3/B2 Storage]
        Uploader --> S3
    end
```

### Container Communication
- **Frontend → API**: HTTP proxy via internal Docker network
- **Frontend → Uploader**: HTTP proxy via internal Docker network
- **Client → Frontend**: Direct HTTP/HTTPS access
- **Services → Storage**: External S3/B2 API calls

> **Detailed Architecture**: [[Docker Architecture Overview]]

---

## 🚀 Staging Deployment

### Step 1: Environment Configuration

1. **Copy Environment Template**
```bash
cp .env.staging.template .env.staging
```

2. **Configure Required Variables**
```bash
# Edit .env.staging with your values
nano .env.staging
```

**Essential Configuration**:
```bash
# Storage Configuration
AWS_S3_BUCKET_NAME=your-staging-bucket
AWS_S3_ACCESS_KEY=your-access-key
AWS_S3_SECRET_KEY=your-secret-key

# Security Configuration
YENDOR_JWT_SECRET=your-super-secure-jwt-secret-32-characters-minimum

# Environment Settings
ASPNETCORE_ENVIRONMENT=Staging
CONTAINERIZED_BUILD=true
```

> **Complete Reference**: [[Environment Variables Reference]]

### Step 2: Automated Deployment

**Quick Deployment**:
```bash
# Run automated deployment script
./deploy-staging.sh
```

The script will:
- ✅ Validate environment configuration
- ✅ Build all container images
- ✅ Start services with health checks
- ✅ Verify endpoint connectivity
- ✅ Display service status and URLs

### Step 3: Manual Deployment (Alternative)

**Build Images**:
```bash
# Build all services
docker-compose -f docker-compose.staging.yml build

# Build specific service
docker-compose -f docker-compose.staging.yml build api
```

**Start Services**:
```bash
# Start all services in background
docker-compose -f docker-compose.staging.yml up -d

# Start with logs visible
docker-compose -f docker-compose.staging.yml up
```

**Verify Deployment**:
```bash
# Check container status
docker-compose -f docker-compose.staging.yml ps

# Test health endpoints
curl http://localhost/health
curl http://localhost:5003/health
curl http://localhost:5002/health
```

---

## 🔧 Service Configuration

### Frontend Service Configuration
- **Image**: `yendorcats/frontend:staging`
- **Technology**: nginx + static files
- **Ports**: 80 (HTTP), 443 (HTTPS)
- **Features**: API proxying, CORS handling, static file serving

**Key Configuration**:
```nginx
# API proxy configuration
location /api/ {
    proxy_pass http://api:80/api/;
    # CORS and proxy headers configured
}

# Uploader proxy configuration  
location /uploader/ {
    proxy_pass http://uploader:80/;
    # File upload optimizations
}
```

> **Detailed Config**: [[Docker Service Configuration#Frontend Service]]

### API Service Configuration
- **Image**: `yendorcats/api:staging`
- **Technology**: .NET Core 8.0
- **Port**: 5003 (external), 80 (internal)
- **Features**: REST API, JWT auth, database management

**Key Features**:
- SQLite database with persistent volume
- S3/B2 storage integration
- Environment-based CORS configuration
- Health check endpoints

> **Detailed Config**: [[Docker Service Configuration#API Service]]

### Uploader Service Configuration
- **Image**: `yendorcats/uploader:staging`
- **Technology**: Node.js 18
- **Port**: 5002 (external), 80 (internal)
- **Features**: File upload handling, image processing

**Key Features**:
- Multi-part file upload support
- Image resizing and optimization
- S3/B2 storage integration
- Temporary file management

> **Detailed Config**: [[Docker Service Configuration#Uploader Service]]

---

## 🌐 Networking & Communication

### Docker Network Configuration
```yaml
networks:
  yendorcats-staging:
    driver: bridge
    name: yendorcats-staging-network
```

### Service Discovery
- Services communicate using container names as hostnames
- **Frontend** → **API**: `http://api:80`
- **Frontend** → **Uploader**: `http://uploader:80`
- **Uploader** → **API**: `http://api:80`

### Port Mapping
| Service | Internal Port | External Port | Protocol |
|---------|---------------|---------------|----------|
| Frontend | 80 | 80 | HTTP |
| Frontend | 443 | 443 | HTTPS |
| API | 80 | 5003 | HTTP |
| Uploader | 80 | 5002 | HTTP |

> **Detailed Networking**: [[Docker Networking Guide]]

---

## 💾 Data Persistence

### Volume Configuration
```yaml
volumes:
  api-data-staging:
    name: yendorcats-api-data-staging
  api-logs-staging:
    name: yendorcats-api-logs-staging
  uploader-temp-staging:
    name: yendorcats-uploader-temp-staging
```

### Data Locations
- **Database**: `/app/data/yendorcats.db` (SQLite)
- **Logs**: `/app/Logs/` (Application logs)
- **Temp Files**: `/app/uploads/` (Temporary uploads)

### Backup Procedures
```bash
# Backup database
docker cp yendorcats-api-staging:/app/data/yendorcats.db ./backup-$(date +%Y%m%d).db

# Backup logs
docker cp yendorcats-api-staging:/app/Logs/ ./logs-backup-$(date +%Y%m%d)/
```

> **Complete Storage Guide**: [[Docker Storage & Volumes]]

---

## 🏥 Health Monitoring

### Health Check Configuration
All services include built-in health checks:

```yaml
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost/health"]
  interval: 30s
  timeout: 10s
  retries: 3
  start_period: 30s
```

### Monitoring Commands
```bash
# Check all container health
docker ps --format "table {{.Names}}\t{{.Status}}"

# Detailed health information
docker inspect yendorcats-api-staging | grep -A 10 Health

# Health check logs
docker logs yendorcats-api-staging | grep health
```

> **Complete Monitoring**: [[Docker Monitoring Guide]]

---

## 🔄 Updates & Maintenance

### Rolling Updates
```bash
# Update specific service
docker-compose -f docker-compose.staging.yml build api
docker-compose -f docker-compose.staging.yml up -d api

# Update all services
docker-compose -f docker-compose.staging.yml build
docker-compose -f docker-compose.staging.yml up -d
```

### Maintenance Tasks
```bash
# View logs
docker-compose -f docker-compose.staging.yml logs -f

# Restart services
docker-compose -f docker-compose.staging.yml restart

# Clean shutdown
docker-compose -f docker-compose.staging.yml down

# Clean shutdown with volume removal
docker-compose -f docker-compose.staging.yml down -v
```

### Resource Monitoring
```bash
# Container resource usage
docker stats

# Disk usage
docker system df

# Clean up unused resources
docker system prune
```

---

## 🚨 Troubleshooting

### Common Issues

**Services Won't Start**:
```bash
# Check logs for errors
docker-compose -f docker-compose.staging.yml logs

# Check environment variables
docker exec yendorcats-api-staging env | grep -E "(AWS|JWT|CONTAINERIZED)"
```

**CORS Errors**:
- Verify `CONTAINERIZED_BUILD=true` is set
- Check nginx proxy configuration
- Verify API CORS settings

**Database Issues**:
```bash
# Check database file exists
docker exec yendorcats-api-staging ls -la /app/data/

# Access database directly
docker exec -it yendorcats-api-staging sqlite3 /app/data/yendorcats.db
```

> **Complete Troubleshooting**: [[Docker Troubleshooting]]

---

## 🎯 Next Steps

### Production Deployment
1. **ECR Setup**: [[Docker ECR Integration]]
2. **Production Config**: [[Docker Production Setup]]
3. **CI/CD Pipeline**: [[Docker CI-CD Pipeline]]

### Advanced Configuration
1. **Security Hardening**: [[Docker Security Guide]]
2. **Performance Tuning**: [[Docker Performance Tuning]]
3. **Monitoring Setup**: [[Docker Monitoring Guide]]

---

## 🏷 Related Documentation

**Setup Guides**: [[Environment Variables Reference]] | [[Docker Service Configuration]]
**Operations**: [[Docker Quick Reference]] | [[Docker Monitoring Guide]]
**Advanced**: [[Docker Production Setup]] | [[Docker CI-CD Pipeline]]

---

> **Last Updated**: 2025-07-27  
> **Status**: Staging deployment tested and verified  
> **Next**: Production ECR deployment preparation
