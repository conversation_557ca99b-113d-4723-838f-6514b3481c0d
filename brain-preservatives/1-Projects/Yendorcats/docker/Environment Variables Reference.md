---
title: "Environment Variables Reference - YendorCats Docker"
tags: [docker, environment, configuration, variables, setup, staging, production]
aliases: [env-vars, environment-config, docker-env]
created: 2025-07-27
updated: 2025-07-27
type: reference
status: complete
---

# Environment Variables Reference

> **Navigation**: [[README]] | [[Docker Deployment Guide]] | [[Docker Service Configuration]]

---

## 📋 Overview

This document provides a comprehensive reference for all environment variables used in the YendorCats Docker deployment. Variables are organized by category and include descriptions, examples, and usage notes.

> **Quick Setup**: Copy `.env.staging.template` to `.env.staging` and configure required values

---

## 🔧 Required Variables

### Core Application Settings
```bash
# Application Environment
ASPNETCORE_ENVIRONMENT=Staging
NODE_ENV=staging
CONTAINERIZED_BUILD=true

# Security Configuration
YENDOR_JWT_SECRET=your-super-secure-jwt-secret-32-characters-minimum-length
```

### Storage Configuration
```bash
# Primary S3-Compatible Storage (Required)
AWS_S3_BUCKET_NAME=your-staging-bucket-name
AWS_S3_ACCESS_KEY=your-s3-access-key
AWS_S3_SECRET_KEY=your-s3-secret-key
AWS_REGION=us-west-004
```

---

## 🌐 Environment-Specific Settings

### Staging Environment
```bash
# Environment Identification
ASPNETCORE_ENVIRONMENT=Staging
NODE_ENV=staging

# Logging Levels (More verbose for debugging)
SERILOG_MINIMUM_LEVEL=Information
SERILOG_OVERRIDE_MICROSOFT=Warning
SERILOG_OVERRIDE_SYSTEM=Warning

# CORS Policy (Permissive for testing)
CORS_POLICY=AllowAll

# Sample Data (Disabled in staging)
SEEDING_ENABLE_SAMPLE_DATA=false
SEEDING_FORCE_RESEED=false
```

### Production Environment
```bash
# Environment Identification
ASPNETCORE_ENVIRONMENT=Production
NODE_ENV=production

# Logging Levels (Less verbose for performance)
SERILOG_MINIMUM_LEVEL=Warning
SERILOG_OVERRIDE_MICROSOFT=Error
SERILOG_OVERRIDE_SYSTEM=Error

# CORS Policy (Restrictive for security)
CORS_POLICY=AllowSpecificOrigins

# Sample Data (Always disabled)
SEEDING_ENABLE_SAMPLE_DATA=false
SEEDING_FORCE_RESEED=false
```

---

## 🗄 Storage Configuration

### AWS S3 / Backblaze B2 Settings
```bash
# Primary Storage (S3-Compatible API)
AWS_S3_BUCKET_NAME=yendorcats-staging
AWS_S3_ACCESS_KEY=004abc123def456ghi789jkl
AWS_S3_SECRET_KEY=K004abcdefghijklmnopqrstuvwxyz1234567890AB
AWS_REGION=us-west-004
AWS_S3_ENDPOINT=https://s3.us-west-004.backblazeb2.com
AWS_S3_PUBLIC_URL=https://f004.backblazeb2.com/file/yendorcats-staging/{key}

# Backblaze B2 Native API (Optional)
B2_BUCKET_NAME=yendorcats-staging
B2_APPLICATION_KEY_ID=004abc123def456ghi789jkl
B2_APPLICATION_KEY=K004abcdefghijklmnopqrstuvwxyz1234567890AB
B2_BUCKET_ID=abc123def456ghi789jkl012
```

### Hybrid Storage Configuration
```bash
# Storage Provider Selection
HYBRID_STORAGE_DEFAULT_PROVIDER=S3
HYBRID_STORAGE_ENABLE_DUAL_STORAGE=true

# S3 Configuration
HYBRID_STORAGE_S3_BUCKET_NAME=${AWS_S3_BUCKET_NAME}
HYBRID_STORAGE_S3_REGION=${AWS_REGION}
HYBRID_STORAGE_S3_SERVICE_URL=${AWS_S3_ENDPOINT}
HYBRID_STORAGE_S3_PUBLIC_URL=${AWS_S3_PUBLIC_URL}
HYBRID_STORAGE_S3_USE_DIRECT_URLS=true

# B2 Configuration
HYBRID_STORAGE_B2_BUCKET_NAME=${B2_BUCKET_NAME}
HYBRID_STORAGE_B2_APPLICATION_KEY_ID=${B2_APPLICATION_KEY_ID}
HYBRID_STORAGE_B2_APPLICATION_KEY=${B2_APPLICATION_KEY}
HYBRID_STORAGE_B2_BUCKET_ID=${B2_BUCKET_ID}
```

---

## 🔐 Security Configuration

### JWT Authentication
```bash
# JWT Settings (Required)
YENDOR_JWT_SECRET=staging-super-secure-jwt-secret-key-32-characters-minimum-length
JWT_ISSUER=YendorCatsApi
JWT_AUDIENCE=YendorCatsClients
JWT_EXPIRY_MINUTES=60
JWT_REFRESH_EXPIRY_DAYS=7
```

### Admin User Configuration
```bash
# Default Admin User (Initial Setup)
YENDOR_DEFAULT_ADMIN_USERNAME=admin
YENDOR_DEFAULT_ADMIN_EMAIL=<EMAIL>
YENDOR_DEFAULT_ADMIN_PASSWORD=your-secure-admin-password
```

### CORS Configuration
```bash
# Additional CORS Origins (Optional)
ADDITIONAL_CORS_ORIGINS=https://your-staging-domain.com,https://your-cdn-domain.com
```

---

## 🗃 Database Configuration

### SQLite Settings
```bash
# Database Connection (Automatic in containers)
ConnectionStrings__SqliteConnection=Data Source=/app/data/yendorcats.db

# Database Initialization
DATABASE_AUTO_MIGRATE=true
DATABASE_SEED_ON_STARTUP=false
```

### Migration Settings
```bash
# Migration Configuration
MIGRATION_BATCH_SIZE=100
MIGRATION_MAX_RETRIES=3
MIGRATION_RETRY_DELAY_SECONDS=30
```

---

## 📊 Performance & Monitoring

### Application Performance
```bash
# Cache Configuration
CACHE_WARMUP_ON_STARTUP=true
PRELOAD_POPULAR_IMAGES=true
MAX_CONCURRENT_OPERATIONS=10

# Request Limits
MAX_REQUEST_SIZE_MB=50
REQUEST_TIMEOUT_SECONDS=30
```

### Health Check Configuration
```bash
# Health Check Settings
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10
HEALTH_CHECK_RETRIES=3
HEALTH_CHECK_START_PERIOD=30
```

### Resource Limits
```bash
# Memory Limits (for container orchestration)
MEMORY_LIMIT_FRONTEND=256M
MEMORY_LIMIT_API=1G
MEMORY_LIMIT_UPLOADER=512M

# CPU Limits
CPU_LIMIT_FRONTEND=0.5
CPU_LIMIT_API=1.0
CPU_LIMIT_UPLOADER=0.5
```

---

## 🔧 Service-Specific Variables

### Frontend Service (nginx)
```bash
# Nginx Configuration
NGINX_WORKER_PROCESSES=auto
NGINX_WORKER_CONNECTIONS=1024
NGINX_CLIENT_MAX_BODY_SIZE=50M

# Proxy Timeouts
NGINX_PROXY_CONNECT_TIMEOUT=30s
NGINX_PROXY_SEND_TIMEOUT=30s
NGINX_PROXY_READ_TIMEOUT=30s
```

### API Service (.NET Core)
```bash
# ASP.NET Core Configuration
ASPNETCORE_URLS=http://+:80
ASPNETCORE_FORWARDEDHEADERS_ENABLED=true

# Container-Specific Settings
CONTAINERIZED_BUILD=true
DISABLE_STATIC_FILES=true
DISABLE_SPA_FALLBACK=true
```

### Uploader Service (Node.js)
```bash
# Node.js Configuration
PORT=80
NODE_OPTIONS=--max-old-space-size=512

# Upload Configuration
UPLOAD_MAX_FILE_SIZE=50MB
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,video/mp4
UPLOAD_TEMP_DIR=/app/uploads
```

---

## 📝 Variable Validation

### Required Variable Checklist
- [ ] `AWS_S3_BUCKET_NAME` - Storage bucket name
- [ ] `AWS_S3_ACCESS_KEY` - Storage access key
- [ ] `AWS_S3_SECRET_KEY` - Storage secret key
- [ ] `YENDOR_JWT_SECRET` - JWT signing secret (32+ chars)
- [ ] `ASPNETCORE_ENVIRONMENT` - Application environment
- [ ] `CONTAINERIZED_BUILD` - Container mode flag

### Validation Commands
```bash
# Check required variables are set
docker exec yendorcats-api-staging env | grep -E "(AWS_S3_BUCKET_NAME|JWT_SECRET|CONTAINERIZED_BUILD)"

# Validate JWT secret length
docker exec yendorcats-api-staging bash -c 'echo ${YENDOR_JWT_SECRET} | wc -c'

# Test storage connectivity
docker exec yendorcats-api-staging curl -I ${AWS_S3_ENDPOINT}
```

---

## 🔄 Environment File Templates

### Staging Template (`.env.staging`)
```bash
# Core Settings
ASPNETCORE_ENVIRONMENT=Staging
NODE_ENV=staging
CONTAINERIZED_BUILD=true

# Storage (Replace with your values)
AWS_S3_BUCKET_NAME=your-staging-bucket
AWS_S3_ACCESS_KEY=your-access-key
AWS_S3_SECRET_KEY=your-secret-key
AWS_REGION=us-west-004

# Security (Generate secure values)
YENDOR_JWT_SECRET=your-super-secure-jwt-secret-32-characters-minimum

# Optional Settings
SEEDING_ENABLE_SAMPLE_DATA=false
HYBRID_STORAGE_DEFAULT_PROVIDER=S3
```

### Production Template (`.env.production`)
```bash
# Core Settings
ASPNETCORE_ENVIRONMENT=Production
NODE_ENV=production
CONTAINERIZED_BUILD=true

# Storage (Production values)
AWS_S3_BUCKET_NAME=your-production-bucket
AWS_S3_ACCESS_KEY=your-production-access-key
AWS_S3_SECRET_KEY=your-production-secret-key
AWS_REGION=us-west-004

# Security (Strong production values)
YENDOR_JWT_SECRET=production-super-secure-jwt-secret-64-characters-minimum-length

# Production Optimizations
SERILOG_MINIMUM_LEVEL=Warning
CACHE_WARMUP_ON_STARTUP=true
HYBRID_STORAGE_ENABLE_DUAL_STORAGE=true
```

---

## 🚨 Security Best Practices

### Secret Management
- **Never commit** `.env` files with real values to version control
- Use **strong, unique secrets** for each environment
- **Rotate secrets** regularly in production
- Consider using **external secret management** (AWS Secrets Manager, etc.)

### Environment Separation
- Use **different storage buckets** for staging/production
- Use **different JWT secrets** for each environment
- **Isolate database instances** between environments
- **Separate access keys** for different environments

### Validation
```bash
# Check for weak JWT secrets
if [ ${#YENDOR_JWT_SECRET} -lt 32 ]; then
    echo "WARNING: JWT secret is too short (minimum 32 characters)"
fi

# Verify environment isolation
if [ "$ASPNETCORE_ENVIRONMENT" = "Production" ] && [[ "$AWS_S3_BUCKET_NAME" == *"staging"* ]]; then
    echo "ERROR: Production environment using staging bucket"
fi
```

---

## 🔗 Related Configuration

### Docker Compose Integration
Variables are automatically loaded from `.env.staging` when using:
```bash
docker-compose -f docker-compose.staging.yml up -d
```

### Service Configuration Files
- **Frontend**: [[Docker Service Configuration#Frontend Service]]
- **API**: [[Docker Service Configuration#API Service]]
- **Uploader**: [[Docker Service Configuration#Uploader Service]]

### Network Configuration
- **Networking**: [[Docker Networking Guide]]
- **Storage**: [[Docker Storage & Volumes]]

---

## 🏷 Variable Categories

**Core**: #environment #containerization #application-settings
**Storage**: #s3 #b2 #storage #buckets #hybrid-storage
**Security**: #jwt #authentication #cors #secrets
**Performance**: #caching #limits #optimization #monitoring
**Deployment**: #staging #production #cicd #automation

---

> **Related**: [[Docker Deployment Guide]] | [[Docker Service Configuration]] | [[Docker Security Guide]]
