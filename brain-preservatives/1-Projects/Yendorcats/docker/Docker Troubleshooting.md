---
title: "Docker Troubleshooting Guide - YendorCats"
tags: [docker, troubleshooting, debugging, issues, solutions, errors]
aliases: [docker-debug, docker-issues, container-troubleshooting]
created: 2025-07-27
updated: 2025-07-27
type: troubleshooting
status: complete
---

# Docker Troubleshooting Guide

> **Navigation**: [[README]] | [[Docker Quick Reference]] | [[Docker Commands Cheatsheet]]

---

## 🚨 Quick Diagnostic Commands

### Immediate Health Check
```bash
# Check all container status
docker-compose -f docker-compose.staging.yml ps

# Quick health verification
curl -f http://localhost/health && echo "Frontend: OK"
curl -f http://localhost:5003/health && echo "API: OK"  
curl -f http://localhost:5002/health && echo "Uploader: OK"

# View recent logs
docker-compose -f docker-compose.staging.yml logs --tail=50
```

### System Resource Check
```bash
# Container resource usage
docker stats --no-stream

# Disk usage
docker system df

# Available system resources
free -h && df -h
```

---

## 🐳 Container Issues

### Containers Won't Start

**Symptoms**: Services fail to start or immediately exit

**Diagnostic Steps**:
```bash
# Check container status
docker-compose -f docker-compose.staging.yml ps

# View startup logs
docker-compose -f docker-compose.staging.yml logs service-name

# Check for port conflicts
netstat -tulpn | grep -E "(80|443|5002|5003)"

# Verify image exists
docker images | grep yendorcats
```

**Common Causes & Solutions**:

1. **Port Already in Use**
   ```bash
   # Find process using port
   lsof -i :80
   
   # Kill process or change port mapping
   docker-compose -f docker-compose.staging.yml down
   # Edit port mapping in docker-compose.staging.yml
   ```

2. **Missing Environment Variables**
   ```bash
   # Check environment file exists
   ls -la .env.staging
   
   # Validate required variables
   grep -E "(AWS_S3_BUCKET_NAME|JWT_SECRET)" .env.staging
   ```

3. **Image Build Failures**
   ```bash
   # Rebuild with verbose output
   docker-compose -f docker-compose.staging.yml build --no-cache service-name
   
   # Check Dockerfile syntax
   docker build -t test-build backend/YendorCats.API/
   ```

### Containers Keep Restarting

**Symptoms**: Containers start but continuously restart

**Diagnostic Steps**:
```bash
# Check restart count
docker ps -a --format "table {{.Names}}\t{{.Status}}"

# View exit codes
docker-compose -f docker-compose.staging.yml logs service-name | grep -i exit

# Check health check failures
docker inspect yendorcats-api-staging | grep -A 10 Health
```

**Solutions**:

1. **Health Check Failures**
   ```bash
   # Test health check manually
   docker exec yendorcats-api-staging curl -f http://localhost/health
   
   # Disable health check temporarily
   # Comment out healthcheck section in docker-compose.yml
   ```

2. **Application Crashes**
   ```bash
   # Check application logs
   docker logs yendorcats-api-staging | grep -E "(ERROR|FATAL|Exception)"
   
   # Increase memory limits
   # Edit docker-compose.yml memory limits
   ```

---

## 🌐 Network & Connectivity Issues

### CORS Errors

**Symptoms**: Browser console shows CORS errors, API calls fail

**Diagnostic Steps**:
```bash
# Test CORS headers
curl -I http://localhost/api/cats

# Check environment variable
docker exec yendorcats-api-staging env | grep CONTAINERIZED_BUILD

# Test direct API access
curl -I http://localhost:5003/api/cats
```

**Solutions**:

1. **Missing CONTAINERIZED_BUILD Variable**
   ```bash
   # Add to .env.staging
   echo "CONTAINERIZED_BUILD=true" >> .env.staging
   
   # Restart API service
   docker-compose -f docker-compose.staging.yml restart api
   ```

2. **Nginx Proxy Configuration**
   ```bash
   # Check nginx configuration
   docker exec yendorcats-frontend-staging cat /etc/nginx/nginx.conf | grep -A 10 "location /api"
   
   # Test proxy connectivity
   docker exec yendorcats-frontend-staging curl -I http://api:80/health
   ```

### Service Communication Failures

**Symptoms**: Services can't communicate with each other

**Diagnostic Steps**:
```bash
# Check network configuration
docker network ls | grep yendorcats

# Test inter-service connectivity
docker exec yendorcats-frontend-staging ping api
docker exec yendorcats-frontend-staging nc -zv api 80

# Check DNS resolution
docker exec yendorcats-frontend-staging nslookup api
```

**Solutions**:

1. **Network Issues**
   ```bash
   # Recreate network
   docker-compose -f docker-compose.staging.yml down
   docker network rm yendorcats-staging-network
   docker-compose -f docker-compose.staging.yml up -d
   ```

2. **Service Discovery Problems**
   ```bash
   # Check service names in docker-compose.yml
   grep -A 5 "services:" docker-compose.staging.yml
   
   # Verify container names
   docker ps --format "table {{.Names}}\t{{.Networks}}"
   ```

---

## 🗄 Database Issues

### Database Connection Failures

**Symptoms**: API can't connect to database, migration errors

**Diagnostic Steps**:
```bash
# Check database file exists
docker exec yendorcats-api-staging ls -la /app/data/

# Test database access
docker exec yendorcats-api-staging sqlite3 /app/data/yendorcats.db ".tables"

# Check database permissions
docker exec yendorcats-api-staging ls -la /app/data/yendorcats.db
```

**Solutions**:

1. **Missing Database File**
   ```bash
   # Check volume mounting
   docker volume inspect yendorcats-api-data-staging
   
   # Recreate database
   docker-compose -f docker-compose.staging.yml restart api
   ```

2. **Permission Issues**
   ```bash
   # Fix permissions
   docker exec -u root yendorcats-api-staging chown -R app:app /app/data
   docker exec -u root yendorcats-api-staging chmod 644 /app/data/yendorcats.db
   ```

### Database Corruption

**Symptoms**: SQLite errors, data inconsistencies

**Diagnostic Steps**:
```bash
# Check database integrity
docker exec yendorcats-api-staging sqlite3 /app/data/yendorcats.db "PRAGMA integrity_check;"

# Check database size
docker exec yendorcats-api-staging ls -lh /app/data/yendorcats.db
```

**Solutions**:

1. **Backup and Restore**
   ```bash
   # Backup current database
   docker cp yendorcats-api-staging:/app/data/yendorcats.db ./backup-$(date +%Y%m%d).db
   
   # Reset database (will recreate)
   docker-compose -f docker-compose.staging.yml down
   docker volume rm yendorcats-api-data-staging
   docker-compose -f docker-compose.staging.yml up -d
   ```

---

## 🗂 Storage Issues

### S3/B2 Connection Problems

**Symptoms**: File uploads fail, storage errors in logs

**Diagnostic Steps**:
```bash
# Check storage configuration
docker exec yendorcats-api-staging env | grep -E "(AWS_S3|B2_)"

# Test storage connectivity
docker exec yendorcats-api-staging curl -I ${AWS_S3_ENDPOINT}

# Check storage logs
docker logs yendorcats-uploader-staging | grep -i storage
```

**Solutions**:

1. **Invalid Credentials**
   ```bash
   # Verify credentials in .env.staging
   grep -E "(ACCESS_KEY|SECRET_KEY)" .env.staging
   
   # Test credentials with AWS CLI
   aws s3 ls s3://your-bucket-name --endpoint-url=${AWS_S3_ENDPOINT}
   ```

2. **Network Connectivity**
   ```bash
   # Test external connectivity
   docker exec yendorcats-api-staging ping s3.us-west-004.backblazeb2.com
   
   # Check firewall/proxy settings
   docker exec yendorcats-api-staging curl -v ${AWS_S3_ENDPOINT}
   ```

---

## 🔧 Build Issues

### Image Build Failures

**Symptoms**: Docker build commands fail

**Diagnostic Steps**:
```bash
# Build with verbose output
docker build --no-cache -t test-build backend/YendorCats.API/ 2>&1 | tee build.log

# Check Dockerfile syntax
docker run --rm -i hadolint/hadolint < backend/YendorCats.API/Dockerfile
```

**Common Issues**:

1. **Frontend Build Targets**
   ```bash
   # Check if CONTAINERIZED_BUILD is set during build
   grep -n "CONTAINERIZED_BUILD" backend/YendorCats.API/Dockerfile
   
   # Verify frontend files are excluded
   docker build --no-cache backend/YendorCats.API/ | grep -i frontend
   ```

2. **Dependency Issues**
   ```bash
   # Check package restore
   docker run --rm -v $(pwd)/backend:/src mcr.microsoft.com/dotnet/sdk:8.0 \
     bash -c "cd /src/YendorCats.API && dotnet restore"
   ```

### Docker Compose Issues

**Symptoms**: docker-compose commands fail

**Diagnostic Steps**:
```bash
# Validate compose file
docker-compose -f docker-compose.staging.yml config

# Check for syntax errors
yamllint docker-compose.staging.yml

# Verify environment file
docker-compose -f docker-compose.staging.yml config --resolve-env-vars
```

---

## 📊 Performance Issues

### High Resource Usage

**Symptoms**: Slow response times, high CPU/memory usage

**Diagnostic Steps**:
```bash
# Monitor resource usage
docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"

# Check container limits
docker inspect yendorcats-api-staging | grep -A 10 Resources

# Monitor system resources
top -p $(docker inspect -f '{{.State.Pid}}' yendorcats-api-staging)
```

**Solutions**:

1. **Increase Resource Limits**
   ```yaml
   # In docker-compose.staging.yml
   deploy:
     resources:
       limits:
         memory: 2G
         cpus: '2.0'
   ```

2. **Optimize Application**
   ```bash
   # Check for memory leaks in logs
   docker logs yendorcats-api-staging | grep -i "memory\|gc\|heap"
   
   # Profile application performance
   docker exec yendorcats-api-staging dotnet-counters monitor --process-id 1
   ```

---

## 🔍 Log Analysis

### Finding Relevant Logs

**Application Logs**:
```bash
# API logs
docker logs yendorcats-api-staging | grep -E "(ERROR|WARN|FATAL)"

# Uploader logs
docker logs yendorcats-uploader-staging | grep -i error

# Frontend/Nginx logs
docker logs yendorcats-frontend-staging | grep -E "(error|404|500)"
```

**System Logs**:
```bash
# Docker daemon logs
journalctl -u docker.service --since "1 hour ago"

# Container events
docker events --since "1h" --filter container=yendorcats-api-staging
```

### Log Patterns to Watch

**Critical Errors**:
```bash
# Database errors
grep -i "database\|sqlite\|connection" logs.txt

# Authentication errors  
grep -i "jwt\|auth\|unauthorized" logs.txt

# Storage errors
grep -i "s3\|storage\|upload" logs.txt

# Memory issues
grep -i "memory\|oom\|heap" logs.txt
```

---

## 🛠 Recovery Procedures

### Complete Environment Reset

**When to Use**: Multiple services failing, unknown state

**Procedure**:
```bash
# 1. Stop all services
docker-compose -f docker-compose.staging.yml down

# 2. Remove containers and networks
docker-compose -f docker-compose.staging.yml down --remove-orphans

# 3. Remove images (optional)
docker rmi $(docker images yendorcats/* -q)

# 4. Clean up volumes (WARNING: Data loss)
docker volume rm yendorcats-api-data-staging yendorcats-api-logs-staging yendorcats-uploader-temp-staging

# 5. Rebuild and restart
./deploy-staging.sh
```

### Partial Service Recovery

**When to Use**: Single service issues

**Procedure**:
```bash
# 1. Stop specific service
docker-compose -f docker-compose.staging.yml stop api

# 2. Rebuild service
docker-compose -f docker-compose.staging.yml build --no-cache api

# 3. Start service
docker-compose -f docker-compose.staging.yml up -d api

# 4. Verify health
curl http://localhost:5003/health
```

---

## 📞 Getting Help

### Information to Collect

Before seeking help, collect:

1. **System Information**:
   ```bash
   docker --version
   docker-compose --version
   uname -a
   ```

2. **Container Status**:
   ```bash
   docker-compose -f docker-compose.staging.yml ps
   docker stats --no-stream
   ```

3. **Logs**:
   ```bash
   docker-compose -f docker-compose.staging.yml logs > debug-logs.txt
   ```

4. **Configuration**:
   ```bash
   # Sanitized environment (remove secrets)
   cat .env.staging | sed 's/=.*/=***REDACTED***/'
   ```

### Support Channels

- **Documentation**: [[README]] | [[Docker Deployment Guide]]
- **Quick Reference**: [[Docker Quick Reference]] | [[Docker Commands Cheatsheet]]
- **Scripts**: [[Docker Scripts Reference]]

---

## 🏷 Troubleshooting Categories

**Connectivity**: #cors #networking #proxy #dns #firewall
**Performance**: #memory #cpu #disk #optimization #monitoring  
**Data**: #database #storage #backup #corruption #permissions
**Deployment**: #build #startup #configuration #environment

---

> **Related**: [[Docker Quick Reference]] | [[Docker Commands Cheatsheet]] | [[Docker Monitoring Guide]]
