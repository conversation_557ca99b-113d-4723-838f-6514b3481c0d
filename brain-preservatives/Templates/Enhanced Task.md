---
# CORE METADATA
creation_date: <% tp.date.now("YYYY-MM-DD") %>
modification_date: <% tp.date.now("YYYY-MM-DD") %>
type: task
status: not-started
priority: <% await tp.system.suggester(["critical", "high", "medium", "low"], ["critical", "high", "medium", "low"], false, "Task Priority") %>
area_category: <% await tp.system.suggester(["Software-Development", "Administration", "Personal", "Church", "Education", "Finance"], ["Software-Development", "Administration", "Personal", "Church", "Education", "Finance"], false, "Area Category") %>
owner: Jordan
tags: [task, <% await tp.system.suggester(["urgent", "project", "admin", "maintenance", "creative"], ["urgent", "project", "admin", "maintenance", "creative"], false, "Primary Tag") %>]

# TASK MANAGEMENT
task_priority: <% await tp.system.suggester(["urgent", "high", "medium", "routine", "someday"], ["urgent", "high", "medium", "routine", "someday"], false, "Task Priority") %>
task_context: <% await tp.system.suggester(["deep-work", "admin", "communication", "maintenance", "creative", "research"], ["deep-work", "admin", "communication", "maintenance", "creative", "research"], false, "Task Context") %>
estimated_time: <% await tp.system.suggester(["15min", "30min", "1hr", "2hr", "4hr", "8hr", "1day"], ["15min", "30min", "1hr", "2hr", "4hr", "8hr", "1day"], false, "Estimated Time") %>
energy_required: <% await tp.system.suggester(["high", "medium", "low"], ["high", "medium", "low"], false, "Energy Required") %>
task_status: not-started
due: <% tp.date.now("YYYY-MM-DD", 7) %>

# RELATIONSHIPS (Enhanced System)
related_projects: ["[[<% await tp.system.suggester(["Project 1", "Project 2"], ["Project 1", "Project 2"], false, "Related Project") %>]]"]
related_areas: ["[[<% await tp.system.suggester(["Software-Development", "Administration", "Personal", "Church"], ["Software-Development", "Administration", "Personal", "Church"], false, "Related Area") %>]]"]
related_resources: []
related_people: ["[[Jordan]]"]
blocked_by: []
waiting_for: []

# TASK LIFECYCLE
created_date: <% tp.date.now("YYYY-MM-DD") %>
started_date: ""
completed_date: ""
actual_time: ""
task_outcome: ""
lessons_learned: ""
---

# <% tp.file.title %>

## 📋 Task Description
<!-- What needs to be done? Be specific and actionable -->

## ✅ Success Criteria
<!-- How will you know it's complete? -->
- [ ] 
- [ ] 
- [ ] 

## 🎯 Context & Motivation
<!-- Why is this important? What's the bigger picture? -->

## 📚 Resources Needed
<!-- What do you need to complete this? -->
- 
- 
- 

## 🚧 Potential Obstacles
<!-- What might block or delay this task? -->
- 
- 

## 📝 Notes & Considerations
<!-- Additional context, constraints, or considerations -->

## 🔄 Subtasks
- [ ] 
- [ ] 
- [ ] 
- [ ] 

## 🔗 Related Tasks
<%*
const relatedTaskQuery = `
TABLE WITHOUT ID
  file.link as "Related Task",
  task_priority as "Priority",
  due as "Due Date"
FROM ""
WHERE type = "task" 
  AND !completed
  AND (contains(related_projects, "${tp.file.title}") 
   OR contains(related_areas, "${tp.file.title}")
   OR file.name != "${tp.file.title}")
SORT task_priority ASC, due ASC
LIMIT 5
`;
tR += "```dataview\n" + relatedTaskQuery + "\n```\n\n";
%>

## 📊 Task Analytics
- **Created**: <% tp.date.now("YYYY-MM-DD") %>
- **Estimated Time**: <% await tp.system.suggester(["15min", "30min", "1hr", "2hr", "4hr"], ["15min", "30min", "1hr", "2hr", "4hr"], false, "Estimated Time") %>
- **Energy Level**: <% await tp.system.suggester(["high", "medium", "low"], ["high", "medium", "low"], false, "Energy Required") %>
- **Context**: <% await tp.system.suggester(["deep-work", "admin", "communication"], ["deep-work", "admin", "communication"], false, "Task Context") %>

## Quick Links
- [[<% tp.file.title %> Project|Related Project]]
- [[<% tp.file.title %> Resource|Create Resource]]
- [[Tasks|All Tasks]]
- [[Home]]
