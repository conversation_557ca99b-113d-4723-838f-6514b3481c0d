---
# CORE METADATA
creation_date: <% tp.date.now("YYYY-MM-DD") %>
modification_date: <% tp.date.now("YYYY-MM-DD") %>
type: project
status: <% await tp.system.suggester(["active", "on-hold", "completed", "cancelled"], ["active", "on-hold", "completed", "cancelled"], false, "Project Status") %>
priority: <% await tp.system.suggester(["critical", "high", "medium", "low"], ["critical", "high", "medium", "low"], false, "Project Priority") %>
area_category: <% await tp.system.suggester(["Software-Development", "Administration", "Personal", "Church", "Education", "Finance"], ["Software-Development", "Administration", "Personal", "Church", "Education", "Finance"], false, "Area Category") %>
owner: Jordan
tags: [para/projects, <% await tp.system.suggester(["software-dev", "church", "personal", "university", "admin", "finance"], ["software-dev", "church", "personal", "university", "admin", "finance"], false, "Primary Tag") %>]

# PROJECT MANAGEMENT
project_client: <% await tp.system.suggester(["Personal", "Church", "University", "Client"], ["Personal", "Church", "University", await tp.system.prompt("Client Name")], false, "Project Client") %>
completion_percentage: 0
estimated_hours: <% await tp.system.suggester(["10", "20", "40", "80", "120"], ["10", "20", "40", "80", await tp.system.prompt("Estimated Hours")], false, "Estimated Hours") %>
actual_hours: 0
deadline: <% tp.date.now("YYYY-MM-DD", 30) %>
start_date: <% tp.date.now("YYYY-MM-DD") %>

# RELATIONSHIPS (Enhanced System)
related_areas: ["[[<% await tp.system.suggester(["Software-Development", "Administration", "Personal", "Church"], ["Software-Development", "Administration", "Personal", "Church"], false, "Primary Area") %>]]"]
related_resources: []
related_people: ["[[Jordan]]"]
stakeholders: []
dependencies: []
deliverables: []

# TASK MANAGEMENT
task_priority: <% await tp.system.suggester(["urgent", "high", "medium", "routine"], ["urgent", "high", "medium", "routine"], false, "Default Task Priority") %>
task_context: <% await tp.system.suggester(["deep-work", "admin", "communication", "creative"], ["deep-work", "admin", "communication", "creative"], false, "Primary Task Context") %>
---

# <% tp.file.title %>

## Overview
<!-- Brief description of the project -->

## Objectives
<!-- What are you trying to achieve? -->
-

## Success Criteria
<!-- How will you know when the project is successful? -->
-

## 🎯 Priority Tasks
### 🔥 Critical & Urgent
- [ ]

### ⚡ High Priority
- [ ]

### 📋 Medium Priority
- [ ]

## 🚀 Project Tasks by Priority
<%*
const taskQuery = `
TABLE WITHOUT ID
  "🔥" as "",
  file.link as "URGENT TASKS",
  choice(estimated_time, estimated_time, "?") as "Time",
  choice(deadline, deadline, "ASAP") as "Due"
FROM this.file.folder
WHERE !completed
  AND task_priority = "urgent"
SORT file.path ASC
LIMIT 5
`;
tR += "```dataview\n" + taskQuery + "\n```\n\n";

const highTaskQuery = `
TABLE WITHOUT ID
  "⚡" as "",
  file.link as "HIGH PRIORITY",
  choice(estimated_time, estimated_time, "?") as "Time",
  choice(deadline, deadline, "Soon") as "Due"
FROM this.file.folder
WHERE !completed
  AND task_priority = "high"
SORT due ASC, file.path ASC
LIMIT 8
`;
tR += "```dataview\n" + highTaskQuery + "\n```\n\n";
%>

## Timeline
- **Start Date**: <% tp.date.now("YYYY-MM-DD") %>
- **Deadline**: <% tp.date.now("YYYY-MM-DD", 30) %>
- **Milestones**:
  - [ ] Initial Planning - <% tp.date.now("YYYY-MM-DD", 7) %>
  - [ ] Development - <% tp.date.now("YYYY-MM-DD", 14) %>
  - [ ] Testing - <% tp.date.now("YYYY-MM-DD", 21) %>
  - [ ] Completion - <% tp.date.now("YYYY-MM-DD", 30) %>

## Resources
<!-- Links to relevant resources -->
-

## 📋 Related Areas
<%*
const areaQuery = `
TABLE WITHOUT ID
  file.link as "📋 Area",
  responsibility_level as "Priority",
  next_review_date as "Next Review"
FROM "2-Areas"
WHERE contains(related_projects, "${tp.file.title}")
  AND status = "active"
SORT choice(responsibility_level = "critical", 1, choice(responsibility_level = "high", 2, 3)) ASC
LIMIT 3
`;
tR += "```dataview\n" + areaQuery + "\n```\n\n";
%>

## 📚 Essential Resources
<%*
const resourceQuery = `
TABLE WITHOUT ID
  file.link as "📚 Resource",
  choice(difficulty = "beginner", "🟢 Easy", choice(difficulty = "intermediate", "🟡 Medium", "🔴 Advanced")) as "Level",
  source as "Source"
FROM "3-Resources"
WHERE (contains(related_projects, "${tp.file.title}")
   OR contains(tags, "${await tp.system.suggester(["software-dev", "church", "personal", "admin"], ["software-dev", "church", "personal", "admin"], false, "Filter Tag")}")
   OR area_category = "${await tp.system.suggester(["Software-Development", "Administration", "Personal", "Church"], ["Software-Development", "Administration", "Personal", "Church"], false, "Filter Area")}")
  AND file.name != "${tp.file.title}"
SORT choice(contains(related_projects, "${tp.file.title}"), 1, 2) ASC, difficulty ASC
LIMIT 4
`;
tR += "```dataview\n" + resourceQuery + "\n```\n\n";
%>

## 🔗 Project Dependencies
<%*
const dependencyQuery = `
TABLE WITHOUT ID
  file.link as "🔗 Depends On",
  type as "Type",
  choice(status = "completed", "✅", choice(status = "active", "🔄", "⏸️")) as "Status"
FROM ""
WHERE contains(this.dependencies, file.name)
SORT choice(status = "completed", 3, choice(status = "active", 1, 2)) ASC
`;
tR += "```dataview\n" + dependencyQuery + "\n```\n\n";
%>

## 📝 Recent Project Activity
<%*
const activityQuery = `
TABLE WITHOUT ID
  file.link as "📝 Recent Activity",
  type as "Type",
  file.mtime as "Modified"
FROM ""
WHERE (contains(related_projects, "${tp.file.title}")
   OR contains(related_areas, "${tp.file.title}")
   OR contains(related_resources, "${tp.file.title}"))
  AND file.mtime >= date(today) - dur(7 days)
SORT file.mtime DESC
LIMIT 5
`;
tR += "```dataview\n" + activityQuery + "\n```\n\n";
%>

## 📅 Meeting Notes
<%*
const meetingQuery = `
TABLE WITHOUT ID
  file.link as "📅 Meeting",
  date as "Date",
  participants as "Participants"
FROM #meeting
WHERE contains(related_projects, "${tp.file.title}") OR contains(file.name, "${tp.file.title}")
SORT date DESC
LIMIT 5
`;
tR += "```dataview\n" + meetingQuery + "\n```\n\n";
%>

## Progress Updates
<!-- Regular updates on project progress -->
### <% tp.date.now("YYYY-MM-DD") %> - Initial Setup
- Project created
- Initial planning started

## Quick Links
- [[<% tp.file.title %> Meeting|New Meeting]]
- [[<% tp.file.title %> Resource|New Resource]]
- [[1-Projects|All Projects]]
