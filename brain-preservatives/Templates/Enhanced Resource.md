---
# CORE METADATA
creation_date: <% tp.date.now("YYYY-MM-DD") %>
modification_date: <% tp.date.now("YYYY-MM-DD") %>
type: resource
status: <% await tp.system.suggester(["active", "archived", "outdated"], ["active", "archived", "outdated"], false, "Resource Status") %>
priority: <% await tp.system.suggester(["critical", "high", "medium", "low"], ["critical", "high", "medium", "low"], false, "Resource Priority") %>
area_category: <% await tp.system.suggester(["Software-Development", "Administration", "Personal", "Church", "Education", "Finance"], ["Software-Development", "Administration", "Personal", "Church", "Education", "Finance"], false, "Area Category") %>
owner: Jordan
tags: [para/resources, <% await tp.system.suggester(["guide", "reference", "tool", "tutorial", "cheatsheet"], ["guide", "reference", "tool", "tutorial", "cheatsheet"], false, "Resource Type") %>]

# RESOURCE CLASSIFICATION
source: <% await tp.system.suggester(["Personal research", "Website", "Book", "Course", "Documentation", "Tool"], ["Personal research", "Website", "Book", "Course", "Documentation", "Tool"], false, "Source") %>
difficulty: <% await tp.system.suggester(["beginner", "intermediate", "advanced"], ["beginner", "intermediate", "advanced"], false, "Difficulty") %>
resource_type: <% await tp.system.suggester(["guide", "reference", "tool", "tutorial", "cheatsheet"], ["guide", "reference", "tool", "tutorial", "cheatsheet"], false, "Resource Type") %>
url: <% await tp.system.prompt("URL (if applicable)", "") %>
last_verified: <% tp.date.now("YYYY-MM-DD") %>

# RELATIONSHIPS (Enhanced System)
related_projects: []
related_areas: ["[[<% await tp.system.suggester(["Software-Development", "Administration", "Personal", "Church"], ["Software-Development", "Administration", "Personal", "Church"], false, "Primary Area") %>]]"]
related_people: ["[[Jordan]]"]
references: []
inspired_by: []
---

# <% tp.file.title %>

## Overview
<!-- Brief description of this resource -->

## Key Points
<!-- Main takeaways or important information -->
-

## Details
<!-- Detailed information -->

## Examples
<!-- Examples or code snippets if applicable -->
```
// Code example
```

## 🎯 Projects Using This Resource
<%*
const projectQuery = `
TABLE WITHOUT ID
  file.link as "🎯 Used By",
  status as "Status",
  completion_percentage + "%" as "Progress"
FROM "1-Projects"
WHERE contains(related_resources, "${tp.file.title}")
  AND status != "completed"
SORT choice(status = "active", 1, 2) ASC, priority ASC
LIMIT 4
`;
tR += "```dataview\n" + projectQuery + "\n```\n\n";
%>

## 📋 Related Areas
<%*
const areaQuery = `
TABLE WITHOUT ID
  file.link as "📋 Area",
  area_category as "Category"
FROM "2-Areas"
WHERE contains(related_resources, "${tp.file.title}")
   OR area_category = "${await tp.system.suggester(["Software-Development", "Administration", "Personal", "Church"], ["Software-Development", "Administration", "Personal", "Church"], false, "Filter Area")}"
SORT choice(contains(related_resources, "${tp.file.title}"), 1, 2) ASC
LIMIT 3
`;
tR += "```dataview\n" + areaQuery + "\n```\n\n";
%>

## 📚 Related Learning Resources
<%*
const relatedQuery = `
LIST
FROM "3-Resources"
WHERE (area_category = "${await tp.system.suggester(["Software-Development", "Administration", "Personal", "Church"], ["Software-Development", "Administration", "Personal", "Church"], false, "Filter Area")}"
   OR contains(tags, "${await tp.system.suggester(["guide", "reference", "tool", "tutorial"], ["guide", "reference", "tool", "tutorial"], false, "Filter Tag")}"))
  AND file.name != "${tp.file.title}"
  AND difficulty <= "${await tp.system.suggester(["beginner", "intermediate", "advanced"], ["beginner", "intermediate", "advanced"], false, "Max Difficulty")}"
SORT file.mtime DESC
LIMIT 3
`;
tR += "```dataview\n" + relatedQuery + "\n```\n\n";
%>

## Notes
<!-- Any additional notes -->

## Quick Links
- [[<% tp.file.title %> Project|New Project]]
- [[<% tp.file.title %> Reference|Quick Reference]]
- [[3-Resources|All Resources]]
