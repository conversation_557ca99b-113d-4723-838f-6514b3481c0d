---
title: "Taio: a complete environment which is Clipboard, powerful Markdown editor and Actions"
source: "https://notes.nicfab.eu/en/posts/taio/"
author:
  - "[[NicFab]]"
published: 2022-02-25
created: 2025-07-23
description: "An all-in-one solution with a complete Markdown editor"
tags:
  - "clippings"
---
[![](https://notes.nicfab.eu/images/taio/taio.png)](https://taio.app/)

## Markdown Editor

Regarding **Markdown**, please refer to [our dedicated page](https://notes.nicfab.eu/it/pages/mkdown/), where we also referred to [**CommonMark**](https://notes.nicfab.eu/it/pages/mkdown/#commonmark) which is the evolutionary project of Markdown itself, aimed at defining a unique syntax. CommonMark is a relevant resource.

We have already dealt with Markdown editors in general, and have published the following contributions:

- [Obsidian part 1](https://notes.nicfab.eu/it/posts/obsidian/obsidian01/)
- [Obsidian part 2](https://notes.nicfab.eu/it/posts/obsidian/obsidian02/)
- [Obsidian part 3](https://notes.nicfab.eu/it/posts/obsidian/obsidian03/)
- [Joplyn](https://notes.nicfab.eu/it/posts/joplin/)
- [Standard Notes](https://notes.nicfab.eu/it/posts/standard_notes/)
- [Typewriter](https://notes.nicfab.eu/it/posts/typewriter/)

[We have also already said](https://notes.nicfab.eu/it/posts/typewriter/#typewriter) that, in our opinion, fundamentally note its aspects:

1. the features and functions available, on the one hand;
2. a good user experience, i.e. the best result in the user-application relationship, on the other.

## Taio

[**Taio**](https://taio.app/) is an app for [**macOS**](https://apps.apple.com/us/app/id1527036273#?platform=mac) and [**iOS**](https://apps.apple.com/us/app/id1527036273#?platform=iphone) developed by [**Ying Zhong**](https://apps.apple.com/us/developer/ying-zhong/id928924733) with features that are different from those of a simple editor.

**We tested Taio version 1.44.1.**

Taio offers three solutions in one and precisely:

1. Clipboard;
2. Markdown editor with CommonMark syntax;
3. Actions.

The app is available in a free version or “Taio Pro” for a monthly, annual or lifetime fee.

Once you open the app, you have the three environments available.

## Clipboard

The [**clipboard**](https://docs.taio.app/#/quick-start/clipboard) is handy because it collects any copied element and makes it available to the editor.

The settings are simple, as the following image shows.

![clips_settings](https://notes.nicfab.eu/images/taio/clip_settings.png#center)

Among the settings, there is the possibility of synchronization using iCloud; it seems to us a perfect solution that goes from the perspective of Apple users.  
Having the devices synchronized is very convenient.

## Editor

The [**editor**](https://docs.taio.app/#/quick-start/editor) is a Markdown syntax writing environment that supports [CommonMark](https://twitter.com/taioapp/status/1331189200222302211?s=21) and [GitHub Flavored Markdown Spec](https://github.github.com/gfm/).

The editor is mighty as it also supports [**Mermaid**](https://mermaid-js.github.io/mermaid/#/) since version 1.0.

**Mermaid** is a JavaScript-based diagramming and charting tool that renders Markdown-inspired text definitions to create and edit diagrams dynamically for charting.

Few Markdown editors support Mermaid [^1], and so, in our opinion, this is an added value.

The editor also supports [**highlightjs**](https://highlightjs.org/), a solution that automatically detects the code’s language.

There is a **configuration menu** which is divided into two parts: one for *general settings* and the other for *additional settings*.

editor\_settings\](/images/taio/editor\_settings.png#center)

Among the **general settings**, you can customize your choices, including:

- Theme:
- Text font;
- Code font;
- Font size;
- Line spacing;
- enable/disable Auto Correction;
- enable/disable Auto Capitalization;
- enable/disable Spell Checking;
- enable/disable Smart Puntuaction;
- Snippets Setting [^2] (when you repeat a lot of text, you can set snippets, i.e. combinations of characters that reproduce the text you set);
- enable/disable the review of changes to the file and the preview of the document as it will be published.

**Additional settings** include enabling/disabling:

- Focus Mode;
- Title for Single Tab;
- Line Wrapping;
- Readable Content;
- Set Line Width;
- Show Page Guide;
- Set Page Guide Widht;
- Shows Line Number;
- Shows Line Indicator;
- Shows Invisibles;
- Shows Cursor Position;
- Highlight Large Files;
- Enables Toolbar;
- Allows Full Screen;
- Soft Tabs;
- Set Bullet Syntax;
- Set Bold Syntax - (you can choose which font to use between \* and -);
- Set Italic Syntax - (you can choose which font to use between \* and -);
- Assets Folder.

![editor_settings](https://notes.nicfab.eu/images/taio/more_options.png#center)

The configuration menu provides a separate entry for **Markdown settings** and precisely whether to enable or not:

- GitHub Flavored;
- Line breaks - `<br>` and `</br>`;
- Smart Puntuaction;
- MatJax;
- inline Math
- Wiki links

![editor_settings](https://notes.nicfab.eu/images/taio/markdown_options.png#center)

The editor allows you to use both Markdown syntax and HTML codes.

Moreover, in the editor, there is a smart bar that - once the text is selected - proposes the following choices:

- bold;
- italic
- underlined;
- strikethrough;
- bulleted lists
- numbered lists;
- things to do;
- image management (link, select image from device or photos, and even sketch);
- link management (insert a link to an external resource or document).

Another exciting feature is that after copying text, for example, from a web page, if you proceed to paste that text, Taio asks the user if he wants to copy it as plain text or as formatted text.

Taio behaves intelligently when you need to insert brackets, proposing both (the open and the closed one), **but only if the text is selected**. Without text selection, we are not aware that the pair of parentheses is automatically inserted.

## Actions

Taio provides predefined actions that you can find in the appropriate section.  
These are shortcuts already set up, but the user can edit the existing ones or create new ones about its use.

![editor_settings](https://notes.nicfab.eu/images/taio/example_actions.png#center)

## Conclusions and privacy

According to the logic with which it was conceived and realized, Taio is an excellent solution favours the writer, allowing the user to have a single environment with a clipboard, an editor and actions.

In our opinion, Taio could be enhanced with some implementations that we indicate:

- add word count, character count (with or without spaces);
- add the reading time counting by providing in the settings the possibility for the user to customize it;
- add the possibility of using mathematical formulas in tables [according to these schemes](https://github.com/tgrosinger/md-advanced-tables/blob/main/docs/formulas.md#formulas-in-markdown-tables);
- provide the possibility for the user to choose a split-screen, i.e. a single window divided into two parts, where one shows the editor, and the other the preview;
- to be able to visualize the so-called “frontmatter”, especially for those who write code (e.g. pages for Hugo with declarations);
- unless we have missed the existence, the developer might foresee that the Tab bar is always visible or made visible through a shortcut (a combination of keys).

All in all, as we said, Taio seems to us a good app, and we hope that the developer - to whom we wrote to point out what we had just said - will consider our proposals.

Last but not least is the profile regarding privacy.

The developer refers to the privacy policy present at [this page](https://docs.taio.app/#/privacy).

Currently, the information provided is scarce, considering that the app is also aimed at European citizens to whom the GDPR applies.

However, the developer claims not to collect personal information apart from those related to the purchase of the app. In any case, the developer specifies that the data is not linked to you.

Probably, in our opinion, the privacy policy also deserves a review.

---

If this resource was helpful, you could contribute by

[Buy me a coffee](https://www.buymeacoffee.com/nicfab)

Or donate via

[Liberapay](https://liberapay.com/nicfab)

  

***Follow us on [Mastodon](https://mastodon.nicfab.it/@nicfab)***

***Stay tuned!***

[^1]: Also [Typewriter](https://notes.nicfab.eu/it/posts/typewriter/#format) supports Mermaid.

[^2]: Taio also allows you to use [TextExpander](https://textexpander.com/)