---

# Secure Vault Backup Procedures

## Overview

This guide provides comprehensive security-focused backup procedures for your HashiCorp Vault deployment, ensuring your secrets remain protected throughout the backup lifecycle while maintaining recoverability.

## Tags
#vault #backup #security #encryption #procedures #recovery #best-practices #yendorcats

---

## Security Principles for Vault Backups

### Core Security Requirements

1. **Encryption at Rest**: All backups must be encrypted before storage
2. **Encryption in Transit**: Secure transfer of backup files
3. **Access Control**: Lim<PERSON> who can create, access, and restore backups
4. **Key Management**: Separate encryption keys from backup data
5. **Audit Trail**: Log all backup and restore operations
6. **Geographic Distribution**: Store backups in multiple secure locations
7. **Retention Policies**: Secure deletion of expired backups

### Threat Model Considerations

- **Insider Threats**: Malicious or compromised administrators
- **External Attackers**: Unauthorized access to backup storage
- **Data Breaches**: Compromise of backup storage systems
- **Physical Security**: Theft or loss of backup media
- **Compliance**: Meeting regulatory requirements

---

## Multi-Layer Encryption Strategy

### 1. Primary Encryption (GPG)

```bash
#!/bin/bash
# secure-backup-encryption.sh

# Generate dedicated backup encryption key
gpg --full-generate-key --expert

# Key specifications:
# - Key type: RSA (sign and encrypt)
# - Key size: 4096 bits
# - Expiration: 2 years
# - Real name: "Vault Backup Key"
# - Email: "<EMAIL>"

# Export public key for team members
gpg --armor --export <EMAIL> > vault-backup-public.key

# Backup private key securely
gpg --armor --export-secret-keys <EMAIL> > vault-backup-private.key.asc
```

### 2. Secondary Encryption (AES-256)

```bash
#!/bin/bash
# double-encryption-backup.sh

VAULT_DATA_DIR="/vault/data"
BACKUP_DIR="/secure/vault-backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="vault-backup-$DATE"

# Generate random AES key for this backup
AES_KEY=$(openssl rand -hex 32)
AES_IV=$(openssl rand -hex 16)

# Create backup
tar -czf /tmp/$BACKUP_NAME.tar.gz -C /vault data config

# First layer: AES-256-CBC encryption
openssl enc -aes-256-cbc -salt -in /tmp/$BACKUP_NAME.tar.gz \
    -out /tmp/$BACKUP_NAME.aes -K $AES_KEY -iv $AES_IV

# Second layer: GPG encryption
gpg --trust-model always --encrypt --armor \
    --recipient <EMAIL> \
    --output $BACKUP_DIR/$BACKUP_NAME.gpg \
    /tmp/$BACKUP_NAME.aes

# Encrypt the AES key with GPG
echo "$AES_KEY:$AES_IV" | gpg --trust-model always --encrypt --armor \
    --recipient <EMAIL> \
    --output $BACKUP_DIR/$BACKUP_NAME.key.gpg

# Secure cleanup
shred -vfz -n 3 /tmp/$BACKUP_NAME.tar.gz /tmp/$BACKUP_NAME.aes
unset AES_KEY AES_IV

echo "Double-encrypted backup created: $BACKUP_NAME.gpg"
echo "AES key file: $BACKUP_NAME.key.gpg"
```

### 3. Key Splitting for Critical Backups

```bash
#!/bin/bash
# key-splitting-backup.sh

# Split GPG private key using Shamir's Secret Sharing
# Requires 'ssss' package: apt-get install ssss

PRIVATE_KEY_FILE="vault-backup-private.key.asc"
SHARES_DIR="/secure/key-shares"

mkdir -p $SHARES_DIR

# Split key into 5 shares, requiring 3 to reconstruct
ssss-split -t 3 -n 5 -w vault-backup < $PRIVATE_KEY_FILE

# This creates 5 shares that should be distributed to trusted individuals
# Store each share in a separate secure location
```

---

## Secure Backup Storage Locations

### 1. Local Encrypted Storage

```bash
#!/bin/bash
# setup-encrypted-backup-storage.sh

# Create encrypted filesystem for backups
BACKUP_DEVICE="/dev/sdb1"  # Adjust for your system
MOUNT_POINT="/secure/vault-backups"

# Create LUKS encrypted partition
cryptsetup luksFormat $BACKUP_DEVICE

# Open encrypted partition
cryptsetup luksOpen $BACKUP_DEVICE vault-backups

# Create filesystem
mkfs.ext4 /dev/mapper/vault-backups

# Mount with secure options
mkdir -p $MOUNT_POINT
mount -o noatime,nodev,nosuid,noexec /dev/mapper/vault-backups $MOUNT_POINT

# Set restrictive permissions
chmod 700 $MOUNT_POINT
chown vault:vault $MOUNT_POINT

# Add to /etc/fstab for persistent mounting
echo "/dev/mapper/vault-backups $MOUNT_POINT ext4 noatime,nodev,nosuid,noexec 0 2" >> /etc/fstab
```

### 2. Remote Secure Storage

```bash
#!/bin/bash
# secure-remote-backup.sh

BACKUP_FILE="$1"
REMOTE_HOST="backup-server.yourdomain.com"
REMOTE_USER="vault-backup"
REMOTE_PATH="/secure/vault-backups"

# Upload via SCP with key authentication
scp -i /secure/keys/backup-ssh-key \
    -o StrictHostKeyChecking=yes \
    -o UserKnownHostsFile=/secure/known_hosts \
    "$BACKUP_FILE" \
    "$REMOTE_USER@$REMOTE_HOST:$REMOTE_PATH/"

# Verify upload integrity
REMOTE_CHECKSUM=$(ssh -i /secure/keys/backup-ssh-key \
    "$REMOTE_USER@$REMOTE_HOST" \
    "sha256sum $REMOTE_PATH/$(basename $BACKUP_FILE)" | cut -d' ' -f1)

LOCAL_CHECKSUM=$(sha256sum "$BACKUP_FILE" | cut -d' ' -f1)

if [ "$LOCAL_CHECKSUM" = "$REMOTE_CHECKSUM" ]; then
    echo "✓ Remote backup verified: $(basename $BACKUP_FILE)"
else
    echo "❌ Remote backup verification failed!"
    exit 1
fi
```

### 3. Cloud Storage with Client-Side Encryption

```bash
#!/bin/bash
# cloud-backup-with-encryption.sh

BACKUP_FILE="$1"
CLOUD_BUCKET="your-backup-bucket"
CLOUD_PATH="vault-backups/$(date +%Y/%m)"

# Additional encryption layer for cloud storage
CLOUD_KEY=$(openssl rand -base64 32)

# Encrypt specifically for cloud storage
openssl enc -aes-256-gcm -salt -in "$BACKUP_FILE" \
    -out "${BACKUP_FILE}.cloud" -pass pass:"$CLOUD_KEY"

# Upload to cloud (example with AWS S3-compatible)
aws s3 cp "${BACKUP_FILE}.cloud" "s3://$CLOUD_BUCKET/$CLOUD_PATH/"

# Store cloud encryption key separately
echo "$CLOUD_KEY" | gpg --trust-model always --encrypt --armor \
    --recipient <EMAIL> \
    --output "${BACKUP_FILE}.cloud.key.gpg"

# Upload key file to different bucket/location
aws s3 cp "${BACKUP_FILE}.cloud.key.gpg" "s3://$CLOUD_BUCKET-keys/$CLOUD_PATH/"

# Secure cleanup
shred -vfz -n 3 "${BACKUP_FILE}.cloud"
unset CLOUD_KEY

echo "Cloud backup completed with separate key storage"
```

---

## Access Control and Authentication

### 1. Role-Based Backup Access

```bash
#!/bin/bash
# setup-backup-rbac.sh

# Create backup operator group
groupadd vault-backup-operators

# Create backup user
useradd -g vault-backup-operators -m -s /bin/bash vault-backup

# Set up sudo permissions for backup operations only
cat > /etc/sudoers.d/vault-backup << EOF
# Vault backup operators
%vault-backup-operators ALL=(vault) NOPASSWD: /usr/local/bin/vault-backup.sh
%vault-backup-operators ALL=(vault) NOPASSWD: /usr/local/bin/vault-restore.sh
%vault-backup-operators ALL=(root) NOPASSWD: /bin/systemctl stop vault
%vault-backup-operators ALL=(root) NOPASSWD: /bin/systemctl start vault
EOF

# Set up SSH key authentication for backup user
mkdir -p /home/<USER>/.ssh
chmod 700 /home/<USER>/.ssh

# Generate dedicated SSH key for backup operations
ssh-keygen -t ed25519 -f /home/<USER>/.ssh/backup_key -N ""
chmod 600 /home/<USER>/.ssh/backup_key*
chown -R vault-backup:vault-backup-operators /home/<USER>/.ssh
```

### 2. Multi-Factor Authentication for Restore

```bash
#!/bin/bash
# mfa-restore-wrapper.sh

# Require multiple approvals for restore operations
REQUIRED_APPROVALS=2
APPROVAL_FILE="/tmp/restore-approvals-$(date +%s)"

echo "Vault restore operation requested"
echo "Requires $REQUIRED_APPROVALS approvals"

# Collect approvals
for i in $(seq 1 $REQUIRED_APPROVALS); do
    echo "Approval $i of $REQUIRED_APPROVALS required"
    read -p "Enter approver name: " APPROVER
    read -s -p "Enter approval code: " APPROVAL_CODE
    echo
    
    # Verify approval code (implement your verification logic)
    if verify_approval_code "$APPROVER" "$APPROVAL_CODE"; then
        echo "$APPROVER:$(date):$APPROVAL_CODE" >> "$APPROVAL_FILE"
        echo "✓ Approval $i accepted"
    else
        echo "❌ Invalid approval code"
        rm -f "$APPROVAL_FILE"
        exit 1
    fi
done

# Proceed with restore if all approvals collected
if [ $(wc -l < "$APPROVAL_FILE") -eq $REQUIRED_APPROVALS ]; then
    echo "All approvals collected. Proceeding with restore..."
    # Call actual restore script
    /usr/local/bin/vault-restore-actual.sh "$@"
else
    echo "Insufficient approvals. Restore cancelled."
    exit 1
fi

# Cleanup
rm -f "$APPROVAL_FILE"
```

---

## Automated Secure Backup Pipeline

### 1. Complete Secure Backup Script

```bash
#!/bin/bash
# secure-vault-backup-pipeline.sh

set -euo pipefail

# Configuration
VAULT_DATA_DIR="/vault/data"
BACKUP_BASE_DIR="/secure/vault-backups"
LOG_FILE="/var/log/vault-backup.log"
RETENTION_DAYS=90
GPG_RECIPIENT="<EMAIL>"

# Logging function
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Error handling
trap 'log "ERROR: Backup failed at line $LINENO"' ERR

log "Starting secure Vault backup"

# Pre-backup checks
if ! vault status >/dev/null 2>&1; then
    log "ERROR: Vault is not accessible"
    exit 1
fi

if ! gpg --list-keys "$GPG_RECIPIENT" >/dev/null 2>&1; then
    log "ERROR: GPG key not found for $GPG_RECIPIENT"
    exit 1
fi

# Create timestamped backup directory
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="$BACKUP_BASE_DIR/$DATE"
mkdir -p "$BACKUP_DIR"

# 1. Create Vault snapshot
log "Creating Vault snapshot"
vault operator raft snapshot save "$BACKUP_DIR/vault-snapshot.snap"

# 2. Backup configuration files
log "Backing up configuration"
tar -czf "$BACKUP_DIR/vault-config.tar.gz" -C /vault config

# 3. Export policies and auth methods
log "Exporting Vault configuration"
vault policy list | while read policy; do
    if [ "$policy" != "default" ] && [ "$policy" != "root" ]; then
        vault policy read "$policy" > "$BACKUP_DIR/policy-$policy.hcl"
    fi
done

vault auth list -format=json > "$BACKUP_DIR/auth-methods.json"
vault secrets list -format=json > "$BACKUP_DIR/secret-engines.json"

# 4. Create comprehensive backup archive
log "Creating backup archive"
tar -czf "$BACKUP_DIR/vault-complete-backup.tar.gz" -C "$BACKUP_DIR" .

# 5. Multi-layer encryption
log "Applying encryption layers"

# Generate AES key for this backup
AES_KEY=$(openssl rand -hex 32)
AES_IV=$(openssl rand -hex 16)

# First layer: AES encryption
openssl enc -aes-256-cbc -salt \
    -in "$BACKUP_DIR/vault-complete-backup.tar.gz" \
    -out "$BACKUP_DIR/vault-backup.aes" \
    -K "$AES_KEY" -iv "$AES_IV"

# Second layer: GPG encryption
gpg --trust-model always --encrypt --armor \
    --recipient "$GPG_RECIPIENT" \
    --output "$BACKUP_DIR/vault-backup-$DATE.gpg" \
    "$BACKUP_DIR/vault-backup.aes"

# Encrypt AES key separately
echo "$AES_KEY:$AES_IV" | gpg --trust-model always --encrypt --armor \
    --recipient "$GPG_RECIPIENT" \
    --output "$BACKUP_DIR/vault-backup-$DATE.key.gpg"

# 6. Generate checksums
log "Generating checksums"
sha256sum "$BACKUP_DIR/vault-backup-$DATE.gpg" > "$BACKUP_DIR/vault-backup-$DATE.sha256"
sha256sum "$BACKUP_DIR/vault-backup-$DATE.key.gpg" >> "$BACKUP_DIR/vault-backup-$DATE.sha256"

# 7. Secure cleanup of intermediate files
log "Cleaning up intermediate files"
shred -vfz -n 3 "$BACKUP_DIR"/*.tar.gz "$BACKUP_DIR"/*.aes "$BACKUP_DIR"/*.snap
rm -f "$BACKUP_DIR"/*.hcl "$BACKUP_DIR"/*.json
unset AES_KEY AES_IV

# 8. Set secure permissions
chmod 600 "$BACKUP_DIR"/*.gpg "$BACKUP_DIR"/*.sha256
chown vault:vault "$BACKUP_DIR"/*.gpg "$BACKUP_DIR"/*.sha256

# 9. Upload to remote locations
log "Uploading to remote storage"
if command -v /usr/local/bin/upload-to-remote.sh >/dev/null; then
    /usr/local/bin/upload-to-remote.sh "$BACKUP_DIR/vault-backup-$DATE.gpg"
    /usr/local/bin/upload-to-remote.sh "$BACKUP_DIR/vault-backup-$DATE.key.gpg"
fi

# 10. Cleanup old backups
log "Cleaning up old backups"
find "$BACKUP_BASE_DIR" -type d -name "20*" -mtime +$RETENTION_DAYS -exec rm -rf {} \;

# 11. Verify backup integrity
log "Verifying backup integrity"
if gpg --verify "$BACKUP_DIR/vault-backup-$DATE.gpg" >/dev/null 2>&1; then
    log "✓ Backup verification successful"
else
    log "❌ Backup verification failed"
    exit 1
fi

log "Secure backup completed successfully: vault-backup-$DATE.gpg"

# Send notification
if command -v /usr/local/bin/send-backup-notification.sh >/dev/null; then
    /usr/local/bin/send-backup-notification.sh "SUCCESS" "Vault backup completed: $DATE"
fi
```

### 2. Backup Verification Script

```bash
#!/bin/bash
# verify-backup-integrity.sh

BACKUP_FILE="$1"
KEY_FILE="${BACKUP_FILE%.gpg}.key.gpg"

if [ -z "$BACKUP_FILE" ]; then
    echo "Usage: $0 <backup-file.gpg>"
    exit 1
fi

echo "Verifying backup integrity: $(basename $BACKUP_FILE)"

# 1. Verify GPG signature
if gpg --verify "$BACKUP_FILE" >/dev/null 2>&1; then
    echo "✓ GPG signature valid"
else
    echo "❌ GPG signature invalid"
    exit 1
fi

# 2. Check file integrity
if [ -f "${BACKUP_FILE%.gpg}.sha256" ]; then
    if sha256sum -c "${BACKUP_FILE%.gpg}.sha256" >/dev/null 2>&1; then
        echo "✓ File integrity verified"
    else
        echo "❌ File integrity check failed"
        exit 1
    fi
fi

# 3. Test decryption (without extracting)
TEMP_DIR=$(mktemp -d)
trap "rm -rf $TEMP_DIR" EXIT

if gpg --decrypt "$BACKUP_FILE" > "$TEMP_DIR/test.aes" 2>/dev/null; then
    echo "✓ GPG decryption successful"
    
    # Test AES decryption if key file exists
    if [ -f "$KEY_FILE" ]; then
        AES_CREDENTIALS=$(gpg --decrypt "$KEY_FILE" 2>/dev/null)
        AES_KEY=$(echo "$AES_CREDENTIALS" | cut -d: -f1)
        AES_IV=$(echo "$AES_CREDENTIALS" | cut -d: -f2)
        
        if openssl enc -aes-256-cbc -d \
            -in "$TEMP_DIR/test.aes" \
            -out "$TEMP_DIR/test.tar.gz" \
            -K "$AES_KEY" -iv "$AES_IV" >/dev/null 2>&1; then
            echo "✓ AES decryption successful"
            
            # Test archive integrity
            if tar -tzf "$TEMP_DIR/test.tar.gz" >/dev/null 2>&1; then
                echo "✓ Archive integrity verified"
                FILE_COUNT=$(tar -tzf "$TEMP_DIR/test.tar.gz" | wc -l)
                echo "  Archive contains $FILE_COUNT files"
            else
                echo "❌ Archive corrupted"
                exit 1
            fi
        else
            echo "❌ AES decryption failed"
            exit 1
        fi
    fi
else
    echo "❌ GPG decryption failed"
    exit 1
fi

echo "✅ Backup verification completed successfully"
```

---

## Secure Recovery Procedures

### 1. Controlled Recovery Process

```bash
#!/bin/bash
# secure-vault-recovery.sh

set -euo pipefail

BACKUP_FILE="$1"
RECOVERY_MODE="$2"  # full, partial, test

if [ -z "$BACKUP_FILE" ] || [ -z "$RECOVERY_MODE" ]; then
    echo "Usage: $0 <backup-file.gpg> <full|partial|test>"
    exit 1
fi

# Require multiple approvals for full recovery
if [ "$RECOVERY_MODE" = "full" ]; then
    echo "CRITICAL: Full Vault recovery requested"
    echo "This will replace all current Vault data"
    read -p "Type 'CONFIRM FULL RECOVERY' to proceed: " CONFIRMATION
    
    if [ "$CONFIRMATION" != "CONFIRM FULL RECOVERY" ]; then
        echo "Recovery cancelled"
        exit 1
    fi
    
    # Additional approval for production
    if [ "$(vault status -format=json | jq -r '.cluster_name')" = "production" ]; then
        /usr/local/bin/mfa-restore-wrapper.sh
    fi
fi

# Create recovery workspace
RECOVERY_DIR="/tmp/vault-recovery-$(date +%s)"
mkdir -p "$RECOVERY_DIR"
trap "shred -vfz -n 3 $RECOVERY_DIR/* 2>/dev/null; rm -rf $RECOVERY_DIR" EXIT

echo "Starting secure recovery process..."

# Decrypt backup
KEY_FILE="${BACKUP_FILE%.gpg}.key.gpg"
if [ -f "$KEY_FILE" ]; then
    # Two-layer decryption
    AES_CREDENTIALS=$(gpg --decrypt "$KEY_FILE")
    AES_KEY=$(echo "$AES_CREDENTIALS" | cut -d: -f1)
    AES_IV=$(echo "$AES_CREDENTIALS" | cut -d: -f2)
    
    gpg --decrypt "$BACKUP_FILE" > "$RECOVERY_DIR/backup.aes"
    openssl enc -aes-256-cbc -d \
        -in "$RECOVERY_DIR/backup.aes" \
        -out "$RECOVERY_DIR/backup.tar.gz" \
        -K "$AES_KEY" -iv "$AES_IV"
else
    # Single-layer decryption
    gpg --decrypt "$BACKUP_FILE" > "$RECOVERY_DIR/backup.tar.gz"
fi

# Extract backup
tar -xzf "$RECOVERY_DIR/backup.tar.gz" -C "$RECOVERY_DIR"

case "$RECOVERY_MODE" in
    "test")
        echo "✓ Test recovery successful - backup is valid"
        ;;
    "partial")
        echo "Partial recovery - specify what to restore"
        ls "$RECOVERY_DIR"
        ;;
    "full")
        echo "Performing full recovery..."
        systemctl stop vault
        
        # Backup current state
        mv /vault/data "/vault/data.backup.$(date +%Y%m%d_%H%M%S)"
        
        # Restore from backup
        cp -r "$RECOVERY_DIR"/* /vault/
        chown -R vault:vault /vault/
        
        systemctl start vault
        echo "✓ Full recovery completed"
        ;;
esac

unset AES_KEY AES_IV AES_CREDENTIALS
```

---

## Monitoring and Alerting

### 1. Backup Monitoring Script

```bash
#!/bin/bash
# monitor-backup-health.sh

BACKUP_DIR="/secure/vault-backups"
ALERT_EMAIL="<EMAIL>"
MAX_AGE_HOURS=25  # Alert if no backup in 25 hours

# Check for recent backups
LATEST_BACKUP=$(find "$BACKUP_DIR" -name "vault-backup-*.gpg" -mtime -1 | head -1)

if [ -z "$LATEST_BACKUP" ]; then
    echo "CRITICAL: No recent Vault backup found" | \
        mail -s "Vault Backup Alert" "$ALERT_EMAIL"
    exit 1
fi

# Verify latest backup
if /usr/local/bin/verify-backup-integrity.sh "$LATEST_BACKUP"; then
    echo "✓ Latest backup verified: $(basename $LATEST_BACKUP)"
else
    echo "CRITICAL: Latest backup verification failed" | \
        mail -s "Vault Backup Verification Failed" "$ALERT_EMAIL"
    exit 1
fi

# Check backup storage space
DISK_USAGE=$(df "$BACKUP_DIR" | tail -1 | awk '{print $5}' | sed 's/%//')
if [ "$DISK_USAGE" -gt 85 ]; then
    echo "WARNING: Backup storage ${DISK_USAGE}% full" | \
        mail -s "Vault Backup Storage Alert" "$ALERT_EMAIL"
fi

echo "Backup monitoring completed successfully"
```

---

## Best Practices Summary

### Security Checklist
- [ ] **Multi-layer encryption** (AES + GPG)
- [ ] **Separate key storage** from backup data
- [ ] **Access controls** with role-based permissions
- [ ] **Audit logging** of all backup operations
- [ ] **Regular verification** of backup integrity
- [ ] **Secure deletion** of temporary files
- [ ] **Geographic distribution** of backups
- [ ] **Retention policies** with secure cleanup

### Operational Checklist
- [ ] **Automated scheduling** with monitoring
- [ ] **Multiple storage locations** (local, remote, cloud)
- [ ] **Recovery testing** procedures
- [ ] **Documentation** of all procedures
- [ ] **Team training** on recovery processes
- [ ] **Incident response** plans
- [ ] **Regular key rotation** for backup encryption
- [ ] **Compliance verification** with regulations

---
