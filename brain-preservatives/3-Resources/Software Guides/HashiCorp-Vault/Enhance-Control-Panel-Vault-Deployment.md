---

# Enhance Control Panel Vault Deployment Guide

## Overview

This guide covers deploying HashiCorp Vault on Enhance Control Panel for your YendorCats project, providing a cost-effective secrets management solution that integrates with your existing hosting setup.

## Tags
#enhance #control-panel #vault #deployment #hosting #docker #yendorcats #budget #self-hosted

---

## Enhance Control Panel Overview

Enhance Control Panel is a modern web hosting control panel that supports:
- **Docker Containers** - Perfect for Vault deployment
- **Custom Applications** - Deploy your own services
- **SSL/TLS Management** - Automatic certificate handling
- **Database Management** - MySQL/PostgreSQL support
- **File Management** - Easy backup and configuration
- **Resource Monitoring** - Track usage and performance

### Benefits for Vault Deployment
- ✅ **Cost-Effective** - No additional server costs
- ✅ **Easy Management** - Web-based interface
- ✅ **Automatic Backups** - Built-in backup features
- ✅ **SSL Certificates** - Free Let's Encrypt integration
- ✅ **Resource Scaling** - Adjust resources as needed

---

## Pre-Deployment Preparation

### 1. Server Requirements

**Minimum Requirements:**
- **CPU**: 1 vCPU
- **RAM**: 512MB (1GB recommended)
- **Storage**: 5GB (10GB recommended)
- **Network**: Stable internet connection

**Recommended for Production:**
- **CPU**: 2 vCPU
- **RAM**: 2GB
- **Storage**: 20GB SSD
- **Backup**: Automated daily backups

### 2. Domain Setup

```bash
# Set up subdomain for Vault
vault.yourdomain.com -> Your Server IP

# Or use a dedicated domain
vaultserver.com -> Your Server IP
```

### 3. Firewall Configuration

```bash
# Required ports for Vault
Port 8200 (HTTPS) - Vault API
Port 22 (SSH) - Server management
Port 80/443 (HTTP/HTTPS) - Web interface
```

---

## Docker-Based Vault Deployment

### 1. Create Vault Directory Structure

```bash
# Create directory structure in Enhance
mkdir -p /home/<USER>/vault/{data,config,logs,scripts}
cd /home/<USER>/vault
```

### 2. Vault Configuration File

```hcl
# config/vault.hcl
ui = true

# Storage backend - file storage for simplicity
storage "file" {
  path = "/vault/data"
}

# Listener configuration
listener "tcp" {
  address = "0.0.0.0:8200"
  tls_cert_file = "/vault/config/tls/vault.crt"
  tls_key_file = "/vault/config/tls/vault.key"
  tls_min_version = "tls12"
}

# API address
api_addr = "https://vault.yourdomain.com:8200"

# Cluster address (for future scaling)
cluster_addr = "https://vault.yourdomain.com:8201"

# Disable mlock for containers
disable_mlock = true

# Enable audit logging
# audit "file" {
#   file_path = "/vault/logs/audit.log"
# }
```

### 3. Docker Compose Configuration

```yaml
# docker-compose.yml
version: '3.8'

services:
  vault:
    image: vault:1.15.2
    container_name: vault-server
    restart: unless-stopped
    ports:
      - "8200:8200"
      - "8201:8201"
    environment:
      VAULT_ADDR: 'https://0.0.0.0:8200'
      VAULT_LOCAL_CONFIG: |
        ui = true
        storage "file" {
          path = "/vault/data"
        }
        listener "tcp" {
          address = "0.0.0.0:8200"
          tls_cert_file = "/vault/config/tls/vault.crt"
          tls_key_file = "/vault/config/tls/vault.key"
          tls_min_version = "tls12"
        }
        api_addr = "https://vault.yourdomain.com:8200"
        disable_mlock = true
    volumes:
      - ./data:/vault/data:rw
      - ./config:/vault/config:ro
      - ./logs:/vault/logs:rw
    cap_add:
      - IPC_LOCK
    networks:
      - vault-network
    healthcheck:
      test: ["CMD", "vault", "status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

networks:
  vault-network:
    driver: bridge

volumes:
  vault-data:
    driver: local
  vault-logs:
    driver: local
```

### 4. SSL Certificate Setup

```bash
# Create TLS directory
mkdir -p config/tls

# Option 1: Self-signed certificate (development)
openssl req -x509 -newkey rsa:4096 -keyout config/tls/vault.key \
    -out config/tls/vault.crt -days 365 -nodes \
    -subj "/C=AU/ST=State/L=City/O=YendorCats/CN=vault.yourdomain.com"

# Option 2: Let's Encrypt (production)
# Use Enhance Control Panel's SSL management to generate certificates
# Then copy them to the vault config directory
```

---

## Enhance Control Panel Integration

### 1. Custom Application Setup

```bash
# In Enhance Control Panel:
# 1. Go to Applications > Custom Applications
# 2. Create new application
# 3. Set application type: Docker Compose
# 4. Upload your docker-compose.yml
# 5. Configure environment variables
```

### 2. Environment Variables Configuration

```bash
# Set in Enhance Control Panel
VAULT_ADDR=https://vault.yourdomain.com:8200
VAULT_LOG_LEVEL=INFO
VAULT_CLUSTER_ADDR=https://vault.yourdomain.com:8201
```

### 3. Reverse Proxy Configuration

```nginx
# Enhance will automatically configure this, but here's the manual config:
server {
    listen 443 ssl http2;
    server_name vault.yourdomain.com;
    
    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/private.key;
    
    location / {
        proxy_pass https://127.0.0.1:8200;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support for UI
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

---

## Deployment Scripts

### 1. Initial Deployment Script

```bash
#!/bin/bash
# deploy-vault.sh

set -e

echo "Deploying Vault on Enhance Control Panel..."

# Create directory structure
mkdir -p vault/{data,config,logs,scripts,backups}
cd vault

# Set permissions
chmod 755 data logs scripts backups
chmod 644 config/*

# Start Vault container
docker-compose up -d

# Wait for Vault to start
echo "Waiting for Vault to start..."
sleep 30

# Check Vault status
docker-compose logs vault

echo "Vault deployment completed!"
echo "Access Vault UI at: https://vault.yourdomain.com:8200"
echo "Remember to initialize and unseal Vault!"
```

### 2. Initialization Script

```bash
#!/bin/bash
# initialize-vault.sh

VAULT_ADDR="https://vault.yourdomain.com:8200"

echo "Initializing Vault..."

# Initialize Vault
INIT_OUTPUT=$(vault operator init -key-shares=5 -key-threshold=3 -format=json)

# Extract keys and token
echo "$INIT_OUTPUT" | jq -r '.unseal_keys_b64[]' > unseal-keys.txt
echo "$INIT_OUTPUT" | jq -r '.root_token' > root-token.txt

# Secure the files
chmod 600 unseal-keys.txt root-token.txt

echo "Vault initialized successfully!"
echo "Unseal keys saved to: unseal-keys.txt"
echo "Root token saved to: root-token.txt"
echo ""
echo "IMPORTANT: Store these files securely and separately!"
echo ""

# Unseal Vault
echo "Unsealing Vault..."
head -3 unseal-keys.txt | while read key; do
    vault operator unseal "$key"
done

echo "Vault is now unsealed and ready for use!"
```

### 3. Backup Script for Enhance

```bash
#!/bin/bash
# enhance-vault-backup.sh

BACKUP_DIR="/home/<USER>/vault/backups"
DATE=$(date +%Y%m%d_%H%M%S)
VAULT_DATA_DIR="/home/<USER>/vault/data"

# Create backup
tar -czf "$BACKUP_DIR/vault-backup-$DATE.tar.gz" \
    -C "/home/<USER>/vault" data config

# Encrypt backup
gpg --symmetric --cipher-algo AES256 \
    --output "$BACKUP_DIR/vault-backup-$DATE.tar.gz.gpg" \
    "$BACKUP_DIR/vault-backup-$DATE.tar.gz"

# Remove unencrypted backup
rm "$BACKUP_DIR/vault-backup-$DATE.tar.gz"

# Upload to Enhance backup storage (if available)
# enhance-cli backup upload "$BACKUP_DIR/vault-backup-$DATE.tar.gz.gpg"

# Clean old backups (keep 30 days)
find "$BACKUP_DIR" -name "vault-backup-*.tar.gz.gpg" -mtime +30 -delete

echo "Backup completed: vault-backup-$DATE.tar.gz.gpg"
```

---

## Monitoring and Maintenance

### 1. Health Check Script

```bash
#!/bin/bash
# vault-health-check.sh

VAULT_ADDR="https://vault.yourdomain.com:8200"
LOG_FILE="/home/<USER>/vault/logs/health-check.log"

# Function to log with timestamp
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

# Check if Vault is running
if docker ps | grep -q vault-server; then
    log "✓ Vault container is running"
else
    log "❌ Vault container is not running"
    docker-compose -f /home/<USER>/vault/docker-compose.yml up -d
    exit 1
fi

# Check Vault status
STATUS=$(vault status -format=json 2>/dev/null)
if [ $? -eq 0 ]; then
    SEALED=$(echo "$STATUS" | jq -r '.sealed')
    if [ "$SEALED" = "false" ]; then
        log "✓ Vault is unsealed and healthy"
    else
        log "⚠ Vault is sealed"
        # Auto-unseal if keys are available
        if [ -f "/home/<USER>/vault/unseal-keys.txt" ]; then
            head -3 /home/<USER>/vault/unseal-keys.txt | while read key; do
                vault operator unseal "$key" >/dev/null 2>&1
            done
            log "✓ Vault auto-unsealed"
        fi
    fi
else
    log "❌ Cannot connect to Vault"
    exit 1
fi

# Check disk space
DISK_USAGE=$(df /home/<USER>/vault | tail -1 | awk '{print $5}' | sed 's/%//')
if [ "$DISK_USAGE" -gt 80 ]; then
    log "⚠ Disk usage is high: ${DISK_USAGE}%"
fi

log "Health check completed"
```

### 2. Automated Maintenance

```bash
# Add to crontab in Enhance Control Panel
# */5 * * * * /home/<USER>/vault/scripts/vault-health-check.sh
# 0 2 * * * /home/<USER>/vault/scripts/enhance-vault-backup.sh
# 0 0 * * 0 /home/<USER>/vault/scripts/cleanup-logs.sh
```

### 3. Log Rotation

```bash
#!/bin/bash
# cleanup-logs.sh

LOG_DIR="/home/<USER>/vault/logs"

# Rotate Vault logs
if [ -f "$LOG_DIR/vault.log" ]; then
    mv "$LOG_DIR/vault.log" "$LOG_DIR/vault.log.$(date +%Y%m%d)"
    gzip "$LOG_DIR/vault.log.$(date +%Y%m%d)"
fi

# Clean old logs (keep 30 days)
find "$LOG_DIR" -name "*.log.*.gz" -mtime +30 -delete
find "$LOG_DIR" -name "health-check.log" -size +10M -exec truncate -s 0 {} \;

echo "Log cleanup completed"
```

---

## YendorCats Integration

### 1. Update Application Configuration

```json
{
  "Vault": {
    "Address": "https://vault.yourdomain.com:8200",
    "Token": "your-vault-token-here",
    "SecretPath": "secret/yendorcats/app-secrets",
    "SkipTlsVerify": false
  }
}
```

### 2. Client Deployment on Enhance

```bash
#!/bin/bash
# deploy-client-on-enhance.sh

CLIENT_NAME="$1"
if [ -z "$CLIENT_NAME" ]; then
    echo "Usage: $0 <client-name>"
    exit 1
fi

# Create client directory
mkdir -p "/home/<USER>/yendorcats"
cd "/home/<USER>/yendorcats"

# Copy application files
cp -r /home/<USER>/yendorcats-template/* .

# Update configuration for client
sed -i "s/{{CLIENT_NAME}}/$CLIENT_NAME/g" appsettings.json
sed -i "s/{{VAULT_ADDRESS}}/https:\/\/vault.yourdomain.com:8200/g" appsettings.json

# Set up client secrets in Vault
vault kv put secret/$CLIENT_NAME/yendorcats/app-secrets \
    DbConnectionString="Server=$CLIENT_NAME-db;Database=YendorCats;User=catuser;Password=TEMP_PASSWORD;" \
    JwtSecret="$(openssl rand -base64 64)" \
    JwtIssuer="YendorCatsApi-$CLIENT_NAME" \
    JwtAudience="YendorCatsClients-$CLIENT_NAME"

echo "Client $CLIENT_NAME deployed successfully!"
```

---

## Troubleshooting

### Common Issues and Solutions

#### 1. Vault Won't Start

```bash
# Check container logs
docker-compose logs vault

# Common fixes:
# - Check file permissions
# - Verify SSL certificates
# - Check port availability
# - Ensure sufficient disk space
```

#### 2. SSL Certificate Issues

```bash
# Regenerate self-signed certificate
openssl req -x509 -newkey rsa:4096 -keyout config/tls/vault.key \
    -out config/tls/vault.crt -days 365 -nodes \
    -subj "/C=AU/ST=State/L=City/O=YendorCats/CN=vault.yourdomain.com"

# Or use Enhance Control Panel's SSL management
```

#### 3. Memory Issues

```bash
# Check container memory usage
docker stats vault-server

# Increase memory limit in docker-compose.yml
services:
  vault:
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
```

#### 4. Backup Failures

```bash
# Check backup directory permissions
chmod 755 /home/<USER>/vault/backups

# Verify GPG is installed
which gpg || apt-get install gnupg

# Test backup manually
/home/<USER>/vault/scripts/enhance-vault-backup.sh
```

---

## Cost Optimization Tips

### Resource Management
- **Monitor Usage**: Use Enhance's monitoring tools
- **Scale Resources**: Adjust CPU/RAM based on actual usage
- **Optimize Storage**: Regular cleanup of logs and old backups
- **Shared Infrastructure**: Use same server for multiple services

### Backup Strategy
- **Local Backups**: Use Enhance's built-in backup features
- **External Storage**: Consider cloud storage for critical backups
- **Retention Policies**: Balance security with storage costs

### Security vs. Cost
- **Self-Signed Certs**: Free but requires manual management
- **Let's Encrypt**: Free automated SSL certificates
- **Basic Monitoring**: Use built-in tools before paid solutions

---

## Best Practices Summary

### Security
- ✅ Use HTTPS with valid certificates
- ✅ Implement proper firewall rules
- ✅ Regular security updates
- ✅ Secure backup encryption
- ✅ Monitor access logs

### Reliability
- ✅ Automated health checks
- ✅ Regular backups
- ✅ Container restart policies
- ✅ Resource monitoring
- ✅ Log rotation

### Maintenance
- ✅ Automated deployment scripts
- ✅ Regular updates
- ✅ Performance monitoring
- ✅ Capacity planning
- ✅ Documentation updates

---
