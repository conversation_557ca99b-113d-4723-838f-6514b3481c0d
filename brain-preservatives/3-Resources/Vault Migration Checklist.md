---
# CORE METADATA
creation_date: 2025-07-14
modification_date: 2025-07-14
type: resource
status: active
priority: high
area_category: Administration
owner: Jordan
tags: [para/resources, checklist, migration, vault-organization]

# RESOURCE CLASSIFICATION
source: Personal research
difficulty: intermediate
resource_type: checklist
url: ""
last_verified: 2025-07-14

# RELATIONSHIPS (Enhanced System)
related_projects: ["[[Vault Restructuring Project]]"]
related_areas: ["[[Administration]]", "[[Task Management]]"]
related_people: ["[[Jordan]]"]
references: ["[[Vault Organization System Guide]]"]
---

# 📋 Vault Migration Checklist

## 🎯 Migration Overview

This checklist guides the systematic migration of existing notes to the new metadata schema and relationship system.

---

## Phase 1: Template Updates ✅ COMPLETED
- [x] Enhanced Project template with new metadata schema
- [x] Enhanced Area template with smart queries
- [x] Enhanced Resource template with relationships
- [x] Enhanced Daily template with priority tasks
- [x] New Enhanced Task template created
- [x] Updated Tasks.md with priority dashboard

---

## Phase 2: Core Note Migration 🔄 IN PROGRESS

### High-Priority Notes (Complete First)
- [x] **YendorCats Project Documentation** - Updated with new schema
- [x] **Church Administration** - Updated with relationships
- [x] **YendorCats S3 Metadata Implementation** - Updated metadata
- [ ] **Software-Development** area note
- [ ] **Website-Maintenance** area note
- [ ] **Network-Administration** area note
- [ ] **Task Management** area note

### Project Notes Migration
```bash
# Find all project notes that need migration
find "1-Projects" -name "*.md" -exec grep -l "related:" {} \;
```

**Priority Order:**
1. [ ] Active projects with `status: active`
2. [ ] High-priority projects with `priority: high`
3. [ ] Recent projects (modified in last 30 days)
4. [ ] Remaining projects

### Area Notes Migration
```bash
# Find all area notes that need migration
find "2-Areas" -name "*.md" -exec grep -l "area:" {} \;
```

**Priority Order:**
1. [ ] Critical areas with `responsibility_level: critical`
2. [ ] High-responsibility areas
3. [ ] Frequently reviewed areas
4. [ ] Remaining areas

### Resource Notes Migration
```bash
# Find all resource notes that need migration
find "3-Resources" -name "*.md" -exec grep -l "area:" {} \;
```

**Priority Order:**
1. [ ] Frequently referenced resources
2. [ ] Project-specific resources
3. [ ] General reference materials
4. [ ] Archived resources

---

## Phase 3: Relationship Mapping 📊

### Identify Current 'related' Field Usage
```bash
# Extract all 'related' field values
grep -r "related:" . --include="*.md" | cut -d: -f3- | sort | uniq
```

### Categorize Relationships
For each note with `related` field:

1. **Analyze linked items**
   - [ ] Determine if they're projects, areas, or resources
   - [ ] Check if links are valid (files exist)
   - [ ] Identify relationship type (depends_on, references, etc.)

2. **Convert to typed arrays**
   - [ ] `related_projects: ["[[Project 1]]", "[[Project 2]]"]`
   - [ ] `related_areas: ["[[Area 1]]", "[[Area 2]]"]`
   - [ ] `related_resources: ["[[Resource 1]]", "[[Resource 2]]"]`

3. **Add bidirectional links**
   - [ ] Ensure relationships work both ways
   - [ ] Update target notes to reference back

### Relationship Health Check
```dataview
# Find notes with missing relationships
TABLE WITHOUT ID
  file.link as "Note",
  type as "Type",
  "Missing relationships" as "Issue"
FROM ""
WHERE (type = "project" AND length(related_areas) = 0)
   OR (type = "area" AND length(related_projects) = 0)
   OR (type = "resource" AND length(related_projects) = 0 AND length(related_areas) = 0)
```

---

## Phase 4: Task System Implementation 🎯

### Add Task Management Fields
For each note type, add:
```yaml
# TASK MANAGEMENT
task_priority: [urgent|high|medium|routine|someday]
task_context: [deep-work|admin|communication|maintenance|creative]
estimated_time: [15min|30min|1hr|2hr|4hr|8hr]
energy_required: [high|medium|low]
```

### Convert Existing Tasks
1. [ ] **Identify task-heavy notes**
   - Daily notes with many tasks
   - Project notes with task lists
   - Area notes with recurring tasks

2. [ ] **Prioritize existing tasks**
   - Mark urgent items as `task_priority: urgent`
   - Assign appropriate context tags
   - Estimate time and energy requirements

3. [ ] **Create dedicated task notes**
   - For complex tasks, create separate task notes
   - Link back to parent projects/areas
   - Use Enhanced Task template

### Update Task Queries
Replace old task queries with new priority-based ones:

**OLD:**
```dataview
TASK
FROM "1-Projects"
WHERE !completed
```

**NEW:**
```dataview
TABLE WITHOUT ID
  "🔥" as "",
  file.link as "URGENT TASKS"
FROM "1-Projects"
WHERE !completed AND task_priority = "urgent"
LIMIT 5
```

---

## Phase 5: Quality Assurance 🔍

### Metadata Validation
- [ ] **Check required fields** - All notes have core metadata
- [ ] **Validate field types** - Arrays are properly formatted
- [ ] **Verify relationships** - All linked notes exist
- [ ] **Test dataview queries** - No broken queries in templates

### Performance Testing
- [ ] **Query speed** - All dataview queries load quickly
- [ ] **Memory usage** - Vault doesn't slow down significantly
- [ ] **Search functionality** - Obsidian search still works well

### User Experience Testing
- [ ] **Navigation flows** - Easy to move between related notes
- [ ] **Task visibility** - Important tasks are prominent
- [ ] **Information discovery** - Can find relevant content easily

---

## Phase 6: Documentation & Training 📚

### Create Documentation
- [x] **Vault Organization System Guide** - Complete reference
- [x] **Migration Checklist** - This document
- [ ] **Quick Start Guide** - For daily usage
- [ ] **Troubleshooting Guide** - Common issues and fixes

### Update Existing Documentation
- [ ] **README.md** - Update with new system overview
- [ ] **Home.md** - Add links to new organization system
- [ ] **Templates/README.md** - Document new templates

---

## 🚨 Common Migration Issues

### 1. **Broken Links**
- **Problem**: `[[Old Note Name]]` links break after renaming
- **Solution**: Use Obsidian's "Update internal links" feature

### 2. **Dataview Errors**
- **Problem**: Queries fail due to missing fields
- **Solution**: Add fallback values: `choice(field, field, "default")`

### 3. **Performance Issues**
- **Problem**: Too many complex queries slow down vault
- **Solution**: Add LIMIT clauses and optimize WHERE conditions

### 4. **Relationship Loops**
- **Problem**: Circular references in relationships
- **Solution**: Use dependency tracking to identify and break loops

---

## 📊 Migration Progress Tracking

### Completion Metrics
```dataview
TABLE WITHOUT ID
  "Migration Progress" as "Category",
  length(filter(rows, (r) => contains(r.tags, "migrated"))) as "Completed",
  length(rows) as "Total",
  round(length(filter(rows, (r) => contains(r.tags, "migrated"))) / length(rows) * 100, 1) + "%" as "Progress"
FROM ""
WHERE type = "project" OR type = "area" OR type = "resource"
GROUP BY "migration"
```

### Next Actions
1. **Continue migrating high-priority notes**
2. **Test relationship queries regularly**
3. **Update templates based on usage patterns**
4. **Monitor system performance**

---

**Tags**: #migration #checklist #vault-organization #metadata #relationships #task-management

---
