---
creation_date: 2025-01-27
modification_date: 2025-01-27
type: legal-document
status: draft
client: YendorCats
project: YendorCats Web Development
tags: [para/areas, legal, sla, web-development, contract, yendorcats]
area: Finance
priority: high
related: [YendorCats Service Agreement, YendorCats Project Quote, YendorCats Financial Tracking]
---

# SERVICE LEVEL AGREEMENT (SLA)
## YENDORCATS MAINE COON BREEDER WEBSITE
## WEB DEVELOPMENT, HOSTING & CLOUDFLARE SERVICES AGREEMENT

**BETWEEN**

**Name & Business:** PaceySpace Digital - Jordan Pacey
**Address:** 1 Adelaide Drive, Caboolture, Queensland, Australia 4510
**Phone:** 07 2111 0402
**ABN:** 81 ***********
**Email:** <EMAIL>
**GST Registered:** No

**AND**

**Name & Business:** YendorCats - Exotic Maine Coon Breeder
**ABN:** [Client ABN]
**Address:** [Client Address]
**Email:** [Client Email]
**Phone:** [Client Phone]

## 1. SERVICE OVERVIEW

This document constitutes a Service Level Agreement (SLA) between PaceySpace Digital and YendorCats, effective as of the date of the agreement. This SLA details the terms and conditions under which PaceySpace Digital will provide comprehensive web development, CI/CD pipeline management, hosting, and maintenance services for the YendorCats professional Maine Coon cat breeder website and gallery system. These services will be delivered using modern web technologies, cloud infrastructure, Cloudflare services, and industry best practices. This Agreement is established under the laws of Queensland, Australia, and is subject to the Australian Consumer Law.

## 2. TERM

**2.1:** This SLA (Service Level Agreement) shall commence on ______________ and continue for the initial development period plus twelve **(12)** months of hosting and maintenance until _____________.

**2.2:** This SLA shall automatically renew for successive twelve **(12)** month periods unless either party provides written notice of non-renewal at least sixty **(60)** days prior to the end of the current Term.

## 3. SERVICES PROVIDED

### 3.1. YendorCats Website Development Services

The Service Provider undertakes to perform the following development activities for the YendorCats Maine Coon breeder website:

**a)** Design and develop a modern, responsive website for YendorCats featuring:
- Professional cat gallery system with rich metadata for showcasing Maine Coon cats
- Advanced filtering and search capabilities (by breed, age, gender, availability, bloodline)
- Mobile-responsive design optimized for all devices and screen sizes
- Contact forms and inquiry management system for potential buyers
- Admin interface for easy content management and cat profile updates
- SEO optimization for cat breeder and Maine Coon related searches

**b)** Implement robust backend infrastructure including:
- .NET 8 RESTful API with secure JWT authentication
- MariaDB database with optimized schema for cat breeding data
- User management and role-based authorization system
- Comprehensive error handling, logging, and monitoring
- API documentation with Swagger/OpenAPI specifications

**c)** Integrate cloud storage and content delivery services:
- Backblaze B2 S3-compatible storage for high-quality cat images
- Rich metadata storage for cat profiles, breeding information, and lineage
- Automated image optimization and compression for web delivery
- Automated backup and disaster recovery systems

**d)** Develop specialized file upload microservice with:
- User-friendly upload interface with drag-and-drop functionality
- Rich metadata management for cat images (name, age, breed, bloodline, etc.)
- Image preview and editing capabilities before upload
- Progress tracking and comprehensive error handling
- Batch upload capabilities for multiple cat photos

### 3.2. Hosting and Infrastructure Services

**Technical Infrastructure:**
- Docker containers for all services (API, Database, File Uploader) deployed on Enhance Control Panel
- MariaDB database for data storage with automated daily backups
- Backblaze B2 for S3-compatible storage with redundancy and global access
- Cloudflare integration for CDN, WAF, DNS, and security services
- SSL/TLS encryption for all communications with automatic renewal

**a)** Configure and maintain HTTPS for secure communication across all services
**b)** Implement and maintain automated backup systems for website data and cat profiles
**c)** Monitor website performance, availability, and security 24/7
**d)** Apply security patches and updates to all infrastructure components
**e)** Provide comprehensive DNS management services
**f)** Complete initial hosting infrastructure setup within **14 days** of the commencement date, with a 1-week leeway period for unforeseen circumstances

### 3.3. Cloudflare Services Integration

The Service Provider shall implement and manage comprehensive Cloudflare services to enhance the YendorCats website performance, security, and reliability:

**a) Core Cloudflare Services (Included):**
- **Content Delivery Network (CDN):** Global content caching for fast image and page loading worldwide
- **DNS Management:** Reliable, fast DNS resolution with 100% uptime SLA
- **SSL/TLS Certificates:** Free SSL certificates with automatic renewal and modern encryption
- **DDoS Protection:** Automatic protection against distributed denial-of-service attacks
- **Web Application Firewall (WAF):** Basic protection against common web threats and vulnerabilities

**b) Enhanced Cloudflare Services (Optional Upgrades):**
- **Cloudflare Pro Plan ($20 USD/month):**
  - Advanced DDoS protection with detailed analytics
  - Image optimization and Polish for faster cat photo loading
  - Mobile optimization for improved mobile user experience
  - Enhanced security features and threat intelligence

- **Cloudflare Business Plan ($200 USD/month):**
  - Advanced WAF with custom rules for cat breeder specific protection
  - Load balancing for high availability during peak traffic
  - Advanced analytics and performance insights
  - Priority support with faster response times

- **Cloudflare Enterprise Plan (Custom Pricing):**
  - Enterprise-grade security and performance
  - Custom SSL certificates and advanced encryption
  - Dedicated customer success manager
  - Advanced bot management and fraud protection

**c) Specialized Cloudflare Features for Cat Breeders:**
- **Image Optimization:** Automatic compression and format conversion for cat photos
- **Mobile Optimization:** Enhanced mobile experience for customers browsing on phones
- **Geo-targeting:** Content optimization based on visitor location
- **Analytics:** Detailed visitor analytics and performance metrics
- **Security Rules:** Custom security rules to protect against industry-specific threats

### 3.4. CI/CD Pipeline Management

The Service Provider undertakes to perform the following CI/CD activities for the YendorCats website:

**a)** Establish and manage a continuous integration and continuous deployment (CI/CD) pipeline for the YendorCats website, leveraging the capabilities of the Enhance Control Panel and modern DevOps practices.

**b)** Design and implement automated testing protocols, build procedures, and deployment sequences within the CI/CD environment, specifically tailored for the cat breeder website functionality.

**c)** Continuously monitor the CI/CD pipeline for operational anomalies and promptly remediate any identified failures to ensure smooth updates and deployments.

**d)** Uphold rigorous version control practices using Git and maintain established code quality assurance measures throughout the CI/CD lifecycle.

**e)** Generate and deliver comprehensive documentation outlining the architecture and configuration of the implemented CI/CD pipeline for the YendorCats project.

**f)** Apply recognized security best practices in the development and operation of the CI/CD pipeline to protect the integrity and confidentiality of YendorCats' data and customer information.

**g)** Achieve initial operational status of the CI/CD pipeline within **14 days** from the commencement date. Recognizing the potential for unforeseen delays, a contingency period of **7 days** is hereby provided, subject to prior written notification to the Client detailing the reasons for the potential delay.

### 3.5. Scalability Services for Cat Breeding Business

The Service Provider shall undertake the following measures to ensure infrastructure scalability for YendorCats' growing business:

**a)** Establish and maintain infrastructure capable of effectively managing anticipated traffic surges during peak periods, specifically including, but not limited to:
- Cat breeding seasons and kitten availability announcements
- Major cat show events and Maine Coon exhibitions
- Social media campaigns and viral content sharing
- Holiday periods when families seek new pets

**b)** Implement and configure auto-scaling functionalities within the hosting environment where technically feasible and deemed beneficial for maintaining service levels during high traffic periods.

**c)** Provision and manage replicated server instances or equivalent resources as necessary to ensure consistent performance and availability during periods of heightened website traffic from potential cat buyers.

**d)** Continuously monitor key resource utilization metrics (e.g., CPU, memory, bandwidth, database performance) and proactively scale infrastructure resources in anticipation of increased demand to prevent performance degradation.

### 3.6. Ongoing Maintenance Services

**a)** Continuous monitoring of:
- Website uptime and performance metrics
- Database performance and query optimization
- Security events and threat detection
- Backup integrity and recovery procedures
- Cat gallery loading times and image optimization
- Contact form functionality and inquiry management

**b)** Regular maintenance including:
- Security updates and patches for all components
- Performance optimization and database tuning
- Content updates and cat profile management (up to 2 hours monthly)
- Technical support via email and phone during business hours
- Monthly performance reports and analytics

## 4. SERVICE LEVELS

**a) Website Uptime:** The Service Provider warrants a minimum uptime of **99.9%** for the YendorCats website, with uptime being calculated on a monthly basis.

**b)** **The guaranteed uptime** specified in section (a) excludes scheduled maintenance periods. The Client shall receive notification of such scheduled maintenance at least forty-eight **(48)** hours prior to commencement.

**c)** Scheduled maintenance activities will be performed during off-peak hours, specifically between 23:00 and 05:00 Australian Eastern Standard Time (AEST), unless otherwise mutually agreed upon in writing by both parties.

### 4.1. PERFORMANCE METRICS

**a) Page Load Time:** Under normal operating conditions, the YendorCats website shall achieve a page load time of **three (3) seconds** or less for at least **ninety percent (90%)** of all page requests. This metric will be measured and reported on a **monthly** basis, with special attention to:
- Homepage loading performance
- Cat gallery page performance
- Individual cat profile pages
- Contact and inquiry forms

**b) API Response Time:** Under normal operating conditions, the API endpoints associated with the YendorCats website shall respond to at least **ninety-five percent (95%)** of all requests within a timeframe not exceeding **five hundred (500) milliseconds**. This metric will be measured and reported on a **monthly** basis.

**c) Cat Gallery Image Load Time:** Gallery images shall load within **two (2) seconds** for at least **ninety-five percent (95%)** of requests, utilizing Cloudflare CDN optimization and automatic image compression. This includes:
- Thumbnail images in gallery views
- Full-size images in lightbox displays
- Cat profile images
- Breeding lineage photos

**d) Database Query Performance:** At least **ninety-five percent (95%)** of all database queries executed in support of the YendorCats website shall complete within a duration of **two hundred (200) milliseconds** or less. This metric will be measured and reported on a **monthly** basis, with focus on:
- Cat search and filtering queries
- Gallery loading queries
- Admin interface data retrieval
- Contact form submissions

**e) Mobile Performance:** The YendorCats website shall achieve a Google PageSpeed Insights score of **85 or higher** for mobile devices, ensuring optimal performance for customers browsing on smartphones and tablets.

## 5. INCIDENT MANAGEMENT

### 5.1. INCIDENT CLASSIFICATION AND RESPONSE TIMES

The following table outlines the classification of incidents and the corresponding target response and resolution times:

| Severity | Description | Response Time | Resolution Time |
|----------|-------------|---------------|-----------------|
| **Critical** | Complete service outage, data breach, or security incident | 30 Minutes | 4 Hours |
| **High** | Partial service outage or severe performance degradation | 2 Hours | 8 Hours |
| **Medium** | Non-critical feature unavailable or minor performance issues | 4 Hours | 24 Hours |
| **Low** | Cosmetic issues or feature requests | 8 Hours | 72 Hours |

### 5.2. HOW TO REPORT AN INCIDENT

**Choose Your Reporting Method:**
- **Email Us:** Send <NAME_EMAIL>
- **Call Us:** Reach us by phone at 07 2111 0402

**What to Include in Your Report:**
- **What's happening?** - Describe the problem clearly
- **When did it start?** - Tell us the exact time you first noticed the issue
- **Can we replicate it?** - If possible, list the steps to reproduce the problem
- **How is it affecting you?** - Explain the impact on your business

## 6. MONITORING AND REPORTING

### 6.1. Continuous Monitoring

**a)** The Service Provider shall implement 24/7 monitoring of:
- Website availability and performance
- Database performance and connectivity
- API response times and error rates
- Security events and potential threats
- Backup completion and integrity

**b)** Automated alerts shall be configured for potential issues with immediate notification to the Service Provider's technical team.

### 6.2. Regular Reporting

**a)** The Service Provider shall provide monthly performance reports including:
- Website uptime statistics and availability metrics
- Performance metrics (page load times, API response times)
- Incident summary with resolution details
- Backup status and verification results
- Security status and threat analysis

**b)** Monthly reports shall be delivered within **10 business days** after the end of each month.

**c)** Quarterly service review meetings shall be scheduled to discuss performance, issues, and improvement opportunities. These meetings shall be scheduled at least **2 weeks** in advance.

## 7. FEES AND PAYMENT

### 7.1 YendorCats Website Development Services

**Total Development Cost:** **$26,647.50 AUD** (including GST)
- Development Services: $24,225.00 AUD
- GST (10%): $2,422.50 AUD

**Payment Schedule:**
- **Deposit (30%):** $7,994.25 AUD - Due upon contract signing
- **Progress Payment (40%):** $10,659.00 AUD - Due at 50% completion
- **Final Payment (30%):** $7,994.25 AUD - Due upon project completion

### 7.2 Ongoing Service Fees

**a) Base Monthly Service Fee:**
Our base monthly fee for the comprehensive hosting, maintenance, and CI/CD services outlined in Section 3 is **$350 AUD** per month, which includes:
- Website hosting and infrastructure management
- Database maintenance and optimization
- Security monitoring and updates
- Basic Cloudflare services (CDN, DNS, SSL, basic WAF)
- Content updates (up to 2 hours monthly)
- Technical support during business hours
- Monthly performance reports

**b) Currency & Tax:**
All fees are quoted in Australian Dollars (AUD) and are GST-free as the Service Provider is not GST registered.

**c) Cloudflare Service Upgrades (Optional):**
Enhanced Cloudflare services can be added to improve performance and security:

- **Cloudflare Pro Plan:** $30 AUD per month (converted from $20 USD)
  - Advanced DDoS protection and analytics
  - Image optimization for faster cat photo loading
  - Mobile optimization for improved user experience
  - Enhanced security features

- **Cloudflare Business Plan:** $300 AUD per month (converted from $200 USD)
  - Advanced WAF with custom security rules
  - Load balancing for high availability
  - Advanced analytics and performance insights
  - Priority support

**d) Resource Scaling for High Traffic:**
To ensure your website always performs optimally during peak periods (breeding seasons, kitten announcements), we offer flexible resource scaling:

- **Additional container instances:** $15 AUD per instance per day
- **Additional database resources:** $5 AUD per GB of storage per month
- **Additional bandwidth:** $0.10 AUD per GB beyond your included allocation
- **Additional S3 storage:** $0.05 AUD per GB per month

### 7.3 Payment Terms

**a)** Monthly service fees are due on the first day of each month
**b)** Invoices are payable within **14 days** of issue date
**c)** Late payment fees of **2% per month** apply to overdue amounts
**d)** Services may be suspended for accounts more than **30 days** overdue

## 8. SERVICE CREDITS

### 8.1 How Credits Are Calculated

If we fall short of the uptime guarantee specified in Section 4, you'll be eligible for service credits applied to your next monthly invoice, calculated as a percentage of your monthly fee:

| Monthly Uptime | Service Credit (% of monthly fee) |
|----------------|-----------------------------------|
| 99.0% - 99.9% | 10% |
| 98.0% - 98.9% | 25% |
| 97.0% - 97.9% | 50% |
| Below 97.0% | 100% |

### 8.2 Requesting Service Credits

Service credits must be requested within **30 days** of the end of the month in which the downtime occurred. Requests must include specific details about the downtime experienced.

## 9. CLIENT RESPONSIBILITIES

The Client agrees to the following responsibilities:

- **Designating a Point of Contact:** Appointing a primary contact person for all communications with PaceySpace
- **Prompt Issue Reporting:** Immediately reporting any issues or incidents that arise
- **Adhering to Payment Terms:** Ensuring all invoices are paid in accordance with the agreed-upon payment terms
- **Content & Data Compliance:** Complying with all applicable laws and regulations concerning website content and data
- **Content Control & Rights:** Maintaining full control over website content and guaranteeing it does not infringe upon any laws or third-party rights
- **Responding to Requests:** Responding to our requests for information or approvals within **5 business days**
- **Providing Feedback on Deliverables:** Submitting feedback on deliverables within **10 business days**

## 10. CONFIDENTIALITY & DATA PROTECTION

### 10.1 Mutual Confidentiality

Both parties agree to maintain strict confidentiality regarding:
- Proprietary business information and trade secrets
- Customer data and personal information
- Technical specifications and system architecture
- Financial information and business strategies

### 10.2 Data Protection Compliance

The Service Provider will:
- Comply with Australian Privacy Principles and applicable data protection laws
- Implement appropriate technical and organizational security measures
- Ensure secure handling of personal and sensitive data
- Provide data breach notification within **72 hours** if required

## 11. INTELLECTUAL PROPERTY

### 11.1 Ownership

Upon final payment of all development fees, the Client will own:
- All custom-developed source code and databases
- All design elements and creative materials
- All documentation and training materials

### 11.2 Third-Party Components

- Open-source libraries remain under their respective licenses
- Third-party services (hosting, storage) subject to their terms
- Service Provider retains rights to general development methodologies

## 12. LIMITATION OF LIABILITY

The Service Provider's total liability under this agreement shall not exceed the total amount paid by the Client in the preceding 12 months. The Service Provider shall not be liable for indirect, incidental, consequential, or punitive damages.

## 13. TERMINATION

Either party may terminate this agreement with **30 days** written notice. Upon termination, the Client pays for all services rendered to date, and both parties return confidential information.

## 14. GOVERNING LAW

This agreement is governed by the laws of Queensland, Australia. Any disputes will be resolved through negotiation, mediation, and if necessary, arbitration.

## 15. SIGNATURES

**SERVICE PROVIDER:**
Jordan Pacey (PaceySpace Digital)  
Signature: _________________________ Date: _____________

**CLIENT:**
YendorCats  
Name: _____________________________  
Signature: _________________________ Date: _____________

---

*This Service Level Agreement is effective from the date of signing and remains in effect for the duration specified in Section 2.*
