---
creation_date: <% tp.date.now("YYYY-MM-DD") %>
modification_date: <% tp.date.now("YYYY-MM-DD") %>
type: resource
source: <% await tp.system.suggester(["Personal research", "Website", "Book", "Course", "Documentation", "Other"], ["Personal research", "Website", "Book", "Course", "Documentation", await tp.system.prompt("Source")], false, "Source") %>
tags: [para/resources, <% await tp.system.suggester(["guide", "reference", "tool", "tutorial", "other"], ["guide", "reference", "tool", "tutorial", await tp.system.prompt("Custom Tag")], false, "Primary Tag") %>]
area: <% await tp.system.suggester(["Software-Development", "Administration", "Personal", "Church", "Other"], ["Software-Development", "Administration", "Personal", "Church", await tp.system.prompt("Area Name")], false, "Related Area") %>
difficulty: <% await tp.system.suggester(["easy", "medium", "hard"], ["easy", "medium", "hard"], false, "Difficulty") %>
url: <% await tp.system.prompt("URL (if applicable)", "") %>
---

# <% tp.file.title %>

## Overview
<!-- Brief description of this resource -->

## Key Points
<!-- Main takeaways or important information -->
-

## Details
<!-- Detailed information -->

## Examples
<!-- Examples or code snippets if applicable -->
```
// Code example
```

## Related Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  status as "Status",
  priority as "Priority"
FROM "1-Projects"
WHERE contains(file.content, "[[<% tp.file.title %>]]")
SORT priority ASC
```

## Related Areas
```dataview
TABLE WITHOUT ID
  file.link as "Area",
  area as "Category"
FROM "2-Areas"
WHERE contains(file.content, "[[<% tp.file.title %>]]") OR area = "<% await tp.system.suggester(["Software-Development", "Administration", "Personal", "Church"], ["Software-Development", "Administration", "Personal", "Church"], false, "Filter Area") %>"
```

## Related Resources
```dataview
LIST
FROM "3-Resources"
WHERE contains(tags, "<% await tp.system.suggester(["guide", "reference", "tool", "tutorial"], ["guide", "reference", "tool", "tutorial"], false, "Filter Tag") %>") AND file.name != "<% tp.file.title %>"
LIMIT 5
```

## Notes
<!-- Any additional notes -->

## Quick Links
- [[<% tp.file.title %> Project|New Project]]
- [[<% tp.file.title %> Reference|Quick Reference]]
- [[3-Resources|All Resources]]
