---
creation_date: <% tp.date.now("YYYY-MM-DD") %>
modification_date: <% tp.date.now("YYYY-MM-DD") %>
type: area
status: active
area: <% await tp.system.suggester(["Software-Development", "Administration", "Personal", "Church", "Other"], ["Software-Development", "Administration", "Personal", "Church", await tp.system.prompt("Area Category")], false, "Area Category") %>
area_owner: <% await tp.system.suggester(["Jordan", "Other"], ["Jordan", await tp.system.prompt("Area Owner")], false, "Area Owner") %>
responsibility_level: <% await tp.system.suggester(["high", "medium", "low"], ["high", "medium", "low"], false, "Responsibility Level") %>
review_frequency: <% await tp.system.suggester(["weekly", "monthly", "quarterly"], ["weekly", "monthly", "quarterly"], false, "Review Frequency") %>
tags: [para/areas, <% await tp.system.suggester(["software-dev", "administration", "church", "personal", "other"], ["software-dev", "administration", "church", "personal", await tp.system.prompt("Custom Tag")], false, "Primary Tag") %>]
last_review_date: <% tp.date.now("YYYY-MM-DD") %>
next_review_date: <% tp.date.now("YYYY-MM-DD", 30) %>
---

# <% tp.file.title %>

## Overview
<!-- Brief description of this area of responsibility -->

## Current Focus
<!-- What's the current focus in this area? -->
-

## Key Responsibilities
<!-- List the key responsibilities in this area -->
-

## Regular Tasks
<!-- Recurring tasks in this area -->
- [ ] Daily:
- [ ] Weekly:
- [ ] Monthly:

## Active Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  priority as "Priority",
  deadline as "Deadline",
  completion_percentage + "%" as "Progress"
FROM "1-Projects"
WHERE area = "<% await tp.system.suggester(["Software-Development", "Administration", "Personal", "Church"], ["Software-Development", "Administration", "Personal", "Church"], false, "Filter Area") %>" AND (status = "active" OR !contains(status, "completed"))
SORT priority ASC, deadline ASC
```

## Related Resources
```dataview
TABLE WITHOUT ID
  file.link as "Resource",
  source as "Source",
  file.mtime as "Last Modified"
FROM "3-Resources"
WHERE contains(file.content, "[[<% tp.file.title %>]]") OR area = "<% await tp.system.suggester(["Software-Development", "Administration", "Personal", "Church"], ["Software-Development", "Administration", "Personal", "Church"], false, "Filter Area") %>"
SORT file.mtime DESC
LIMIT 10
```

## Recent Notes
```dataview
TABLE WITHOUT ID
  file.link as "Note",
  file.mtime as "Last Modified"
FROM -"Templates"
WHERE contains(file.content, "[[<% tp.file.title %>]]")
SORT file.mtime DESC
LIMIT 5
```

## Key Metrics
<!-- Metrics to track in this area -->
-

## Quick Links
- [[<% tp.file.title %> Project|New Project]]
- [[<% tp.file.title %> Resource|New Resource]]
- [[<% tp.file.title %> Meeting|New Meeting]]
- [[2-Areas|All Areas]]
