---
creation_date: 2025-07-14
modification_date: 2025-07-14
type: documentation
aliases: [SSH Keys Documentation, Obsidian Notes SSH]
tags: [para/resources, ssh, security, dotfiles, configuration]
resource: Dotfiles
related_resources: ["Dotfiles-Management", "Dotfiles-Components"]
---

# SSH Keys Documentation

This document provides information about the SSH keys used for accessing the Obsidian notes repository.

## Repository SSH Keys

The following SSH key pair is used for accessing the Obsidian notes repository:

- **Private Key**: `obsidian-notes-asus`
- **Public Key**: `obsidian-notes-asus.pub`

These keys are stored in this vault and should be copied to the `~/.ssh/` directory on any machine that needs to access the repository.

## SSH Configuration

To use these keys with the repository, add the following configuration to your `~/.ssh/config` file:

```
Host ucanotes
  HostName github.com
  IdentityFile ~/.ssh/obsidian-notes-asus
```

## Key Management

1. **Backup**: Always keep a secure backup of these keys
2. **Permissions**: Ensure private key has restrictive permissions (600)
3. **Passphrase**: Use a strong passphrase to protect the private key
4. **Key Rotation**: Consider rotating keys every 6-12 months

## Related
- [[3-Resources]]
- [[3-Resources/Dotfiles/Dotfiles-Management]]
- [[3-Resources/Dotfiles/Dotfiles-Components]]
- [[3-Resources/README]]