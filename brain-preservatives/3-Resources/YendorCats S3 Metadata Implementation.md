---
# CORE METADATA
creation_date: 2025-04-27
modification_date: 2025-07-14
type: resource
status: active
priority: high
area_category: Software-Development
owner: Jordan
tags: [para/resources, guide, software-dev, s3, metadata, backblaze]

# RESOURCE CLASSIFICATION
source: Project documentation
difficulty: intermediate
resource_type: guide
url: ""
last_verified: 2025-07-14

# RELATIONSHIPS (Enhanced System)
related_projects: ["[[YendorCats Project Documentation]]"]
related_areas: ["[[Software-Development]]"]
related_people: ["[[Jordan]]"]
references: ["[[AWS S3 Documentation]]", "[[.NET 8 Documentation]]"]
inspired_by: ["[[S3 Best Practices Guide]]"]
---

# YendorCats S3 Metadata Implementation

## Overview
This document details the implementation of S3 metadata for cat images in the YendorCats project. The system uses S3 object metadata as the primary source of information, with filename parsing as a fallback mechanism. This approach provides flexibility and extensibility while maintaining backward compatibility.

## Key Points
- S3 object metadata is the primary source of cat information
- Filename parsing serves as a fallback mechanism
- Enhanced `IS3StorageService` interface with metadata support
- Updated `CatGalleryImage` model with additional properties
- Modified `CatGalleryController` to prioritize S3 metadata
- File uploader service stores comprehensive metadata in S3

## Implementation Details

### S3StorageService Enhancement

The `IS3StorageService` interface was extended to support metadata operations:

```csharp
public interface IS3StorageService
{
    // Existing methods
    Task<string> UploadFileAsync(Stream fileStream, string fileName, string contentType);
    Task DeleteFileAsync(string fileName);
    Task<string> GetPreSignedUrlAsync(string fileName, int expiryMinutes = 60);
    Task ConfigureCorsAsync();
    
    // New methods for metadata support
    Task<string> UploadFileWithMetadataAsync(Stream fileStream, string fileName, 
        string contentType, Dictionary<string, string> metadata);
    Task<Dictionary<string, string>> GetObjectMetadataAsync(string fileName);
}
```

The implementation of `GetObjectMetadataAsync` retrieves metadata from S3 objects:

```csharp
public async Task<Dictionary<string, string>> GetObjectMetadataAsync(string fileName)
{
    _logger.LogInformation("Getting metadata for file: {FileName}", fileName);

    try
    {
        // Create a request to get the object metadata
        var request = new GetObjectMetadataRequest
        {
            BucketName = _bucketName,
            Key = fileName
        };

        // Get the object metadata
        var response = await _s3Client.GetObjectMetadataAsync(request);

        // Convert the metadata to a dictionary
        var metadata = new Dictionary<string, string>();
        
        foreach (var key in response.Metadata.Keys)
        {
            metadata[key] = response.Metadata[key];
        }

        _logger.LogInformation("Retrieved metadata for file: {FileName}, MetadataCount: {MetadataCount}", 
            fileName, metadata.Count);

        return metadata;
    }
    catch (AmazonS3Exception ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
    {
        _logger.LogWarning("File not found when retrieving metadata: {FileName}", fileName);
        return new Dictionary<string, string>();
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Error retrieving metadata for file: {FileName}", fileName);
        throw;
    }
}
```

### CatGalleryImage Model Enhancement

The `CatGalleryImage` model was expanded to include additional properties:

```csharp
public class CatGalleryImage
{
    // Existing properties
    public string Id { get; set; } = string.Empty;
    public string CatName { get; set; } = string.Empty;
    public float Age { get; set; }
    public DateTime DateTaken { get; set; }
    public int OrderNumber { get; set; }
    public string ImageUrl { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    
    // New properties for enhanced metadata
    public string? Description { get; set; }
    public string? Color { get; set; }
    public string? Gender { get; set; }
    public string? Traits { get; set; }
    public string? Mother { get; set; }
    public string? Father { get; set; }
    
    // Additional metadata as key-value pairs
    public Dictionary<string, string> AdditionalMetadata { get; set; } = new();
    
    // Methods for creating CatGalleryImage objects
    public static CatGalleryImage? FromFilename(string filePath, string relativePath, string category);
    public static CatGalleryImage FromS3Metadata(Dictionary<string, string> metadata, 
        string relativePath, string category, CatGalleryImage? fallbackImage = null);
}
```

A new method `FromS3Metadata` was added to create a `CatGalleryImage` from S3 metadata:

```csharp
public static CatGalleryImage FromS3Metadata(
    Dictionary<string, string> metadata, 
    string relativePath, 
    string category,
    CatGalleryImage? fallbackImage = null)
{
    // Create a new image with default values or from fallback
    var image = fallbackImage ?? new CatGalleryImage
    {
        Id = Guid.NewGuid().ToString(),
        ImageUrl = relativePath,
        Category = category,
        DateTaken = DateTime.Now,
        OrderNumber = 1
    };
    
    // Try to extract standard metadata fields
    if (metadata.TryGetValue("name", out string? name) && !string.IsNullOrEmpty(name))
    {
        image.CatName = name;
    }
    
    if (metadata.TryGetValue("age", out string? ageStr) && 
        float.TryParse(ageStr, out float age))
    {
        image.Age = age;
    }
    
    if (metadata.TryGetValue("description", out string? description))
    {
        image.Description = description;
    }
    
    if (metadata.TryGetValue("color", out string? color))
    {
        image.Color = color;
    }
    
    if (metadata.TryGetValue("gender", out string? gender))
    {
        image.Gender = gender;
    }
    
    if (metadata.TryGetValue("traits", out string? traits))
    {
        image.Traits = traits;
    }
    
    if (metadata.TryGetValue("mother", out string? mother))
    {
        image.Mother = mother;
    }
    
    if (metadata.TryGetValue("father", out string? father))
    {
        image.Father = father;
    }
    
    // Add any additional metadata not covered by specific properties
    foreach (var kvp in metadata)
    {
        string key = kvp.Key.ToLowerInvariant();
        if (key != "name" && key != "age" && key != "description" && 
            key != "color" && key != "gender" && key != "traits" && 
            key != "mother" && key != "father" && key != "category")
        {
            image.AdditionalMetadata[kvp.Key] = kvp.Value;
        }
    }
    
    return image;
}
```

### CatGalleryController Update

The `CatGalleryController` was modified to use S3 metadata as the primary source of information:

```csharp
private async Task<List<CatGalleryImage>> ScanDirectoryForImagesAsync(string category)
{
    var result = new List<CatGalleryImage>();
    var imagesPath = Path.Combine(_webHostEnvironment.WebRootPath, "resources", category);
    
    _logger.LogInformation("Scanning directory: {ImagesPath}", imagesPath);
    
    if (!Directory.Exists(imagesPath))
    {
        _logger.LogWarning("Directory not found: {ImagesPath}", imagesPath);
        return result; // Return empty list if directory doesn't exist
    }

    var files = Directory.GetFiles(imagesPath)
        .Where(file => _validExtensions.Contains(Path.GetExtension(file).ToLower()))
        .ToList();
    
    foreach (var file in files)
    {
        var fileName = Path.GetFileName(file);
        var s3Key = $"resources/{category}/{fileName}";
        var relativePath = $"/{s3Key}";
        
        try
        {
            // First try to get metadata from S3
            var metadata = await _s3StorageService.GetObjectMetadataAsync(s3Key);
            
            if (metadata.Count > 0)
            {
                // Try to parse from filename as a fallback for missing fields
                var fallbackFromFilename = CatGalleryImage.FromFilename(file, relativePath, category);
                
                // Create image from S3 metadata with filename parsing as fallback
                var catImage = CatGalleryImage.FromS3Metadata(metadata, relativePath, category, fallbackFromFilename);
                result.Add(catImage);
            }
            else
            {
                // No S3 metadata found, try to parse from filename
                var catImage = CatGalleryImage.FromFilename(file, relativePath, category);
                
                if (catImage != null)
                {
                    result.Add(catImage);
                }
                else
                {
                    // Fallback for files that don't match the expected format
                    var fallbackImage = CreateFallbackImage(file, relativePath, category);
                    result.Add(fallbackImage);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing file: {FileName}", fileName);
            
            // Try to parse from filename as a fallback
            var catImage = CatGalleryImage.FromFilename(file, relativePath, category);
            
            if (catImage != null)
            {
                result.Add(catImage);
            }
            else
            {
                // Last resort fallback
                var fallbackImage = CreateFallbackImage(file, relativePath, category);
                result.Add(fallbackImage);
            }
        }
    }

    return result;
}
```

## Frontend Integration

The frontend JavaScript was updated to display all available metadata in the gallery modal:

```javascript
if (modalCaption) {
    // Build a comprehensive caption with all available metadata
    let captionHTML = `<h4>${item.title}</h4>`;
    
    // Add description if available
    if (item.description) {
        captionHTML += `<p class="description">${item.description}</p>`;
    }
    
    // Start metadata section
    captionHTML += '<div class="metadata-section">';
    
    // Add age if available
    if (item.age) {
        captionHTML += `<p><strong>Age:</strong> ${item.age} years</p>`;
    }
    
    // Add gender if available
    if (item.gender) {
        const genderText = item.gender === 'M' ? 'Male' : item.gender === 'F' ? 'Female' : item.gender;
        captionHTML += `<p><strong>Gender:</strong> ${genderText}</p>`;
    }
    
    // Add color if available
    if (item.color) {
        captionHTML += `<p><strong>Color/Pattern:</strong> ${item.color}</p>`;
    }
    
    // Add traits if available
    if (item.traits) {
        captionHTML += `<p><strong>Traits:</strong> ${item.traits}</p>`;
    }
    
    // Add parents if available
    if (item.mother || item.father) {
        let parentsText = '<strong>Parents:</strong> ';
        if (item.mother) parentsText += `Mother: ${item.mother}`;
        if (item.mother && item.father) parentsText += ', ';
        if (item.father) parentsText += `Father: ${item.father}`;
        captionHTML += `<p>${parentsText}</p>`;
    }
    
    // Close metadata section
    captionHTML += '</div>';
    
    modalCaption.innerHTML = captionHTML;
}
```

## Related Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  status as "Status",
  priority as "Priority"
FROM "1-Projects"
WHERE contains(file.content, "[[YendorCats S3 Metadata Implementation]]")
SORT priority ASC
```

## Related Areas
```dataview
TABLE WITHOUT ID
  file.link as "Area",
  area as "Category"
FROM "2-Areas"
WHERE contains(file.content, "[[YendorCats S3 Metadata Implementation]]") OR area = "Software-Development"
```

## Related Resources
```dataview
LIST
FROM "3-Resources"
WHERE contains(tags, "s3") OR contains(tags, "metadata") OR contains(tags, "backblaze")
LIMIT 5
```

## Quick Links
- [[YendorCats Project Documentation|Back to Project]]
- [[YendorCats File Uploader Service|File Uploader Documentation]]
- [[3-Resources|All Resources]]
