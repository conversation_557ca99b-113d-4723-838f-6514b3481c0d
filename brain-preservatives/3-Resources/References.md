---
creation_date: 2025-07-14
modification_date: 2025-07-14
type: index
aliases: [Reference Index, Reference List]
tags: [para/resources, index, references]
resource: References
---

# References

> A central place to collect and organize reference materials from various sources.

## Academic References

```dataview
TABLE 
  authors as "Authors",
  publication_date as "Date",
  source as "Source"
FROM "3-Resources"
WHERE contains(tags, "academic") OR contains(tags, "research") OR contains(tags, "paper")
SORT publication_date DESC
```

## Technical References

```dataview
TABLE 
  source as "Source",
  file.mtime as "Last Modified"
FROM "3-Resources"
WHERE contains(tags, "technical-reference") OR contains(tags, "api") OR contains(tags, "documentation")
SORT file.name ASC
```

## Web References

```dataview
TABLE 
  url as "URL",
  date_accessed as "Date Accessed"
FROM "3-Resources"
WHERE url
SORT file.name ASC
```

## Books

```dataview
TABLE 
  authors as "Author(s)",
  publication_date as "Published",
  isbn as "ISBN"
FROM "3-Resources"
WHERE contains(tags, "book")
SORT authors ASC
```

## Personal References

```dataview
TABLE 
  file.mtime as "Last Modified"
FROM "3-Resources"
WHERE contains(tags, "personal-reference")
SORT file.name ASC
```

## Citation Formats

### APA Style
```
Author, A. A. (Year of publication). Title of work: Capital letter also for subtitle. Publisher Name.
```

### MLA Style
```
Author. Title of Book. Publisher, Publication Date.
```

### Chicago Style
```
Last name, First name. Title of Book. Place of publication: Publisher, Year of publication.
```

## Related
- [[3-Resources]]
- [[3-Resources/Resource Index]]
- [[3-Resources/People MOC]]