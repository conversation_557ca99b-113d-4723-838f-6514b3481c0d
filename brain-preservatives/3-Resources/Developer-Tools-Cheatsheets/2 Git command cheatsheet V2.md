---
creation_date: 2025-04-16
modification_date: 2025-04-16
type: index
aliases:
  - Resources
tags:
  - para/resources
  - index
  - guide
  - guides
  - development
  - software
  - git
  - git
  - files
  - recovery
  - restore
---



## To list all files that have ever existed in a repository:

*This is a simplified variation of <PERSON><PERSON><PERSON>'s [solution](https://stackoverflow.com/a/543383/313192):*

```
git log --pretty=format: --name-status | cut -f2- | sort -u
```

**Edit:** Thanks to [<PERSON><PERSON><PERSON>](https://stackoverflow.com/questions/543346/list-all-the-files-that-ever-existed-in-a-git-repository#comment357659_543426) for teaching me a bit more in the comments, this version has a shorter pipeline and gives git more opportunity to get things right.

```
git log --pretty=format: --name-only --diff-filter=A | sort -u
```

a simpler command to show history of deleted items:
```bash
git log --diff-filter=D --summary
```